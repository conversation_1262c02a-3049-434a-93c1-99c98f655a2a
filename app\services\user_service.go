package services

import (
	"compliance-and-risk-management-backend/app/apperr"
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/ctxhelper"
	"context"
	"errors"
	"fmt"
	"time"
)

type (
	UserService interface {
		CountUsers(context.Context, string, *entities.QueryFilter) (int, error)
		CreateDefaultUser(context.Context) error
		FindByID(context.Context, int64) (*entities.User, error)
		LoginUser(context.Context, txns_db.TransactionsSQLOperations, *forms.UserLoginForm) (*entities.User, *entities.JwtToken, error)
		RegisterUser(context.Context, txns_db.TransactionsDB, *forms.UserRegistrationForm) (*entities.User, error)
		SetupAdmin(context.Context, txns_db.TransactionsSQLOperations) error
	}

	AppUserService struct {
		activityRepository        repos.ActivityRepository
		apiKeyRepository          repos.APIKeyRepository
		applicationRepository     repos.ApplicationRepository
		organizationRepository    repos.OrganizationRepository
		phoneNumberRepository     repos.PhoneNumberRepository
		sessionRepository         repos.SessionRepository
		storeRepository           repos.StoreRepository
		userApplicationRepository repos.UserApplicationRepository
		userCreditRepository      repos.UserCreditRepository
		userRepository            repos.UserRepository
	}
)

func NewUserService(
	activityRepository repos.ActivityRepository,
	apiKeyRepository repos.APIKeyRepository,
	applicationRepository repos.ApplicationRepository,
	organizationRepository repos.OrganizationRepository,
	phoneNumberRepository repos.PhoneNumberRepository,
	sessionRepository repos.SessionRepository,
	storeRepository repos.StoreRepository,
	userApplicationRepository repos.UserApplicationRepository,
	userCreditRepository repos.UserCreditRepository,
	userRepository repos.UserRepository,
) UserService {
	return &AppUserService{
		activityRepository:        activityRepository,
		apiKeyRepository:          apiKeyRepository,
		applicationRepository:     applicationRepository,
		organizationRepository:    organizationRepository,
		phoneNumberRepository:     phoneNumberRepository,
		sessionRepository:         sessionRepository,
		storeRepository:           storeRepository,
		userApplicationRepository: userApplicationRepository,
		userCreditRepository:      userCreditRepository,
		userRepository:            userRepository,
	}
}

func (s *AppUserService) CountUsers(
	ctx context.Context,
	UserUserID string,
	filter *entities.QueryFilter,
) (int, error) {

	userCount := s.userRepository.CountUsers(ctx, filter)
	return userCount, nil
}

func (s *AppUserService) CreateDefaultUser(
	ctx context.Context,
) error {

	filter := &entities.QueryFilter{}
	userCount := s.userRepository.CountUsers(ctx, filter)

	if userCount < 1 {
		// Create default user
		user := &entities.User{
			Email:     "<EMAIL>",
			FirstName: "ESD",
			LastName:  "Admin",
			Password:  "0866417b35a",
		}

		err := s.userRepository.Save(ctx, user)
		if err != nil {
			fmt.Printf("failed to save user, err=[%v]", err)
		}

	}
	return nil
}

func (s *AppUserService) FindByID(
	ctx context.Context,
	userID int64,
) (*entities.User, error) {

	user, err := s.userRepository.FindByID(ctx, userID)
	if err != nil {
		return user, err
	}

	return user, nil
}

func (s *AppUserService) LoginUser(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	form *forms.UserLoginForm,
) (*entities.User, *entities.JwtToken, error) {

	token := &entities.JwtToken{}

	user, err := s.userRepository.FindByEmail(ctx, operations, form.Email)
	if err != nil {
		return user, token, err
	}

	err = utils.ComparePassword(user.Password, form.Password)
	if err != nil {
		return nil, nil, errors.New("login failed")
	}

	err = s.loadUserData(ctx, operations, user)
	if err != nil {
		return nil, nil, errors.New("unable to load user data")
	}

	applicationType := ctxhelper.ApplicationType(ctx)

	application, err := s.applicationRepository.FindByApplicationTypeAndUser(
		ctx,
		operations,
		applicationType,
		user.ID,
	)
	if err != nil {
		return user, token, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find application with type=[%v] and user=[%v]",
			applicationType,
			user.ID,
		)
	}

	// Create session
	session := &entities.Session{
		ApplicationID:   application.ID,
		ApplicationType: applicationType,
		IPAddress:       "localhost",
		LastRefreshedAt: time.Now(),
		UserAgent:       "api",
		UserID:          user.ID,
	}

	err = s.sessionRepository.Save(ctx, operations, session)
	if err != nil {
		fmt.Printf("unable to create session, err=[%v]\n", err.Error())
		return user, token, err
	}

	token = utils.CreateTokenEndpointCustom(session, user)

	return user, token, nil
}

func (s *AppUserService) RegisterUser(
	ctx context.Context,
	transactionsDB txns_db.TransactionsDB,
	form *forms.UserRegistrationForm,
) (*entities.User, error) {

	user, err := s.userRepository.FindByEmail(ctx, transactionsDB, form.Email)
	if err != nil && !utils.IsErrNoRows(err) {
		fmt.Printf("user registration error=[%v]\n", err)
		return user, err
	}

	if !user.IsNew() {
		fmt.Printf("user=[%v] is not new, returning...\n", user.Email)
		return user, nil
	}

	// Save and retrieve phone number - phoneNumberRepository
	phoneNumber, err := utils.ParsePhoneNumber(utils.GetPhoneNumberString(&form.PhoneNumber))
	if err != nil {
		return user, err
	}

	existingPhoneNumber, err := s.phoneNumberRepository.SearchByPhoneNumber(ctx, transactionsDB, phoneNumber)
	if err != nil && !utils.IsErrNoRows(err) {
		return user, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to search for existing phone number while registering user.",
		)
	}

	defaultOrganization, err := s.organizationRepository.GetDefaultOrganization(ctx, transactionsDB)
	if err != nil {
		return user, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to find default organization",
		)
	}

	err = transactionsDB.InTransaction(ctx, func(ctx context.Context, operations txns_db.TransactionsSQLOperations) error {

		if existingPhoneNumber.IsNew() {
			err = s.phoneNumberRepository.Save(ctx, operations, &phoneNumber)
			if err != nil {
				return apperr.Wrap(
					err,
				).AddLogMessagef(
					"Failed to save when creating phone number for user",
				)
			}

			existingPhoneNumber.ID = phoneNumber.ID
		}

		hashedPassword, err := utils.GeneratePasswordHash([]byte(form.Password))
		if err != nil {
			return apperr.Wrap(
				err,
			).AddLogMessagef(
				"unable to hash user password",
			)
		}

		newAccountNumber, err := s.generateNewAccountNumber(ctx, operations)
		if err != nil {
			return apperr.Wrap(
				err,
			).AddLogMessagef(
				"unable to generate new account number",
			)
		}

		user.AccountNumber = newAccountNumber
		user.FirstName = form.FirstName
		user.LastName = form.LastName
		user.Email = form.Email
		user.NationalID = form.NationalID
		user.OrganizationID = defaultOrganization.ID
		user.Password = hashedPassword
		user.PhoneNumber = &phoneNumber
		user.PhoneNumberID = phoneNumber.ID

		// Save user
		err = s.userRepository.Save(ctx, user)
		if err != nil {
			fmt.Printf("failed to save user, err=[%v]", err)
		}

		// Setup user application
		userApplication := &entities.UserApplication{
			ApplicationID: 5,
			UserID:        user.ID,
		}

		err = s.userApplicationRepository.Save(ctx, operations, userApplication)
		if err != nil {
			fmt.Printf("error setting up user application, err=[%v]\n", err)
			return err
		}

		// Create default store for the user
		defaultStore := &entities.Store{
			Name:           user.FirstName + "'s Store",
			OrganizationID: user.OrganizationID,
			UserID:         user.ID,
			CountryCode:    "KE", // Default to Kenya
			Location:       "Default Location",
			Email:          user.Email,
			PhoneNumber:    form.PhoneNumber.Number,
			Website:        "",
			VAT:            "0",
			PIN:            "0",
		}

		err = s.storeRepository.Save(ctx, defaultStore)
		if err != nil {
			return apperr.Wrap(err).AddLogMessagef("Failed to create default store for user id=[%v]", user.ID)
		}

		// Automatically create API Key.
		apiKey := &entities.APIKey{
			APIKey:         utils.GenerateAPIKeyUUID(),
			Description:    fmt.Sprintf("%v API Key", user.FirstName),
			OrganizationID: user.OrganizationID,
			StoreID:        &defaultStore.ID,
			Store:          defaultStore,
			UserID:         user.ID,
		}

		err = s.apiKeyRepository.Save(ctx, apiKey)
		if err != nil {
			return apperr.Wrap(err).AddLogMessagef("Failed to create api key for user id=[%v]", user.ID)
		}

		// Automatically assign 5 credits.
		userCredits := &entities.UserCredit{
			UserID:        user.ID,
			CreditBalance: 5,
		}

		err = s.userCreditRepository.Save(ctx, operations, userCredits)
		if err != nil {
			return apperr.Wrap(err).AddLogMessagef("Failed to create user credits for user id=[%v]", user.ID)
		}

		// Send a welcome email to user.

		return err
	})

	if err != nil {
		return user, err
	}

	return user, nil
}

func (s *AppUserService) SetupAdmin(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
) error {

	filter := &entities.QueryFilter{}

	// Create default organization if not exists
	orgCount := s.organizationRepository.CountOrganizations(ctx, filter)
	if orgCount > 0 {
		return nil
	}

	defaultEmail := "<EMAIL>"
	defaultPassowrd, err := utils.GeneratePasswordHash([]byte("TejUmpck68"))
	if err != nil {
		fmt.Printf("error setting admin password, err=[%v]\n", err)
		return err
	}

	defaultOrg := &entities.Organization{
		Name:          "My Store",
		Address:       "Nairobi, Kenya",
		Branch:        "Main Branch - HQ",
		ContactPerson: "System Admin",
		DateFormat:    "YYYY-MM-dd",
		Email:         defaultEmail,
		Password:      defaultPassowrd,
	}

	err = s.organizationRepository.Save(ctx, defaultOrg)
	if err != nil {
		fmt.Printf("error creating default organization, err=[%v]\n", err)
		return err
	}

	// Create default admin user if not exists
	userCount := s.userRepository.CountUsers(ctx, filter)
	if userCount > 0 {
		return nil
	}

	admin := &entities.User{
		FirstName:      "System",
		LastName:       "Admin",
		Email:          defaultEmail,
		Password:       defaultPassowrd,
		OrganizationID: defaultOrg.ID,
	}

	err = s.userRepository.Save(ctx, admin)
	if err != nil {
		fmt.Printf("error setting up admin, err=[%v]\n", err)
		return err
	}

	// Setup admin application
	adminApplication := &entities.UserApplication{
		ApplicationID: 1,
		UserID:        admin.ID,
	}

	err = s.userApplicationRepository.Save(ctx, operations, adminApplication)
	if err != nil {
		fmt.Printf("error setting up admin application, err=[%v]\n", err)
		return err
	}

	// setup user application
	userApplication := &entities.UserApplication{
		ApplicationID: 5,
		UserID:        admin.ID,
	}

	err = s.userApplicationRepository.Save(ctx, operations, userApplication)
	if err != nil {
		fmt.Printf("error setting up user application, err=[%v]\n", err)
		return err
	}

	activity := entities.Activity{}
	activity.UserID = admin.ID
	activity.ActivityType = "ADMIN_SETUP"
	activity.Description = "Admin created successfully."

	err = s.activityRepository.CreateActivity(ctx, &activity)
	if err != nil {
		fmt.Printf("error saving activity, err=[%v]\n", err)
		return err
	}

	return nil
}

func (s *AppUserService) generateNewAccountNumber(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
) (string, error) {

	// Get the last user record entry from database
	lastUser, err := s.userRepository.GetLastUser(ctx, operations)

	utils.Log.Infof("lastUser err=[%v], lastUser=[%+v]", err, lastUser)

	if err != nil {

		utils.Log.Errorf("error getting last user, err=[%v]", err)
		utils.Log.Infof("utils.IsErrNoRows(err)=[%v]", utils.IsErrNoRows(err))

		if utils.IsErrNoRows(err) {
			// First user registration, set first account number
			utils.Log.Infof("first user registration, setting account number to RC01001")
			lastUser = &entities.User{AccountNumber: "RC01001"}
		} else {
			return "", err
		}
	}

	utils.Log.Infof("last user account number=[%v]", lastUser.AccountNumber)

	// Increment the account number by 1
	newAccountNumber, err := utils.IncrementAccountNumber(lastUser.AccountNumber)
	if err != nil {
		utils.Log.Errorf("error incrementing account number, err=[%v]", err)
		return "", err
	}

	// Return the account number
	return newAccountNumber, nil
}

func (s *AppUserService) loadUserData(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	user *entities.User,
) error {

	organization, err := s.organizationRepository.GetOrganizationByID(user.OrganizationID)
	if err != nil {
		return err
	}

	user.Organization = organization

	// Get user default store
	defaultStore, err := s.storeRepository.GetUserDefaultStore(user.ID)
	if err != nil && !utils.IsErrNoRows(err) {
		return err
	}

	user.DefaultStore = defaultStore

	// Load user registered stores - max 5
	paginationFilter := &entities.PaginationFilter{Offset: 0, Page: 1, Per: 5, Limit: 5}
	userStores, err := s.storeRepository.FilterStoresForUser(ctx, operations, user.ID, paginationFilter)
	if err != nil && utils.IsErrNoRows(err) {
		return err
	}

	user.Stores = userStores

	return nil
}
