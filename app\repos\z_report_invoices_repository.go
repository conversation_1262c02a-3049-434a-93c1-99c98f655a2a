package repos

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"compliance-and-risk-management-backend/app/entities"
	"strings"

	txns_db "compliance-and-risk-management-backend/app/database"
)

const (
	countZReportInvoicesSQL = `SELECT COUNT(*) AS count FROM z_report_invoices WHERE 1=1`

	insertZReportInvoiceSQL = `INSERT INTO z_report_invoices (z_report_id, invoice_id, created_at, updated_at) VALUES`

	insertSingleZReportInvoiceSQL = insertZReportInvoiceSQL + ` VALUES ($1, $2, $3, $4) RETURNING id`

	selectZReportInvoiceByIDSQL = selectZReportInvoiceSQL + ` WHERE id=$1`

	selectZReportInvoiceSQL = `SELECT id, z_report_id, invoice_id, created_at, updated_at FROM z_report_invoices`

	updateZReportInvoiceSQL = `UPDATE z_report_invoices SET is_synced=$1, updated_at=$2 WHERE id=$3`
)

type (
	ZReportInvoiceRepository interface {
		CountZReportInvoices(ctx context.Context, filter *entities.PaginationFilter) int
		FilterZReportInvoices(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter) ([]*entities.ZReportInvoice, error)
		GetZReportInvoiceByID(ctx context.Context, zreportID int64) (*entities.ZReportInvoice, error)
		Save(ctx context.Context, operations txns_db.TransactionsSQLOperations, zreport *entities.ZReportInvoice) error
		SaveMultiple(ctx context.Context, operations txns_db.TransactionsSQLOperations, zReportInvoices []*entities.ZReportInvoice) error
	}

	AppZReportInvoiceRepository struct {
		db *sql.DB
	}
)

func NewZReportInvoiceRepository(db *sql.DB) ZReportInvoiceRepository {
	return &AppZReportInvoiceRepository{db: db}
}

func (r *AppZReportInvoiceRepository) CountZReportInvoices(
	ctx context.Context,
	filter *entities.PaginationFilter,
) int {
	var count int
	query, args, _ := r.buildQueryFromFilter(countZReportInvoicesSQL, filter, []interface{}{}, 1)
	row := r.db.QueryRowContext(ctx, query, args...)
	row.Scan(&count)
	return count
}

func (r *AppZReportInvoiceRepository) FilterZReportInvoices(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) ([]*entities.ZReportInvoice, error) {

	zreports := make([]*entities.ZReportInvoice, 0)

	args := make([]interface{}, 0)
	query := selectZReportInvoiceSQL
	currentIndex := 1

	query, args, currentIndex = r.buildQueryFromFilter(query, filter, args, currentIndex)
	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return zreports, err
	}

	defer rows.Close()

	for rows.Next() {
		zreport, err := r.scanRowIntoZReportInvoice(rows)
		if err != nil {
			return zreports, err
		}

		zreports = append(zreports, zreport)
	}

	return zreports, rows.Err()
}

func (r *AppZReportInvoiceRepository) GetZReportInvoiceByID(
	ctx context.Context,
	zreportID int64,
) (*entities.ZReportInvoice, error) {
	row := r.db.QueryRow(selectZReportInvoiceByIDSQL, zreportID)
	return r.scanRowIntoZReportInvoice(row)
}

func (r *AppZReportInvoiceRepository) Save(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	zreport *entities.ZReportInvoice,
) error {

	zreport.Timestamps.Touch()
	var err error

	if zreport.IsNew() {
		res := operations.QueryRowContext(
			ctx,
			insertSingleZReportInvoiceSQL,
			zreport.CreatedAt,
			zreport.UpdatedAt,
		)

		err = res.Scan(&zreport.ID)

	} else {

		_, err = r.db.Exec(
			updateZReportInvoiceSQL,
			zreport.UpdatedAt,
			zreport.ID,
		)
	}

	if err != nil {
		log.Printf("error saving zreport, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppZReportInvoiceRepository) SaveMultiple(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	zReportInvoices []*entities.ZReportInvoice,
) error {

	args := make([]interface{}, len(zReportInvoices)*4)
	insertQuery := insertZReportInvoiceSQL
	values := make([]string, len(zReportInvoices))
	argsCurrentCount := 0

	for index, zReportInvoice := range zReportInvoices {
		zReportInvoice.Touch()
		args[argsCurrentCount] = zReportInvoice.ZReportID
		args[argsCurrentCount+1] = zReportInvoice.InvoiceID
		args[argsCurrentCount+2] = zReportInvoice.CreatedAt
		args[argsCurrentCount+3] = zReportInvoice.UpdatedAt

		values[index] = fmt.Sprintf("($%d,$%d,$%d,$%d)", argsCurrentCount+1, argsCurrentCount+2, argsCurrentCount+3, argsCurrentCount+4)
		argsCurrentCount += 4
	}

	insertQuery += strings.Join(values, ",")
	insertQuery += " ON CONFLICT (z_report_id, invoice_id) DO UPDATE SET updated_at=now() RETURNING id"

	rows, err := operations.QueryContext(ctx, insertQuery, args...)
	if err != nil {
		return err
	}

	defer rows.Close()

	rowCount := 0
	for rows.Next() {
		rows.Scan(&zReportInvoices[rowCount].ID)
		rowCount++
	}

	return rows.Err()
}

func (r *AppZReportInvoiceRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	return query, args, currentIndex
}

func (r *AppZReportInvoiceRepository) scanRowIntoZReportInvoice(
	rowScanner txns_db.RowScanner,
) (*entities.ZReportInvoice, error) {

	var zreport entities.ZReportInvoice

	err := rowScanner.Scan(
		&zreport.ID,
		&zreport.CreatedAt,
		&zreport.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning zreport,  err=[%v]\n", err.Error())
		return &zreport, err
	}

	return &zreport, nil
}
