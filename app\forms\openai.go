package forms

type (
	Tool struct {
		Type string `json:"type"`
	}

	CreateOpenAIAssistantForm struct {
		Instructions string `binding:"required" json:"instructions"`
		Model        string `binding:"required" json:"model"`
		Name         string `binding:"required" json:"name"`
		Tools        []Tool `binding:"required" json:"tools"`
	}

	UpdateOpenAIAssistantForm struct {
		Instructions string `json:"instructions"`
		Model        string `json:"model"`
		Name         string `json:"name"`
		Tools        []Tool `json:"tools"`
		// FileIDS      []*string `json:"file_ids"` // To implement after file uploads impl
	}

	CreateOpenAIMessageForm struct {
		Role    string `binding:"required" json:"role"`
		Content string `binding:"required" json:"content"`
	}

	CreateOpenAIThreadMessageFields struct {
		Role    string `json:"role"`
		Content string `json:"content"`
		// FileIDs []*string `json:"file_ids"` // To implement after file uploads impl
	}

	CreateOpenAIThreadMessageForm struct {
		Messages []CreateOpenAIThreadMessageFields `json:"messages"`
	}

	CreateOpenAIThreadRunForm struct {
		AsistantID string `binding:"required" json:"assistant_id"`
	}
)
