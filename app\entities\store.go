package entities

import "gopkg.in/guregu/null.v3"

type (
	Store struct {
		SequentialIdentifier
		AvatarURL         string        `json:"avatar_url"`
		CountryCode       string        `json:"country_code"`
		Email             string        `json:"email"`
		EtimsBranch       string        `json:"etims_branch"`
		EtimsCMCKey       string        `json:"etims_cmc_key"`
		EtimsDeviceId     string        `json:"etims_device_id"`
		EtimsDeviceSerial string        `json:"etims_device_serial"`
		EtimsEnvironment  string        `json:"etims_environment"` // sandbox or production
		EtimsIntrlKey     string        `json:"etims_intrl_key"`
		EtimsMrcNo        string        `json:"etims_mrc_no"`
		EtimsSdcId        string        `json:"etims_sdcid"`
		EtimsSignlKey     string        `json:"etims_sign_key"`
		IsLicensed        bool          `json:"is_licensed"`
		Location          string        `json:"location"`
		Name              string        `json:"name"`
		OrganizationID    int64         `json:"organization_id"`
		Organization      *Organization `json:"organization,omitempty"`
		PhoneNumber       string        `json:"phone_number"`
		PIN               string        `json:"pin"`
		VAT               string        `json:"vat"`
		Website           string        `json:"website"`
		UserID            int64         `json:"user_id"`
		DeletedAt         null.Time     `db:"deleted_at" json:"-"`
		Timestamps
	}

	StoreList struct {
		Stores     []*Store    `json:"stores"`
		Pagination *Pagination `json:"pagination"`
	}
)
