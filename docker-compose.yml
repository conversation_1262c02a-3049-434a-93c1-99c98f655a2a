version: "3"
services:
  optimus:
    image: struts-optimus:backend
    env_file: .env.prod
    container_name: struts-optimus-backend
    restart: always
    ports:
      - "9015:9015"

    build:
      context: .
      dockerfile: Dockerfile
      args:
        - MPESA_CONSUMER_KEY
        - MPESA_CONSUMER_SECRET

    environment:
      - MPESA_CONSUMER_KEY
      - MPESA_CONSUMER_SECRET
      - SMTP_EMAIL
      - SMTP_HOST
      - SMTP_PASSWORD

    extra_hosts:
      - "host.docker.internal:**********"

    command: >
      /bin/bash -c "echo DB URL = $DATABASE_URL"
