package ctxhelper

import (
	"context"

	"compliance-and-risk-management-backend/app/entities"
)

func ClientVersion(ctx context.Context) string {
	existing := ctx.Value(entities.ContextKeyClientVersion)
	if existing == nil {
		return ""
	}

	return existing.(string)
}

func WithClientVersion(ctx context.Context, clientVersion string) context.Context {
	return context.WithValue(ctx, entities.ContextKeyClientVersion, clientVersion)
}
