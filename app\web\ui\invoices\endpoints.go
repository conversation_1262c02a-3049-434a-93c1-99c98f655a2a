package invoices

import (
	"compliance-and-risk-management-backend/app/database"

	"compliance-and-risk-management-backend/app/services"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	appRouter *gin.Engine,
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	analyticsService services.AnalyticsService,
	invoiceService services.InvoiceService,
) {

	unauthenticatedAPI := routerGroup.Group("")
	{
		unauthenticatedAPI.GET("/invoices", filterInvoices(appRouter, transactionsDB, analyticsService, invoiceService))
		unauthenticatedAPI.GET("/invoices/:id", getInvoice(transactionsDB, analyticsService, invoiceService))
	}
}
