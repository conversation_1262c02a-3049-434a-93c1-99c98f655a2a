package ctxhelper

import (
	"context"

	"compliance-and-risk-management-backend/app/entities"
)

func TokenInfo(ctx context.Context) *entities.TokenInfo {
	existing := ctx.Value(entities.ContextKeyTokenInfo)
	if existing == nil {
		return &entities.TokenInfo{}
	}

	tokenInfo, ok := existing.(*entities.TokenInfo)
	if !ok {
		return &entities.TokenInfo{}
	}

	return tokenInfo
}

func WithTokenInfo(ctx context.Context, tokenInfo *entities.TokenInfo) context.Context {
	return context.WithValue(ctx, entities.ContextKeyTokenInfo, tokenInfo)
}
