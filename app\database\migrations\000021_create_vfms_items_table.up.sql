CREATE TABLE IF NOT EXISTS vfms_items (
    id                  BIGSERIAL                   PRIMARY KEY,    
    name                VA<PERSON><PERSON><PERSON>(255)                NOT NULL,
    vfms_id             BIGINT                      NOT NULL,     
    unit_measure        VARCHAR(255),   
    is_taxable          BOOLEAN                     NOT NULL,     
    created_at          TIMESTAMP WITH TIME ZONE    NOT NULL DEFAULT now(),
    updated_at          TIMESTAMP WITH TIME ZONE
);

CREATE UNIQUE INDEX IF NOT EXISTS vfms_items_name_uidx ON vfms_items(name);
