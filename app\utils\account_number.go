package utils

import (
	"fmt"
	"strconv"
	"strings"
)

// IncrementAccountNumber takes the last account number and returns the next one
func IncrementAccountNumber(lastAccountNumber string) (string, error) {
	const prefix = "RC01"
	const width = 3 // Number of digits, e.g., 001, 002

	if !strings.HasPrefix(lastAccountNumber, prefix) {
		return "", fmt.Errorf("invalid account number format")
	}

	numPart := strings.TrimPrefix(lastAccountNumber, prefix)
	num, err := strconv.Atoi(numPart)
	if err != nil {
		return "", fmt.Errorf("invalid numeric part: %w", err)
	}

	nextNum := num + 1
	nextAccountNumber := fmt.Sprintf("%s%0*d", prefix, width, nextNum)
	return nextAccountNumber, nil
}
