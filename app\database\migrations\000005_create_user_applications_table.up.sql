
CREATE TABLE IF NOT EXISTS user_applications (
  id               BIGSERIAL        PRIMARY KEY,
  user_id          BIGINT           NOT NULL REFERENCES users (id),
  application_id   BIGINT           NOT NULL REFERENCES applications (id),
  created_at       TIMESTAMPTZ      NOT NULL DEFAULT clock_timestamp(),
  updated_at       TIMESTAMPTZ      NOT NULL DEFAULT clock_timestamp()
);

CREATE UNIQUE INDEX user_applications_user_id_application_ic_uidx ON user_applications(user_id, application_id);
