package services

import (
	"context"
	"errors"
	"fmt"
	"compliance-and-risk-management-backend/app/apperr"
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/ctxhelper"
	"strings"
)

type APIKeyService interface {
	CreateAPIKey(ctx context.Context, operations database.TransactionsSQLOperations, form *forms.CreateAPIKeyForm) (*entities.APIKey, error)
	DeleteAPIKey(ctx context.Context, id int64, storeId int64) (*entities.APIKey, error)
	FilterAPIKeys(ctx context.Context, operations database.TransactionsSQLOperations, filter *entities.PaginationFilter) (*entities.APIKeyList, error)
	FindAPIKeyByID(ctx context.Context, apiKeyID int64, storeId int64) (*entities.<PERSON>Key, error)
	GetAPIKeyByKey(ctx context.Context, apiKey string) (*entities.APIKey, error)
	SaveAPIKey(ctx context.Context, operations database.TransactionsSQLOperations, apiKey *entities.APIKey) error
	UpdateAPIKey(ctx context.Context, transactionsDB *database.AppTransactionsDB, apiKeyID int64, storeId int64, form *forms.UpdateAPIKeyForm) (*entities.APIKey, error)
}

type AppAPIKeyService struct {
	apiKeyRepository repos.APIKeyRepository
	storeRepository  repos.StoreRepository
	userRepository   repos.UserRepository
}

func NewAPIKeyService(
	apiKeyRepo repos.APIKeyRepository,
	storeRepository repos.StoreRepository,
	userRepository repos.UserRepository,
) APIKeyService {
	return &AppAPIKeyService{
		apiKeyRepository: apiKeyRepo,
		storeRepository:  storeRepository,
		userRepository:   userRepository,
	}
}

func (s *AppAPIKeyService) CreateAPIKey(
	ctx context.Context,
	operations database.TransactionsSQLOperations,
	form *forms.CreateAPIKeyForm,
) (*entities.APIKey, error) {

	tokenInfo := ctxhelper.TokenInfo(ctx)

	user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	if err != nil {
		return &entities.APIKey{}, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find user by id=[%v]",
			tokenInfo.UserID,
		)
	}

	store, err := s.storeRepository.FindByID(ctx, form.StoreID)
	if err != nil {
		return &entities.APIKey{}, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find store by id=[%v]",
			form.StoreID,
		)
	}

	apiKey := &entities.APIKey{
		APIKey:         utils.GenerateAPIKeyUUID(),
		Description:    form.Description,
		OrganizationID: user.OrganizationID,
		StoreID:        &store.ID,
		Store:          store,
		UserID:         user.ID,
	}

	err = s.apiKeyRepository.Save(ctx, apiKey)
	if err != nil {
		return apiKey, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to create api key with description=[%v]",
			form.Description,
		)
	}

	return apiKey, nil
}

func (s *AppAPIKeyService) DeleteAPIKey(
	ctx context.Context,
	keyID int64,
	storeId int64,
) (*entities.APIKey, error) {

	apiKey := &entities.APIKey{}
	var err error

	apiKey, err = s.apiKeyRepository.FindByID(ctx, keyID, storeId)
	if err != nil {

		if utils.IsErrNoRows(err) {
			return apiKey, utils.NewErrorWithErrorCodeAndMessage(
				errors.New("apiKey not found"),
				utils.ErrorCodeResourceNotFound,
				"apiKey not found",
				"apiKey=[%v] not found",
				keyID,
			)
		}

		return apiKey, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find apiKey by id=[%v]",
			keyID,
		)
	}

	err = s.apiKeyRepository.Delete(ctx, apiKey)
	if err != nil {
		return apiKey, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to delete apiKey by id=[%v]",
			keyID,
		)
	}

	return apiKey, nil
}

func (s *AppAPIKeyService) FilterAPIKeys(
	ctx context.Context,
	operations database.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) (*entities.APIKeyList, error) {

	apiKeyList := &entities.APIKeyList{}

	tokenInfo := ctxhelper.TokenInfo(ctx)

	filter.StoreID = tokenInfo.StoreID

	count, err := s.apiKeyRepository.CountAPIKeys(ctx, tokenInfo.StoreID, filter)
	if err != nil {
		fmt.Printf("unable to count apiKeys, err=[%v]\n", err.Error())
		return apiKeyList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to count api keys for user=[%v]",
			tokenInfo.UserID,
		)
	}

	apiKeys, err := s.apiKeyRepository.FilterAPIKeys(ctx, operations, tokenInfo.StoreID, filter)
	if err != nil {
		fmt.Printf("unable to filter apiKeys, err=[%v]\n", err.Error())
		return apiKeyList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to filter api keys for user=[%v]",
			tokenInfo.UserID,
		)
	}

	storeIDs := make([]int64, len(apiKeys))
	for index, apiKey := range apiKeys {
		if apiKey.StoreID != nil {
			storeIDs[index] = *apiKey.StoreID
		}
	}

	stores, err := s.storeRepository.FindByIDs(ctx, operations, storeIDs)
	if err != nil {
		return apiKeyList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find stores for user=[%v]",
			tokenInfo.UserID,
		)
	}

	storeMap := make(map[int64]*entities.Store)
	for _, store := range stores {
		storeMap[store.ID] = store
	}

	for _, apiKey := range apiKeys {
		if apiKey.StoreID != nil {
			if _, exists := storeMap[*apiKey.StoreID]; exists {
				apiKey.Store = storeMap[*apiKey.StoreID]
			}
		}
	}

	apiKeyList.APIKeys = apiKeys

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	apiKeyList.Pagination = pagination

	return apiKeyList, nil
}

func (s *AppAPIKeyService) FindAPIKeyByID(
	ctx context.Context,
	keyID int64,
	storeId int64,
) (*entities.APIKey, error) {

	apiKey := &entities.APIKey{}
	var err error

	tokenInfo := ctxhelper.TokenInfo(ctx)

	user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	if err != nil {
		return &entities.APIKey{}, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find user by id=[%v]",
			tokenInfo.UserID,
		)
	}

	apiKey, err = s.apiKeyRepository.FindByID(ctx, keyID, storeId)
	if err != nil {

		if utils.IsErrNoRows(err) {
			return apiKey, utils.NewErrorWithErrorCodeAndMessage(
				errors.New("apiKey not found"),
				utils.ErrorCodeResourceNotFound,
				"apiKey not found",
				"apiKey=[%v] not found",
				keyID,
			)
		}

		return apiKey, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find apiKey by id=[%v]",
			keyID,
		)
	}

	if apiKey.UserID != user.ID {
		return &entities.APIKey{}, nil
	}

	if apiKey.StoreID != nil {
		store, err := s.storeRepository.FindByID(ctx, *apiKey.StoreID)
		if err != nil {
			return apiKey, apperr.Wrap(
				err,
			).AddLogMessagef(
				"Failed to find store by id=[%v]",
				*apiKey.StoreID,
			)
		}

		apiKey.Store = store
	}

	return apiKey, nil
}

func (s *AppAPIKeyService) GetAPIKeyByKey(
	ctx context.Context,
	key string,
) (*entities.APIKey, error) {

	tokenInfo := ctxhelper.TokenInfo(ctx)

	user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	if err != nil {
		return &entities.APIKey{}, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find user by id=[%v]",
			tokenInfo.UserID,
		)
	}

	apiKey, err := s.apiKeyRepository.FindByKey(ctx, key)
	if err != nil {
		return apiKey, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find apiKey by key=[%v]",
			key,
		)
	}

	if apiKey.UserID != user.ID {
		return &entities.APIKey{}, nil
	}

	if apiKey.StoreID != nil {
		store, err := s.storeRepository.FindByID(ctx, *apiKey.StoreID)
		if err != nil {
			return apiKey, apperr.Wrap(
				err,
			).AddLogMessagef(
				"Failed to find store by id=[%v]",
				*apiKey.StoreID,
			)
		}

		apiKey.Store = store
	}

	return apiKey, nil
}

func (s *AppAPIKeyService) SaveAPIKey(
	ctx context.Context,
	operations database.TransactionsSQLOperations,
	apiKey *entities.APIKey,
) error {

	err := s.apiKeyRepository.Save(ctx, apiKey)
	if err != nil {
		return apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to save apiKey=[%v]",
			apiKey.Description,
		)
	}

	return nil
}

func (s *AppAPIKeyService) UpdateAPIKey(
	ctx context.Context,
	transactionsDB *database.AppTransactionsDB,
	apiKeyID int64,
	storeId int64,
	form *forms.UpdateAPIKeyForm,
) (*entities.APIKey, error) {

	tokenInfo := ctxhelper.TokenInfo(ctx)

	user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	if err != nil {
		return &entities.APIKey{}, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find apiKey user by id=[%v]",
			tokenInfo.UserID,
		)
	}

	apiKey, err := s.apiKeyRepository.FindByID(ctx, apiKeyID, storeId)
	if err != nil {
		return apiKey, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find apiKey by id=[%v]",
			apiKeyID,
		)
	}

	if apiKey.UserID != user.ID {
		return &entities.APIKey{}, nil
	}

	store, err := s.storeRepository.FindByID(ctx, form.StoreID)
	if err != nil {
		return apiKey, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find store by id=[%v]",
			form.StoreID,
		)
	}

	apiKey.Description = strings.TrimSpace(form.Description)
	apiKey.StoreID = &store.ID
	apiKey.Store = store

	err = s.apiKeyRepository.Save(ctx, apiKey)
	if err != nil {
		return apiKey, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to update apiKey by id=[%v]",
			apiKeyID,
		)
	}

	return apiKey, nil
}
