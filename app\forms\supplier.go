package forms

type (
	CreateSupplierForm struct {
		Name          string `binding:"required" json:"name"`
		Contact<PERSON>erson string `json:"contact_person"`
		Email         string `json:"email"`
		PhoneNumber   string `json:"phone_number"`
	}

	UpdateSupplierForm struct {
		Name          string `binding:"required" json:"name"`
		Contact<PERSON>erson string `json:"contact_person"`
		Email         string `json:"email"`
		PhoneNumber   string `json:"phone_number"`
	}
)
