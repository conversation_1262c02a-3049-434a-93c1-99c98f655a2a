package services

import (
	"context"
	"fmt"
	"compliance-and-risk-management-backend/app/apperr"
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/providers"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/ctxhelper"

	"github.com/google/uuid"
)

type EtimsStockService interface {
	CreateEtimsStock(context.Context, txns_db.TransactionsDB, *forms.CreateEtimsStockItemsForm) (*entities.EtimsStock, error)
	FilterEtimsStock(context.Context, txns_db.TransactionsSQLOperations, *entities.PaginationFilter) (*entities.EtimsStockList, error)
	FindByID(context.Context, txns_db.TransactionsSQLOperations, int64) (*entities.EtimsStock, error)
}

type AppEtimsStockService struct {
	etimsVSCU                 providers.EtimsVSCU
	etimsStockRepository      repos.EtimsStockRepository
	etimsStockItemsRepository repos.EtimsStockItemRepository
	storeRepository           repos.StoreRepository
}

func NewEtimsStockService(
	etimsVSCU providers.EtimsVSCU,
	etimsStockRepository repos.EtimsStockRepository,
	etimsStockItemsRepository repos.EtimsStockItemRepository,
	storeRepository repos.StoreRepository,
) EtimsStockService {
	return &AppEtimsStockService{
		etimsVSCU:                 etimsVSCU,
		etimsStockRepository:      etimsStockRepository,
		etimsStockItemsRepository: etimsStockItemsRepository,
		storeRepository:           storeRepository,
	}
}

func (s *AppEtimsStockService) CreateEtimsStock(
	ctx context.Context,
	transactionsDB txns_db.TransactionsDB,
	form *forms.CreateEtimsStockItemsForm,
) (*entities.EtimsStock, error) {

	tokenInfo := ctxhelper.TokenInfo(ctx)

	store, err := s.storeRepository.FindByID(ctx, tokenInfo.StoreID)
	if err != nil {
		return &entities.EtimsStock{}, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find configured store for API Key=[%v]",
			tokenInfo.APIKeyID,
		)
	}

	fmt.Printf("store name=[%v], store pin=[%v], branch=[%v]\n", store.Name, store.PIN, store.EtimsBranch)

	etimsStockForm := &forms.CreateEtimsStockItemsForm{
		Tin:         store.PIN,
		BhfID:       store.EtimsBranch,
		ModrID:      "Admin",
		ModrNm:      "Admin",
		RegrID:      "Admin",
		RegrNm:      "Admin",
		SarNo:       form.SarNo,
		OrgSarNo:    form.OrgSarNo,
		RegTyCd:     form.RegTyCd,
		CustTin:     form.CustTin,
		CustNm:      form.CustNm,
		CustBhfID:   form.CustBhfID,
		SarTyCd:     form.SarTyCd,
		OcrnDt:      form.OcrnDt,
		TotItemCnt:  form.TotItemCnt,
		TotTaxblAmt: form.TotTaxblAmt,
		TotTaxAmt:   form.TotTaxAmt,
		TotAmt:      form.TotAmt,
		Remark:      form.Remark,
		ItemList:    form.ItemList,
	}

	etimsStock := &entities.EtimsStock{
		Tin:         store.PIN,
		BhfID:       store.EtimsBranch,
		RegrNm:      etimsStockForm.RegrNm,
		RegrID:      etimsStockForm.RegrID,
		ModrNm:      etimsStockForm.ModrNm,
		ModrID:      etimsStockForm.ModrID,
		SarNo:       form.SarNo,
		OrgSarNo:    form.OrgSarNo,
		RegTyCd:     form.RegTyCd,
		CustTin:     form.CustTin,
		CustNm:      form.CustNm,
		CustBhfID:   form.CustBhfID,
		SarTyCd:     form.SarTyCd,
		OcrnDt:      form.OcrnDt,
		TotItemCnt:  form.TotItemCnt,
		TotTaxblAmt: form.TotTaxblAmt,
		TotTaxAmt:   form.TotTaxAmt,
		TotAmt:      form.TotAmt,
		Remark:      form.Remark,
		UUID:        uuid.New().String(),
		StoreID:     store.ID,
	}

	err = transactionsDB.InTransaction(ctx, func(ctx context.Context, operations txns_db.TransactionsSQLOperations) error {

		// Save stock
		err = s.etimsStockRepository.Save(ctx, operations, etimsStock)
		if err != nil {
			fmt.Printf("failed to save etims stock, err=[%v] \n", err)
			return err
		}

		// Save stock items
		etimsStockItems := []*entities.EtimsStockItem{}

		for _, stockItem := range form.ItemList {

			etimsStockItem := &entities.EtimsStockItem{
				UUID:         uuid.New().String(),
				EtimsStockId: etimsStock.ID,
				ItemSeq:      stockItem.ItemSeq,
				ItemCd:       stockItem.ItemCd,
				ItemClsCd:    stockItem.ItemClsCd,
				ItemNm:       stockItem.ItemNm,
				Bcd:          stockItem.Bcd,
				PkgUnitCd:    stockItem.PkgUnitCd,
				Pkg:          stockItem.Pkg,
				QtyUnitCd:    stockItem.QtyUnitCd,
				Qty:          stockItem.Qty,
				ItemExprDt:   stockItem.ItemExprDt,
				Prc:          stockItem.Prc,
				SplyAmt:      stockItem.SplyAmt,
				TotDcAmt:     stockItem.TotDcAmt,
				TaxblAmt:     stockItem.TaxblAmt,
				TaxTyCd:      stockItem.TaxTyCd,
				TaxAmt:       stockItem.TaxAmt,
				TotAmt:       stockItem.TotAmt,
			}

			etimsStockItems = append(etimsStockItems, etimsStockItem)
		}

		err = s.etimsStockItemsRepository.SaveMultiple(ctx, operations, etimsStockItems)
		if err != nil {
			fmt.Printf("failed to save etims stock items, err=[%v] \n", err)
			return err
		}

		return err
	})

	if err != nil {
		return &entities.EtimsStock{}, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to save stock for sarNo=[%v]",
			form.SarNo,
		)
	}

	utils.PrintStructToConsole(etimsStockForm)

	// Post to ETIMS
	response, err := s.etimsVSCU.CreateStockItems(etimsStockForm)
	if err != nil {
		fmt.Printf("failed to save etims stock, err=[%v] \n", err)
		return etimsStock, err
	}

	fmt.Printf("response.ResultCd=[%v]\n", response.ResultCd)

	return etimsStock, nil
}

func (s *AppEtimsStockService) FilterEtimsStock(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) (*entities.EtimsStockList, error) {

	list := &entities.EtimsStockList{}

	count, err := s.etimsStockRepository.Count(ctx, filter)
	if err != nil {
		return list, err
	}

	stockList, err := s.etimsStockRepository.FilterEtimsStocks(ctx, operations, filter)
	if err != nil {
		return list, err
	}

	list.EtimsStock = stockList

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	list.Pagination = pagination

	return list, nil
}

func (s *AppEtimsStockService) FindByID(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	id int64,
) (*entities.EtimsStock, error) {

	tokenInfo := ctxhelper.TokenInfo(ctx)

	store, err := s.storeRepository.FindByID(ctx, tokenInfo.StoreID)
	if err != nil {
		return &entities.EtimsStock{}, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find configured store for API Key=[%v]",
			tokenInfo.APIKeyID,
		)
	}

	stock, err := s.etimsStockRepository.FindByID(ctx, id)
	if err != nil {
		return &entities.EtimsStock{}, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to find stock by id=[%v]",
			id,
		)
	}

	// TODO: Validate stock store id against tokenInfo store Id
	fmt.Printf("tokenInfo.StoreID=[%v], store.ID=[%v]\n", tokenInfo.StoreID, store.ID)

	// TODO: Return stock items list

	return stock, nil
}
