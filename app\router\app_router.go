package router

import (
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/services"
	"strings"

	"os"

	"github.com/gin-contrib/sessions"
	"github.com/gin-contrib/sessions/cookie"
	"github.com/gin-contrib/static"
	"github.com/gin-gonic/gin"

	"compliance-and-risk-management-backend/app/web/auth"
	esdWsRouter "compliance-and-risk-management-backend/app/web/router"
)

func SetupRouter(
	transactionsDB *database.AppTransactionsDB,
	sessionAuthenticator auth.SessionAuthenticator,
	activityRepository repos.ActivityRepository,
	apiKeyRepository repos.APIKeyRepository,
	apiKeyService services.APIKeyService,
	creditNoteService services.CreditNoteService,
	etimsItemService services.EtimsItemService,
	etimsStockService services.EtimsStockService,
	etimsVSCUService services.EtimsVSCUService,
	excelFileUploadService services.ExcelFileUploadService,
	userRepository repos.UserRepository,
	analyticsService services.AnalyticsService,
	categoryService services.CategoryService,
	currencyService services.CurrencyService,
	customerService services.CustomerService,
	incomeTrackingService services.IncomeTrackingService,
	invoiceService services.InvoiceService,
	itemService services.ItemService,
	paymentService services.PaymentService,
	paymentTransactionsService services.PaymentTransactionsService,
	phoneNumberHasheservice services.PhoneNumberHashService,
	receivingService services.ReceivingService,
	saleService services.SaleService,
	settingService services.SettingService,
	sessionService services.SessionService,
	storeRepository repos.StoreRepository,
	storeService services.StoreService,
	supplierService services.SupplierService,
	userCreditService services.UserCreditService,
	userService services.UserService,
) *gin.Engine {

	environment := os.Getenv("ENVIRONMENT")
	router := createAndSetUpRouter(sessionAuthenticator, environment)

	router.Static("/downloads", "./downloads")
	router.Static("/assets", "./assets")
	router.LoadHTMLGlob("templates/*.html")
	router.Use(static.Serve("/", static.LocalFile("./web", true)))
	router.NoRoute(func(ctx *gin.Context) {
		if !strings.HasPrefix(ctx.Request.RequestURI, "/api") {
			ctx.File("./web")
		}
	})

	esdWsRouter.AddStrutsOptimusEndpoints(
		router,
		transactionsDB,
		sessionAuthenticator,
		activityRepository,
		apiKeyRepository,
		apiKeyService,
		creditNoteService,
		etimsItemService,
		etimsStockService,
		etimsVSCUService,
		excelFileUploadService,
		userRepository,
		analyticsService,
		categoryService,
		currencyService,
		customerService,
		incomeTrackingService,
		invoiceService,
		itemService,
		paymentService,
		paymentTransactionsService,
		phoneNumberHasheservice,
		receivingService,
		saleService,
		settingService,
		sessionService,
		storeRepository,
		storeService,
		supplierService,
		userCreditService,
		userService,
	)

	return router
}

func createAndSetUpRouter(
	sessionAuthenticator auth.SessionAuthenticator,
	environment string,
) *gin.Engine {

	switch environment {
	case "dev":
		gin.SetMode(gin.DebugMode)
	case "development":
		gin.SetMode(gin.DebugMode)
	case "production":
		gin.SetMode(gin.ReleaseMode)
	case "stage":
		gin.SetMode(gin.ReleaseMode)
	default:
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.Default()

	router.Use(middleware.Secure())
	router.Use(middleware.GzipCompression())
	router.Use(middleware.CORSMiddleware())

	router.Use(middleware.SetRequestId())
	router.Use(middleware.HealthCheck())
	router.Use(middleware.SetupAppContext(sessionAuthenticator))

	store := cookie.NewStore([]byte("secret"))
	router.Use(sessions.Sessions("mysession", store))

	router.SetTrustedProxies(nil)

	return router
}
