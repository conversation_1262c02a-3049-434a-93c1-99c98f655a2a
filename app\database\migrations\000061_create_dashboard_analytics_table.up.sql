CREATE TABLE IF NOT EXISTS dashboard_analytics (
    id                       BIGSERIAL                   PRIMARY KEY, 
    store_id                 BIGINT                      NOT NULL REFERENCES stores(id),         
    user_id                  BIGINT                      NOT NULL REFERENCES users(id),         
    name                     BIGINT                      NOT NULL,
    count                    BIGINT                      NOT NULL,   
    deleted_at               TIMESTAMP,
    created_at               TIMESTAMP WITH TIME ZONE    NOT NULL DEFAULT now(),
    updated_at               TIMESTAMP WITH TIME ZONE
);

CREATE INDEX IF NOT EXISTS dashboard_analytics_store_id_idx ON dashboard_analytics(store_id);
CREATE INDEX IF NOT EXISTS dashboard_analytics_user_id_idx ON dashboard_analytics(user_id);

