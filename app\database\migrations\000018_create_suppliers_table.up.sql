CREATE TABLE IF NOT EXISTS suppliers (
    id                BIGSERIAL                   PRIMARY KEY,    
    name              <PERSON><PERSON><PERSON><PERSON>(150)                NOT NULL,    
    contact_person    VARCHAR(250)                NOT NULL,    
    email             VARCHAR(250),
    phone_number      VARCHAR(150),    
    organization_id   BIGINT                      NOT NULL REFERENCES organizations (id),
    deleted_at        TIMESTAMP,
    created_at        TIMESTAMP WITH TIME ZONE    DEFAULT now() NOT NULL, 
    updated_at        TIMESTAMP WITH TIME ZONE 
);

CREATE UNIQUE INDEX IF NOT EXISTS suppliers_name_organization_id_uidx ON suppliers(name, organization_id);
