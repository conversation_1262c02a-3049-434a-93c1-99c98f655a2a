package entities

import "gopkg.in/guregu/null.v3"

type (
	Organization struct {
		SequentialIdentifier
		Name          string       `json:"name"`
		Address       string       `json:"address"`
		Branch        string       `json:"branch"`
		Contact<PERSON>erson string       `json:"contact_person"`
		DateFormat    string       `json:"date_format"`
		Email         string       `json:"email"`
		PhoneNumberID int64        `json:"phone_number_id"`
		PhoneNumber   *PhoneNumber `json:"phone_number"`
		IsSynced      bool         `json:"is_synced"`
		Password      string       `json:"-"`
		DeletedAt     null.Time    `db:"deleted_at" json:"-"`
		Timestamps
	}

	OrganizationList struct {
		Organizations []*Organization `json:"organizations"`
		Pagination    Pagination      `json:"pagination"`
	}
)
