CREATE TABLE IF NOT EXISTS sessions (
  id                BIGSERIAL     PRIMARY KEY,
  user_id           BIGINT        NOT NULL REFERENCES users(id),
  application_id    BIGINT        NOT NULL REFERENCES applications(id),
  ip_address        VARCHAR(16)   NOT NULL DEFAULT '',
  user_agent        VARCHAR(1024) NOT NULL DEFAULT '',
  last_refreshed_at TIMESTAMPTZ   NOT NULL DEFAULT clock_timestamp(),
  deactivated_at    TIMESTAMPTZ,
  created_at        TIMESTAMPTZ   NOT NULL DEFAULT clock_timestamp(),
  updated_at        TIMESTAMPTZ   NOT NULL DEFAULT clock_timestamp()
);

CREATE INDEX IF NOT EXISTS sessions_user_application_idx ON sessions(user_id, application_id);
