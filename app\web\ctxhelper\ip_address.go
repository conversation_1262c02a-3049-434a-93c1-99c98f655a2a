package ctxhelper

import (
	"context"

	"compliance-and-risk-management-backend/app/entities"
)

func IPAddress(ctx context.Context) string {
	existing := ctx.Value(entities.ContextKeyIPAddress)
	if existing == nil {
		return ""
	}

	return existing.(string)
}

func WithIpAddress(ctx context.Context, ipAddress string) context.Context {
	return context.WithValue(ctx, entities.ContextKeyIPAddress, ipAddress)
}
