package entities

import (
	"database/sql/driver"
)

type ETIMSEnvironment string

const (
	ETIMSEnvironmentSandbox    ETIMSEnvironment = "sandbox"
	ETIMSEnvironmentProduction ETIMSEnvironment = "production"
)

func (s ETIMSEnvironment) IsValid() bool {
	return s == ETIMSEnvironmentSandbox || s == ETIMSEnvironmentProduction
}

// Scan implements the Scanner interface.
func (s *ETIMSEnvironment) Scan(value interface{}) error {
	*s = ETIMSEnvironment(string(value.([]uint8)))
	return nil
}

// Value implements the driver Valuer interface.
func (s ETIMSEnvironment) Value() (driver.Value, error) {
	return s.String(), nil
}

func (s ETIMSEnvironment) String() string {
	return string(s)
}
