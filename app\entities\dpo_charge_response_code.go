package entities

type DpoChargeTokenResponseCode string

const (
	TransactionCharged     DpoChargeTokenResponseCode = "000" // TransactionCharged Transaction charged
	TransactionAlreadyPaid DpoChargeTokenResponseCode = "200" // TransactionAlreadyPaid Transaction alreadyp aid
	TokenMissing           DpoChargeTokenResponseCode = "801" // TokenMissing Token missing
	InvalidToken           DpoChargeTokenResponseCode = "802" // InvalidToken Invalid token
	MissingRequestOrName   DpoChargeTokenResponseCode = "803" // MissingRequestOrName Missing request or name
	XMLError               DpoChargeTokenResponseCode = "804" // XMLError Xml error
	DataMismatch           DpoChargeTokenResponseCode = "902" // DataMismatch Data mismatch
	MissingMandatoryFields DpoChargeTokenResponseCode = "950" // MissingMandatoryFields Missing mandatory fields
	TransactionDenied      DpoChargeTokenResponseCode = "999" // TransactionDenied Transaction denied
)

func (v DpoChargeTokenResponseCode) Description() string {
	switch v {
	case TransactionCharged:
		return "Transaction charged"
	case TransactionAlreadyPaid:
		return "Transaction already paid"

	case TokenMissing:
		return "Request missing company token"

	case InvalidToken:
		return "Wrong CompanyToken"

	case MissingRequestOrName:
		return "No request or error in Request type name"

	case XMLError:
		return "Error in XML"

	case DataMismatch:
		return "Data mismatch in one of the fields – fieldname"

	case MissingMandatoryFields:
		return "Request missing mandatory fields – fieldname"

	case TransactionDenied:
		return "Transaction Declined - Explanation"
	default:
		return "Unknown"
	}
}

// IsError determines whether the card response is an error or not.
func (c *ChargeCreditCardResponse) IsError() bool {
	return c.Result != "000"
}
