package utils

import (
	"encoding/json"
	"reflect"
)

func CopyStruct(dst, src interface{}) error {
	dstVal := reflect.ValueOf(dst).Elem()
	srcVal := reflect.ValueOf(src).Elem()

	for i := 0; i < dstVal.NumField(); i++ {
		field := dstVal.Field(i)
		if field.CanSet() {
			field.Set(srcVal.Field(i))
		}
	}
	return nil
}

func ObjectToJSON(obj interface{}) (string, error) {
	jsonBytes, err := json.Marshal(obj)
	if err != nil {
		return "", err
	}
	return string(jsonBytes), nil
}
