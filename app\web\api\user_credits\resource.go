package user_credits

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/webutils"
)

func topUpUserCredits(
	transactionsDB *database.AppTransactionsDB,
	userCreditService services.UserCreditService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		// Get B2B Api Key from header request
		b2bAPIKey := ctx.Request.Header.Get("x-b2b-api-key")
		if b2bAPIKey == "" {
			ctx.JSON(500, gin.H{
				"message": "unable to process api key",
			})
			return
		}

		var form forms.UserCreditTopupForm
		err := ctx.Bind(&form)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind userCreditTopUp form while creating userCreditTopUp",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		userCreditTopUp, err := userCreditService.TopupUserCredits(ctx.Request.Context(), transactionsDB, b2bAPIKey, &form)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to save userCreditTopUp. Err=" + err.Error(),
			})
			return
		}

		ctx.JSON(http.StatusOK, userCreditTopUp)
	}
}
