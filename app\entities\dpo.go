package entities

import (
	"encoding/xml"
	"math/big"
	"net/http"
)

// Client struct represents a client and it's configuration for working with the DPO API.
// The client provides functions to initiate, verify, cancel and revoke payment tokens.
// The client uses a basic net/http http.Client.
type Client struct {
	Debug       bool   // Determines whether to use test or live url
	Token       string // Credentials key for the company
	Http        *http.Client
	UserAgent   string
	MaxAttempts int    // Maximum number of attempts per operation
	RedirectURL string // RedirectURL the url to redirect to when payment flow completes
	BackURL     string // BackURL is the url to redirect to when payment fails or is cancelled
}

// ChargeCreditCardRequest is a request to charge a users card directly.
type ChargeCreditCardRequest struct {
	XMLName          xml.Name      `xml:"API3G"`
	CompanyToken     string        `xml:"CompanyToken"`
	Request          string        `xml:"Request"`
	TransactionToken string        `xml:"TransactionToken"`
	CreditCardNumber string        `xml:"CreditCardNumber"`
	CreditCardExpiry string        `xml:"CreditCardExpiry"`
	CreditCardCVV    string        `xml:"CreditCardCVV"`
	CardHolderName   string        `xml:"CardHolderName"`
	ThreeD           ThreeDRequest `xml:"ThreeD"`
}

// ThreeDRequest request data for 3D systems
type ThreeDRequest struct {
	Enrolled    string `xml:"Enrolled"`
	Paresstatus string `xml:"Paresstatus"`
	Eci         string `xml:"Eci"`
	Xid         string `xml:"Xid"`
	Cavv        string `xml:"Cavv"`
	Signature   string `xml:"Signature"`
	Veres       string `xml:"Veres"`
	Pares       string `xml:"Pares"`
}

// ChargeCreditCardResponse response returned from after processing a credit card charge directly.
type ChargeCreditCardResponse struct {
	XMLName     xml.Name `xml:"API3G"`
	Result      string   `xml:"Result"`
	Explanation string   `xml:"ResultExplanation"`
	RedirectURL string   `xml:"RedirectUrl,omitempty"`
	BackURL     string   `xml:"BackUrl,omitempty"`
	DeclinedURL string   `xml:"declinedUrl,omitempty"`
}

// CreateTokenRequest is a request to create a token that will be used to process (i.e. initiate, complete, cancel, revoke) payments.
type CreateTokenRequest struct {
	XMLName      xml.Name               `xml:"API3G"`
	CompanyToken string                 `xml:"CompanyToken"`
	Request      string                 `xml:"Request"`
	Transaction  CreateTokenTransaction `xml:"Transaction"`
	Services     []Service              `xml:"Services>Service"`
}

// Service is a product or service that users can pay for through DPO
type Service struct {
	ServiceType        string `xml:"ServiceType"`
	ServiceDescription string `xml:"ServiceDescription"`
	ServiceDate        string `xml:"ServiceDate"`
}

// CreateTokenTransaction TODO: add docs
type CreateTokenTransaction struct {
	PaymentAmount     string `xml:"PaymentAmount"`
	PaymentCurrency   string `xml:"PaymentCurrency"`
	CompanyRef        string `xml:"CompanyRef"`
	CustomerAddress   string `xml:"customerAddress"`
	CustomerCity      string `xml:"customerCity"`
	CustomerCountry   string `xml:"customerCountry"`
	CustomerEmail     string `xml:"customerEmail"`
	CustomerFirstName string `xml:"customerFirstName"`
	CustomerLastName  string `xml:"customerLastName"`
	CustomerPhone     int    `xml:"customerPhone"`
	EmailTransaction  string `xml:"EmailTransaction"`
	RedirectURL       string `xml:"RedirectURL"`
	BackURL           string `xml:"BackURL"`
	CompanyRefUnique  int    `xml:"CompanyRefUnique"`
	PTL               string `xml:"PTL"`
	PTLtype           string `xml:"PTLtype"`
}

// CreateTokenResponse is returned after processing a CreateTokenRequest and depending on the Result may be an error response or not
type CreateTokenResponse struct {
	XMLName           xml.Name    `xml:"API3G"`
	Result            string      `xml:"Result"`
	ResultExplanation string      `xml:"ResultExplanation"`
	TransToken        string      `xml:"TransToken,omitempty"`
	TransRef          string      `xml:"TransRef,omitempty"`
	Allocations       Allocations `xml:"Allocations,omitempty"`
}

// Allocations collection of allocations
type Allocations struct {
	Allocation Allocation `xml:"Allocation"`
}

// Allocation an allocation as defined by DPO
type Allocation struct {
	AllocationID   string `xml:"AllocationID"`
	AllocationCode string `xml:"AllocationCode"`
}

type DPOPaymentToken struct {
	TransToken          string `json:"trans_token"`
	PaymentURL          string `json:"payment_url"`
	ResponseCode        string `json:"response_code"`
	ResponseDescription string `json:"response_description"`
}

// VerifyTokenRequest is a request to verify a token that was requested as a CreateTokenRequest
type VerifyTokenRequest struct {
	XMLName          xml.Name `xml:"API3G"`
	CompanyToken     string   `xml:"CompanyToken"`
	TransactionToken string   `xml:"TransactionToken"`
	Request          string   `xml:"Request"`
}

// VerifyTokenResponse is returned after processing a VerifyTokenRequet and depending on the .Result may be an error response or not
type VerifyTokenResponse struct {
	XMLName           xml.Name `xml:"API3G"`
	Result            string   `xml:"Result"`
	ResultExplanation string   `xml:"ResultExplanation"`
}

// CancelTokenRequest represents a request to cancel a previously created token.
type CancelTokenRequest struct {
	XMLName      xml.Name `xml:"API3G"`
	CompanyToken string   `xml:"CompanyToken"`
	Request      string   `xml:"Request"`
	Token        string   `xml:"TransactionToken"`
}

// CancelTokenResponse is the result of requesting a cancel token and depending on .Result may be an error or not.
type CancelTokenResponse struct {
	XMLName           xml.Name `xml:"API3G"`
	Result            string   `xml:"Result"`
	ResultExplanation string   `xml:"ResultExplanation"`
}

// RefundTokenRequest represents a request to initiate a refund
type RefundTokenRequest struct {
	XMLName        xml.Name  `xml:"API3G"`
	CompanyToken   string    `xml:"CompanyToken"`
	Request        string    `xml:"Request"`
	Token          string    `xml:"TransactionToken"`
	RefundAmount   big.Float `xml:"refundAmount"`   // RefundAmount Requested refund amount. (Mandatory)
	RefundDetails  string    `xml:"refundDetails"`  // RefundDetails Requested refund description. (Mandatory)
	RefundRef      string    `xml:"refundRef"`      // refundRef Refund reference.	(Optional)
	RefundApproval int8      `xml:"refundApproval"` // refundApproval In case it being sent, refund will be checked by a checker (Optional)
}

// RefundTokenResponse represents response from initiating a refund request.
type RefundTokenResponse struct {
	XMLName           xml.Name `xml:"API3G"`
	Result            string   `xml:"Result"`
	ResultExplanation string   `xml:"ResultExplanation"`
}
