package auth

import (
	"context"
	"errors"
	"net/http"
	"time"

	db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/utils"
)

const (
	tokenHeader = "X-STRUTSPOS-TOKEN"
)

type SessionAuthenticator interface {
	RefreshTokenFromRequest(ctx context.Context, usersDB db.TransactionsSQLOperations, tokenInfo *entities.TokenInfo, w http.ResponseWriter) (string, error)
	SetUserSessionInResponse(ctx context.Context, w http.ResponseWriter, user *entities.User, session *entities.Session) (string, error)
	TokenInfoFromRequest(req *http.Request) (*entities.TokenInfo, error)
	UserByID(ctx context.Context, usersDB db.TransactionsSQLOperations, userID int64) (*entities.User, error)
}

type AppSessionAuthenticator struct {
	jwtHandler        JWTHandler
	sessionRepository repos.SessionRepository
	userRepository    repos.UserRepository
}

func NewSessionAuthenticator(
	sessionRepository repos.SessionRepository,
	userRepository repos.UserRepository,
) SessionAuthenticator {
	return NewSessionAuthenticatorWithJWTHandler(
		NewJWTHandler(),
		sessionRepository,
		userRepository,
	)
}

func NewSessionAuthenticatorWithJWTHandler(
	jwtHandler JWTHandler,
	sessionRepository repos.SessionRepository,
	userRepository repos.UserRepository,
) SessionAuthenticator {
	return &AppSessionAuthenticator{
		jwtHandler:        jwtHandler,
		sessionRepository: sessionRepository,
		userRepository:    userRepository,
	}
}

func (a *AppSessionAuthenticator) RefreshTokenFromRequest(
	ctx context.Context,
	usersDB db.TransactionsSQLOperations,
	tokenInfo *entities.TokenInfo,
	w http.ResponseWriter,
) (string, error) {

	session, err := a.sessionRepository.FindByID(ctx, usersDB, tokenInfo.SessionID)
	if err != nil {
		return "", utils.NewDatabaseError(
			err,
			"Failed to find session by id=[%v]",
			tokenInfo.SessionID,
		)
	}

	if session.DeactivatedAt.Valid {
		return "", utils.NewErrorWithCode(
			errors.New("session expired"),
			utils.ErrorCodeSessionExpired,
			"Failed to refresh session by id=[%v]",
			tokenInfo.SessionID,
		)
	}

	user, err := a.userRepository.FindByID(ctx, session.UserID)
	if err != nil {
		return "", utils.NewDatabaseError(
			err,
			"Failed to find user by id=[%v]",
			session.UserID,
		)
	}

	// if !user.Status.IsActive() {
	// 	return "", utils.NewErrorWithCode(
	// 		errors.New("invalid status"),
	// 		utils.ErrorCodeInvalidUserStatus,
	// 		"Failed to refresh session by id=[%v] for user with id=[%v]",
	// 		tokenInfo.SessionID,
	// 		session.UserID,
	// 	)
	// }

	session.LastRefreshedAt = time.Now()

	err = a.sessionRepository.Save(ctx, usersDB, session)
	if err != nil {
		return "", utils.NewDatabaseError(
			err,
			"Failed to update last refreshed at timestamp for session=[%v]",
			session.ID,
		)
	}

	return a.SetUserSessionInResponse(ctx, w, user, session)
}

func (a *AppSessionAuthenticator) SetUserSessionInResponse(
	ctx context.Context,
	w http.ResponseWriter,
	user *entities.User,
	session *entities.Session,
) (string, error) {

	tokenValue, err := a.jwtHandler.CreateUserToken(user, session)
	if err != nil {
		return "", utils.NewError(
			err,
			"Failed to generate signed user token for user=[%v]",
			user.ID,
		).Notify()
	}

	w.Header().Set(tokenHeader, tokenValue)

	return tokenValue, nil
}

func (a *AppSessionAuthenticator) TokenInfoFromRequest(req *http.Request) (*entities.TokenInfo, error) {
	tokenValue := req.Header.Get(tokenHeader)
	return a.jwtHandler.TokenInfo(tokenValue)
}

func (a *AppSessionAuthenticator) UserByID(
	ctx context.Context,
	usersDB db.TransactionsSQLOperations,
	userID int64,
) (*entities.User, error) {

	user, err := a.userRepository.FindByID(ctx, userID)
	if err != nil {
		return user, utils.NewDatabaseError(
			err,
			"Failed to find user by id=[%v]",
			userID,
		)
	}

	return user, nil
}
