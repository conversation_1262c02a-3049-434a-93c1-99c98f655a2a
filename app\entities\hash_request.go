package entities

import "gopkg.in/guregu/null.v3"

type (
	HashRequest struct {
		SequentialIdentifier
		UserID        int64     `json:"user_id"`
		PhoneNumber   string    `json:"phone_number"`
		Hash          string    `json:"hash"`
		Status        string    `json:"status"`
		Description   string    `json:"description"`
		TransactionID int64     `json:"transaction_id"`
		DeletedAt     null.Time `db:"deleted_at" json:"-"`
		Timestamps
	}

	HashRequestList struct {
		HashRequests []*HashRequest `json:"hash_requests"`
		Pagination   *Pagination    `json:"pagination"`
	}
)
