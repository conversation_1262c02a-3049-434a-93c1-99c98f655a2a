package sales

import (
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/web/auth"

	"compliance-and-risk-management-backend/app/services"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	saleService services.SaleService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyActiveUser(transactionsDB, sessionAuthenticator, sessionService))
	{
		protectedAPI.POST("/sales", makeSale(transactionsDB, saleService))
		protectedAPI.GET("/sales", filterSales(transactionsDB, saleService))
		protectedAPI.GET("/store/sales", filterSalesForStore(transactionsDB, saleService))
		protectedAPI.GET("/sales/:id", getSale(transactionsDB, saleService))
		protectedAPI.PUT("/sales/:id", updateSale(transactionsDB, saleService))
		protectedAPI.GET("/sales/download", downloadSalesExcel(transactionsDB, saleService))
	}
}
