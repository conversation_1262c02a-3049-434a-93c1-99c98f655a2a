package apperr

import (
	"database/sql"
	"errors"
	"fmt"
	"net/http"
	"strings"
)

type Error struct {
	err            error // original error
	errorCode      ErrorCode
	errorMessage   string // Contains error message for error that can be displayed to user
	httpStatusCode int
	logMessages    []string // Contains the "stack" of error messages
	notify         bool     // if true, changes log level to error instead of warn
}

func Wrap(err error) *Error {
	return New(err, ErrorCodeRequestFailed)
}

func New(err error, errorCode ErrorCode) *Error {
	if errorCode == "" {
		errorCode = ErrorCodeRequestFailed
	}

	if err == nil {
		err = errors.New("nil error")
	}

	appError, ok := err.(*Error)
	if !ok {
		appError = &Error{
			err: err,
		}

		appError.SetErrorCode(errorCode)
		appError.AddLogMessage(err.Error())
	}

	return appError
}

func NewDatabaseError(err error) *Error {
	appError := Wrap(err)

	if IsErrNotFound(err) {
		appError.SetErrorCode(ErrorCodeResourceNotFound)
		appError.httpStatusCode = http.StatusNotFound
	} else {
		appError.SetErrorCode(ErrorCodeRequestFailed)
		appError.httpStatusCode = http.StatusInternalServerError
		appError.Notify()
	}

	return appError
}

func WrapNilableDatabaseError(err error) error {
	if err == nil {
		return nil
	}

	return NewDatabaseError(err)
}

func NewInvalidArgumentError(errorMessage string) *Error {
	return New(
		errors.New(errorMessage),
		ErrorCodeInvalidArgument,
	).SetErrorMessage(errorMessage)
}

func NewUnauthorizedError(errorMessage string) *Error {
	return New(
		errors.New(errorMessage),
		ErrorCodeUnauthorized,
	).SetErrorMessage(errorMessage)
}

func (e *Error) SetErrorMessage(message string) *Error {
	if len(message) > 0 {
		e.errorMessage = message
	}
	return e
}

func (e *Error) SetErrorMessagef(format string, args ...interface{}) *Error {
	return e.SetErrorMessage(fmt.Sprintf(format, args...))
}

func (e *Error) Error() string {
	if e.errorMessage != "" {
		return e.errorMessage
	}

	return e.err.Error()
}

func (e *Error) UseErrorLoglevel() bool {
	return e.notify
}

func (e *Error) Err() error {
	return e.err
}

func (e *Error) HttpStatus() int {

	if e.httpStatusCode != 0 {
		return e.httpStatusCode
	}

	mapHttpStatus, ok := statusCodesMap[e.errorCode]
	if !ok || mapHttpStatus == 0 {
		return http.StatusBadRequest
	}

	return mapHttpStatus
}

func (e *Error) JsonResponse() map[string]interface{} {
	if e.errorMessage == "" {
		e.errorMessage = "Failed to perform request. Please try again."
	}

	return map[string]interface{}{
		"error_code":    e.errorCode.String(),
		"error_message": e.errorMessage,
		"status_code":   e.HttpStatus(),
	}
}

func (e *Error) Notify() *Error {
	e.notify = true
	return e
}

func (e *Error) SetErrorCode(errorCode ErrorCode) *Error {
	e.errorCode = errorCode
	mapErrorMessage, ok := errorMessagesMap[errorCode]
	if !ok || mapErrorMessage == "" {
		e.errorMessage = e.Error()
	} else {
		e.errorMessage = mapErrorMessage
	}

	return e
}

func (e *Error) AddLogMessage(message string) *Error {
	e.logMessages = append([]string{message}, e.logMessages...)
	return e
}

func (e *Error) AddLogMessagef(format string, args ...interface{}) *Error {
	return e.AddLogMessage(fmt.Sprintf(format, args...))
}

func (e *Error) LogMessages() string {
	messages := []string{e.Err().Error()}

	if e.logMessages != nil && len(e.logMessages) > 0 {
		for _, logMessage := range e.logMessages {
			messages = append(messages, "\t"+logMessage)
		}
	}

	return strings.Join(messages, "\n")
}

func (e *Error) GetLogMessageStack() []string {
	return e.logMessages
}

func IsErrNotFound(err error) bool {
	appError := Wrap(err)
	return appError.Err().Error() == sql.ErrNoRows.Error()
}
