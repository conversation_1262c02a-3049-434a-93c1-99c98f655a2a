package entities

import "database/sql/driver"

const (
	TINCustomerIDType            CustomerIDTypeStr = "tin"
	NIDACustomerIDType           CustomerIDTypeStr = "nida"
	PassportCustomerIDType       CustomerIDTypeStr = "passport"
	DrivingLicenseCustomerIDType CustomerIDTypeStr = "driving_license"
	VotersIDCustomerIDType       CustomerIDTypeStr = "voters_id"
	NoneOfTheAbove               CustomerIDTypeStr = "none_of_the_above"
)

type CustomerIDTypeStr string

func (s *CustomerIDTypeStr) Scan(value interface{}) error {
	*s = CustomerIDTypeStr(string(value.([]uint8)))
	return nil
}

func (s CustomerIDTypeStr) Value() (driver.Value, error) {
	return s.String(), nil
}

func (s CustomerIDTypeStr) String() string {
	return string(s)
}
