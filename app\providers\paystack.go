package providers

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"

	"compliance-and-risk-management-backend/app/logger"

	"compliance-and-risk-management-backend/app/entities"
)

const (
	PAYSTACK_BASE_URL = "https://api.paystack.co"
)

type (
	Paystack interface {
		CreateCustomer(*entities.PaystackCustomer) (*entities.PaystackResponse, error)
		CreatePlan(*entities.PaystackPlan) (*entities.PaystackResponse, error)
		CreateSubscription(*entities.PaystackSubscription) (*entities.PaystackResponse, error)
		FetchPlanByCode(string) (*entities.PaystackResponse, error)
		FetchPlanByID(int64) (*entities.PaystackResponse, error)
		InitiateTransaction(*entities.PaystackTransaction) (*entities.PaystackResponse, error)
		ListPlans() (*entities.PaystackPlansResponse, error)
		ListSubscriptions() (*entities.PaystackSubscriptionList, error)
		UpdatePlan(string, *entities.PaystackPlan) (*entities.PaystackResponse, error)
		VerifyPayment(referenceNumber string) (*entities.PaystackResponse, error)
	}

	AppPaystack struct {
		paystackPrivateKey string
	}
)

func NewPaystack() Paystack {

	return NewPaystackWithCrerdentials(
		os.Getenv("PAYSTACK_PRIVATE_KEY"),
	)
}

func NewPaystackWithCrerdentials(
	paystackPrivateKey string,
) Paystack {

	return &AppPaystack{
		paystackPrivateKey: paystackPrivateKey,
	}
}

func (s *AppPaystack) CreateCustomer(
	customer *entities.PaystackCustomer,
) (*entities.PaystackResponse, error) {

	paystackResponse := &entities.PaystackResponse{}
	baseURL := fmt.Sprintf("%v/customer", PAYSTACK_BASE_URL)

	payload, err := json.Marshal(customer)
	if err != nil {
		logger.Errorf("failed to marshal paystack customer form payload, err=[%v]\n", err)
		return paystackResponse, err
	}

	req, err := http.NewRequest("POST", baseURL, bytes.NewBuffer(payload))
	if err != nil {
		logger.Errorf("error creating paystack customer request, err=[%v]\n", err)
		return paystackResponse, err
	}

	req.Header.Set("Authorization", "Bearer "+s.paystackPrivateKey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	res, err := client.Do(req)
	if err != nil {
		logger.Errorf("error sending paystack customer request, err=[%v]\n", err)
		return paystackResponse, err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.Errorf("failed to read customer creation response, err=[%v]\n", err)
		return paystackResponse, err
	}

	responseBody := string(body)
	json.Unmarshal([]byte(responseBody), paystackResponse)

	if !paystackResponse.Status {
		logger.Errorf("error creating customer=[%v], err=[%v]\n", customer.FirstName, paystackResponse.Message)
		return paystackResponse, errors.New(paystackResponse.Message)
	}

	return paystackResponse, nil
}

func (s *AppPaystack) CreatePlan(
	plan *entities.PaystackPlan,
) (*entities.PaystackResponse, error) {

	paystackResponse := &entities.PaystackResponse{}
	baseURL := fmt.Sprintf("%v/plan", PAYSTACK_BASE_URL)

	payload, err := json.Marshal(plan)
	if err != nil {
		logger.Errorf("failed to marshal paystack plan form payload, err=[%v]\n", err)
		return paystackResponse, err
	}

	req, err := http.NewRequest("POST", baseURL, bytes.NewBuffer(payload))
	if err != nil {
		logger.Errorf("error creating paystack plan request, err=[%v]\n", err)
		return paystackResponse, err
	}

	req.Header.Set("Authorization", "Bearer "+s.paystackPrivateKey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	res, err := client.Do(req)
	if err != nil {
		logger.Errorf("error sending paystack plan request, err=[%v]\n", err)
		return paystackResponse, err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.Errorf("failed to read paystack plan creeation response, err=[%v]\n", err)
		return paystackResponse, err
	}

	responseBody := string(body)
	json.Unmarshal([]byte(responseBody), paystackResponse)

	if !paystackResponse.Status {
		logger.Errorf("error creating plan=[%v], err=[%v]\n", plan.Name, paystackResponse.Message)
		return paystackResponse, errors.New(paystackResponse.Message)
	}

	return paystackResponse, nil
}

func (s *AppPaystack) CreateSubscription(
	subscription *entities.PaystackSubscription,
) (*entities.PaystackResponse, error) {

	paystackResponse := &entities.PaystackResponse{}
	baseURL := fmt.Sprintf("%v/subscription", PAYSTACK_BASE_URL)

	payload, err := json.Marshal(subscription)
	if err != nil {
		logger.Errorf("failed to marshal paystack subscription form payload, err=[%v]\n", err)
		return paystackResponse, err
	}

	req, err := http.NewRequest("POST", baseURL, bytes.NewBuffer(payload))
	if err != nil {
		logger.Errorf("error creating paystack subscription request, err=[%v]\n", err)
		return paystackResponse, err
	}

	req.Header.Set("Authorization", "Bearer "+s.paystackPrivateKey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	res, err := client.Do(req)
	if err != nil {
		logger.Errorf("error sending paystack subscription request, err=[%v]\n", err)
		return paystackResponse, err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.Errorf("failed to read paystack subscription response, err=[%v]\n", err)
		return paystackResponse, err
	}

	responseBody := string(body)
	json.Unmarshal([]byte(responseBody), paystackResponse)

	if !paystackResponse.Status {
		logger.Errorf("error creating subscription for customer=[%v] and plan=[%v], err=[%v]\n", subscription.Customer, subscription.Plan, paystackResponse.Message)

		switch paystackResponse.Message {
		case "This subscription is already in place.":
			return paystackResponse, nil
		}

		return paystackResponse, errors.New(paystackResponse.Message)
	}

	return paystackResponse, nil
}

func (s *AppPaystack) FetchPlanByCode(
	code string,
) (*entities.PaystackResponse, error) {

	paystackPlan := &entities.PaystackResponse{}
	baseURL := fmt.Sprintf("%v/plan/%v", PAYSTACK_BASE_URL, code)

	req, err := http.NewRequest("GET", baseURL, nil)
	if err != nil {
		logger.Errorf("error creating paystack plan request, err=[%v]\n", err)
		return paystackPlan, err
	}

	req.Header.Set("Authorization", "Bearer "+s.paystackPrivateKey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	res, err := client.Do(req)
	if err != nil {
		logger.Errorf("error sending paystack plan request, err=[%v]\n", err)
		return paystackPlan, err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.Errorf("failed to read paystack fetch plan by code response, err=[%v]\n", err)
		return paystackPlan, err
	}

	responseBody := string(body)
	json.Unmarshal([]byte(responseBody), paystackPlan)

	if !paystackPlan.Status {
		logger.Errorf("error fetching plan by code=[%v], err=[%v]\n", code, paystackPlan.Message)
		return paystackPlan, errors.New(paystackPlan.Message)
	}

	return paystackPlan, nil
}

func (s *AppPaystack) FetchPlanByID(
	id int64,
) (*entities.PaystackResponse, error) {

	paystackPlan := &entities.PaystackResponse{}
	baseURL := fmt.Sprintf("%v/plan/%v", PAYSTACK_BASE_URL, id)

	req, err := http.NewRequest("GET", baseURL, nil)
	if err != nil {
		logger.Errorf("error creating paystack plan request, err=[%v]\n", err)
		return paystackPlan, err
	}

	req.Header.Set("Authorization", "Bearer "+s.paystackPrivateKey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	res, err := client.Do(req)
	if err != nil {
		logger.Errorf("error sending paystack plan request, err=[%v]\n", err)
		return paystackPlan, err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.Errorf("failed to read fetch plan by id response, err=[%v]\n", err)
		return paystackPlan, err
	}

	responseBody := string(body)
	json.Unmarshal([]byte(responseBody), paystackPlan)

	if !paystackPlan.Status {
		logger.Errorf("error fetching plan by id=[%v], err=[%v]\n", id, paystackPlan.Message)
		return paystackPlan, errors.New(paystackPlan.Message)
	}

	return paystackPlan, nil
}

func (s *AppPaystack) InitiateTransaction(
	transaction *entities.PaystackTransaction,
) (*entities.PaystackResponse, error) {

	transactionResponse := &entities.PaystackResponse{}
	baseURL := fmt.Sprintf("%v/transaction/initialize", PAYSTACK_BASE_URL)

	payload, err := json.Marshal(transaction)
	if err != nil {
		logger.Errorf("failed to marshal paystack transaction form payload, err=[%v]\n", err)
		return transactionResponse, err
	}

	req, err := http.NewRequest("POST", baseURL, bytes.NewBuffer(payload))
	if err != nil {
		logger.Errorf("error creating paystack transaction request, err=[%v]\n", err)
		return transactionResponse, err
	}

	req.Header.Set("Authorization", "Bearer "+s.paystackPrivateKey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	res, err := client.Do(req)
	if err != nil {
		logger.Errorf("error sending paystack transaction request, err=[%v]\n", err)
		return transactionResponse, err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.Errorf("failed to read paystack payment initiation response, err=[%v]\n", err)
		return transactionResponse, err
	}

	responseBody := string(body)
	json.Unmarshal([]byte(responseBody), transactionResponse)

	if !transactionResponse.Status {
		logger.Errorf("error initiating payment for amount=[%v], err=[%v]\n", transaction.Amount, transactionResponse.Message)
		return transactionResponse, errors.New(transactionResponse.Message)
	}

	return transactionResponse, nil
}

func (s *AppPaystack) ListPlans() (*entities.PaystackPlansResponse, error) {

	paystackPlans := &entities.PaystackPlansResponse{}
	baseURL := fmt.Sprintf("%v/plan", PAYSTACK_BASE_URL)

	req, err := http.NewRequest("GET", baseURL, nil)
	if err != nil {
		logger.Errorf("error creating paystack plan request, err=[%v]\n", err)
		return paystackPlans, err
	}

	req.Header.Set("Authorization", "Bearer "+s.paystackPrivateKey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	res, err := client.Do(req)
	if err != nil {
		logger.Errorf("error sending paystack plan request, err=[%v]\n", err)
		return paystackPlans, err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.Errorf("failed to read paystack plans listing response, err=[%v]\n", err)
		return paystackPlans, err
	}

	responseBody := string(body)
	json.Unmarshal([]byte(responseBody), paystackPlans)

	if !paystackPlans.Status {
		logger.Errorf("error listing paystack plans, err=[%v]\n", paystackPlans.Message)
		return paystackPlans, errors.New(paystackPlans.Message)
	}

	return paystackPlans, nil
}

func (s *AppPaystack) ListSubscriptions() (*entities.PaystackSubscriptionList, error) {

	paystackSubscriptions := &entities.PaystackSubscriptionList{}
	baseURL := fmt.Sprintf("%v/subscription", PAYSTACK_BASE_URL)

	req, err := http.NewRequest("GET", baseURL, nil)
	if err != nil {
		logger.Errorf("error creating paystack subscriptions request, err=[%v]\n", err)
		return paystackSubscriptions, err
	}

	req.Header.Set("Authorization", "Bearer "+s.paystackPrivateKey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	res, err := client.Do(req)
	if err != nil {
		logger.Errorf("error sending paystack subscriptions listing request, err=[%v]\n", err)
		return paystackSubscriptions, err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.Errorf("failed to read paystack subscriptions listing response, err=[%v]\n", err)
		return paystackSubscriptions, err
	}

	responseBody := string(body)
	json.Unmarshal([]byte(responseBody), paystackSubscriptions)

	if !paystackSubscriptions.Status {
		logger.Errorf("error listing paystack subscriptions, err=[%v]\n", paystackSubscriptions.Message)
		return paystackSubscriptions, errors.New(paystackSubscriptions.Message)
	}

	return paystackSubscriptions, nil
}

func (s *AppPaystack) UpdatePlan(
	paystackPlanCode string,
	plan *entities.PaystackPlan,
) (*entities.PaystackResponse, error) {

	paystackResponse := &entities.PaystackResponse{}
	baseURL := fmt.Sprintf("%v/plan/%v", PAYSTACK_BASE_URL, paystackPlanCode)

	payload, err := json.Marshal(plan)
	if err != nil {
		logger.Errorf("failed to marshal paystack plan form payload, err=[%v]\n", err)
		return paystackResponse, err
	}

	req, err := http.NewRequest("PUT", baseURL, bytes.NewBuffer(payload))
	if err != nil {
		logger.Errorf("error updating paystack plan request, err=[%v]\n", err)
		return paystackResponse, err
	}

	req.Header.Set("Authorization", "Bearer "+s.paystackPrivateKey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	res, err := client.Do(req)
	if err != nil {
		logger.Errorf("error sending paystack plan request, err=[%v]\n", err)
		return paystackResponse, err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.Errorf("failed to read paystack plan creeation response, err=[%v]\n", err)
		return paystackResponse, err
	}

	responseBody := string(body)
	json.Unmarshal([]byte(responseBody), paystackResponse)

	if !paystackResponse.Status {
		logger.Errorf("error updating plan=[%v], err=[%v]\n", plan.Name, paystackResponse.Message)
		return paystackResponse, errors.New(paystackResponse.Message)
	}

	return paystackResponse, nil
}

func (s *AppPaystack) VerifyPayment(
	reference string,
) (*entities.PaystackResponse, error) {

	validationResponse := &entities.PaystackResponse{}
	url := fmt.Sprintf("%s/transaction/verify/%s", PAYSTACK_BASE_URL, reference)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		fmt.Println("Error creating request:", err)
		return validationResponse, err
	}

	req.Header.Set("Authorization", "Bearer "+s.paystackPrivateKey)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Println("Error sending request:", err)
		return validationResponse, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Println("Error reading response:", err)
		return validationResponse, err
	}

	responseBody := string(body)
	json.Unmarshal([]byte(responseBody), validationResponse)

	if !validationResponse.Status {
		logger.Errorf("error verifying paystack payment with reference=[%v], err=[%v]\n", reference, validationResponse.Message)
		return validationResponse, errors.New(validationResponse.Message)
	}

	return validationResponse, nil
}
