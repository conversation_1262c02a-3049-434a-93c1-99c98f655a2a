package providers

import (
	"encoding/json"
	"os"
	"compliance-and-risk-management-backend/app/utils"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/lambda"
)

type (
	Lambda interface {
		InvokeSync(string, interface{}) (*lambda.InvokeOutput, error)
		InvokeAsync(string, interface{}) error
	}

	AppLambda struct {
		client *lambda.Lambda
	}
)

func NewLambda() Lambda {
	return NewLambdaWithCredentials(
		os.Getenv("AWS_REGION"),
		os.<PERSON>env("AWS_ACCESS_KEY_ID"),
		os.Getenv("AWS_SECRET_ACCESS_KEY"),
		os.Getenv("ENVIRONMENT"),
	)
}

func NewLambdaWithCredentials(
	region,
	accessKeyId,
	secretAccessKey,
	environment string,
) Lambda {
	config := &aws.Config{
		Region: aws.String(region),
		Credentials: credentials.NewStaticCredentials(
			accessKeyId,
			secretAccessKey,
			"",
		),
	}

	if isUsingLocalstack(environment) {
		config.Endpoint = aws.String(os.Getenv("LOCALSTACK_ENDPOINT"))
		config.S3ForcePathStyle = aws.Bool(true)
	}

	sess := session.Must(session.NewSession())

	return &AppLambda{client: lambda.New(sess)}
}

func (svc *AppLambda) invoke(funcName string, payload interface{}, async bool) (*lambda.InvokeOutput, error) {
	requestInput, err := json.Marshal(payload)
	if err != nil {
		return nil, utils.NewError(
			err,
			"Error marshalling lambda function payload",
		)
	}

	invokeInput := &lambda.InvokeInput{
		FunctionName: aws.String(funcName),
		Payload:      requestInput,
	}

	if async {
		invokeInput.InvocationType = aws.String("Event")
	}

	return svc.client.Invoke(&lambda.InvokeInput{FunctionName: aws.String(funcName), Payload: requestInput})
}

func (svc *AppLambda) InvokeSync(funcName string, payload interface{}) (*lambda.InvokeOutput, error) {
	resp, err := svc.invoke(funcName, payload, false)
	if err != nil {
		return resp, utils.NewError(
			err,
			"Error invoking lambda function:[%v] in sync mode",
			funcName,
		)
	}

	return resp, nil
}

func (svc *AppLambda) InvokeAsync(funcName string, payload interface{}) error {
	_, err := svc.invoke(funcName, payload, true)
	if err != nil {
		return utils.NewError(
			err,
			"Error invoking lambda function:[%v] in async mode",
			funcName,
		)
	}

	return nil
}
