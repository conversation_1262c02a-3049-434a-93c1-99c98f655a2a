package providers

import (
	"bytes"
	"encoding/xml"
	"fmt"
	"io"
	"log"
	"math/big"
	"net/http"
	"os"
	"strings"
	"time"

	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/utils"
)

const (
	defaultUA = "africasasa: https://africasasa.com/"
)

type (
	DPO interface {
		CancelToken(tokenStr string) (*entities.CancelTokenResponse, error)
		ChargeCreditCard(form *forms.DPOPaymentRequestForm) (*entities.DPOPaymentToken, error)
		CreateToken(token *entities.CreateTokenRequest) (*entities.CreateTokenResponse, error)
		GeneratePaymentToken(form *forms.DPOPaymentRequestForm) (*entities.DPOPaymentToken, error)
		RefundToken(tokenStr string, form *forms.DPOPaymentRefundForm) (*entities.RefundTokenResponse, error)
		VerifyPayment(trxToken string) (*entities.VerifyTokenResponse, error)
	}

	AppDPO struct {
		dpoAPIURL   string
		dpoPayURL   string
		dpoToken    string
		httpClient  *http.Client
		maxAttempts int
		userAgent   string
	}
)

func NewDPO() DPO {
	return NewDPOWithCrerdentials(
		os.Getenv("DPO_API_URL"),
		os.Getenv("DPO_PAY_URL"),
		os.Getenv("DPO_TOKEN"),
	)
}

func NewDPOWithCrerdentials(
	dpoAPIURL string,
	dpoPayURL string,
	dpoToken string,
) DPO {
	return &AppDPO{
		dpoAPIURL: dpoAPIURL,
		dpoPayURL: dpoPayURL,
		dpoToken:  dpoToken,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		maxAttempts: 5,
		userAgent:   defaultUA,
	}
}

// CancelToken initiates token cancellations
func (p *AppDPO) CancelToken(tokenStr string) (*entities.CancelTokenResponse, error) {
	cancelRequest := &entities.CancelTokenRequest{
		Request:      "cancelToken",
		CompanyToken: p.dpoToken,
		Token:        tokenStr,
	}

	var xmlData []byte
	var err error

	xmlData, err = utils.XMLMarshalWithHeader(cancelRequest)
	if err != nil {
		return nil, fmt.Errorf("failed to form XML request: %s got: %v", string(xmlData), err)
	}

	r := bytes.NewReader(xmlData)
	var created = false

	for i := 0; !created && i < p.maxAttempts; i++ {
		req, err := http.NewRequest("POST", p.dpoAPIURL, r)
		if err != nil {
			return nil, err
		}
		req.Header.Add("User-Agent", defaultUA)
		req.Header.Add("Content-Type", "application/xml")
		req.Header.Add("Cache-control", "no-cache")

		resp, err := p.httpClient.Do(req)
		if err != nil {
			return nil, err
		}

		bodyData, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, fmt.Errorf("failed to read body: %s got: %v", string(bodyData), err)
		}

		var cancelTokenResponse entities.CancelTokenResponse
		if resp.StatusCode == http.StatusOK {
			err = xml.Unmarshal(bodyData, &cancelTokenResponse)
			if err != nil {
				return nil, fmt.Errorf("failed unmarshal response: %v", err)
			}

			switch cancelTokenResponse.Result {
			case "000":
				return &cancelTokenResponse, nil
			case "999", "804", "950":
			default:
				return &cancelTokenResponse, fmt.Errorf("dpo error: %s", cancelTokenResponse.ResultExplanation)
			}
		} else if resp.StatusCode >= 400 && resp.StatusCode < 500 {
			return nil, fmt.Errorf("invalid response code:%d body: %s", resp.StatusCode, string(bodyData))
		}
	}

	return nil, fmt.Errorf("failed to process request after %d attempts", p.maxAttempts)
}

// ChargeCreditCard is used for charging a card directly.
func (p *AppDPO) ChargeCreditCard(
	form *forms.DPOPaymentRequestForm,
) (*entities.DPOPaymentToken, error) {

	paymentToken, err := p.GeneratePaymentToken(form)
	if err != nil {
		log.Printf("failed to generate payment request token, err=[%v]\n", err)
		return paymentToken, nil
	}

	creditCardForm := form.CreditCardForm

	cardRequest := &entities.ChargeCreditCardRequest{
		CompanyToken:     p.dpoToken,
		Request:          "chargeTokenCreditCard",
		TransactionToken: paymentToken.TransToken,
		CreditCardNumber: creditCardForm.CardNumber,
		// The API doesn't accept  an expiry with MM/YY it requires MMYY
		CreditCardExpiry: strings.ReplaceAll(creditCardForm.CardExpiry, "/", ""),
		CreditCardCVV:    creditCardForm.CVV,
		CardHolderName:   creditCardForm.CardHolderName,
		ThreeD: entities.ThreeDRequest{
			Enrolled:    "Y",
			Paresstatus: "Y",
			Eci:         "05",
			Xid:         "",
			Cavv:        "",
			Signature:   "_",
			Veres:       "AUTHENTICATION_SUCCESSFUL",
			Pares:       "",
		},
	}

	var xmlData []byte

	xmlData, err = utils.XMLMarshalWithHeader(cardRequest)
	if err != nil {
		return nil, fmt.Errorf("failed to form XML request: %s got: %v", string(xmlData), err)
	}

	r := bytes.NewReader(xmlData)
	req, err := http.NewRequest("POST", p.dpoAPIURL, r)
	if err != nil {
		return nil, err
	}
	req.Header.Add("User-Agent", defaultUA)
	req.Header.Add("Content-Type", "application/xml")
	req.Header.Add("Cache-control", "no-cache")

	resp, err := p.httpClient.Do(req)
	if err != nil {
		return nil, err
	}

	bodyData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read body: %s got: %v", string(bodyData), err)
	}

	var chargeCardResponse entities.ChargeCreditCardResponse
	if resp.StatusCode == http.StatusOK {
		err = xml.Unmarshal(bodyData, &chargeCardResponse)
		if err != nil {
			return nil, fmt.Errorf("failed unmarshal response: %v", err)
		}
		if chargeCardResponse.IsError() {
			return nil, fmt.Errorf("failed to charge card: %s", chargeCardResponse.Explanation)
		}

		paymentToken.ResponseCode = chargeCardResponse.Result
		paymentToken.ResponseDescription = chargeCardResponse.Explanation

		return paymentToken, nil
	}

	return paymentToken, fmt.Errorf("invalid response code:%d body: %s", resp.StatusCode, string(bodyData))
}

// CreateToken creates a token that can be used to perform payments. This is the first step in the payment flow with DPO
// Once the token is created it must be verified using client.VerifyToken
func (p *AppDPO) CreateToken(tokenRequest *entities.CreateTokenRequest) (*entities.CreateTokenResponse, error) {

	tokenResponse := &entities.CreateTokenResponse{}
	var xmlData []byte
	var err error

	xmlData, err = utils.XMLMarshalWithHeader(tokenRequest)
	if err != nil {
		return tokenResponse, fmt.Errorf("failed to form XML request: %s got: %v", string(xmlData), err)
	}

	r := bytes.NewReader(xmlData)
	req, err := http.NewRequest("POST", p.dpoAPIURL, r)
	if err != nil {
		return tokenResponse, err
	}

	req.Header.Add("User-Agent", defaultUA)
	req.Header.Add("Content-Type", "application/xml")
	req.Header.Add("Cache-control", "no-cache")

	resp, err := p.httpClient.Do(req)
	if err != nil {
		return tokenResponse, err
	}

	bodyData, err := io.ReadAll(resp.Body)
	if err != nil {
		return tokenResponse, fmt.Errorf("failed to read body: %s got: %v", string(bodyData), err)
	}

	if resp.StatusCode == http.StatusOK {
		err = xml.Unmarshal(bodyData, &tokenResponse)
		if err != nil {
			return tokenResponse, fmt.Errorf("failed unmarshal response: %v", err)
		}

		if tokenResponse.Result != "000" {
			return nil, fmt.Errorf("failed to charge card: %s", tokenResponse.ResultExplanation)
		}
		return tokenResponse, nil
	}

	return nil, fmt.Errorf("invalid response code:%d body: %s", resp.StatusCode, string(bodyData))
}

// RefundToken initiates token refunds - NOT YET IMPLEMENTED
func (p *AppDPO) RefundToken(tokenStr string, form *forms.DPOPaymentRefundForm) (*entities.RefundTokenResponse, error) {
	refundApproval := 0
	if form.RequiresApproval {
		refundApproval = 1
	}

	refundRequest := &entities.RefundTokenRequest{
		CompanyToken:   p.dpoToken,
		Request:        "refundToken",
		Token:          tokenStr,
		RefundAmount:   big.Float{},
		RefundDetails:  form.Description,
		RefundRef:      form.RefundRef,
		RefundApproval: int8(refundApproval),
	}

	var xmlData []byte
	var err error

	xmlData, err = utils.XMLMarshalWithHeader(refundRequest)
	if err != nil {
		return nil, fmt.Errorf("failed to form XML request: %s got: %v", string(xmlData), err)
	}

	r := bytes.NewReader(xmlData)
	var created = false

	for i := 0; !created && i < p.maxAttempts; i++ {
		req, err := http.NewRequest("POST", p.dpoAPIURL, r)
		if err != nil {
			return nil, err
		}
		req.Header.Add("User-Agent", defaultUA)
		req.Header.Add("Content-Type", "application/xml")
		req.Header.Add("Cache-control", "no-cache")

		resp, err := p.httpClient.Do(req)
		if err != nil {
			return nil, err
		}

		bodyData, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, fmt.Errorf("failed to read body: %s got: %v", string(bodyData), err)
		}

		var refundTokenResponse entities.RefundTokenResponse
		if resp.StatusCode == http.StatusOK {
			err = xml.Unmarshal(bodyData, &refundTokenResponse)
			if err != nil {
				return nil, fmt.Errorf("failed unmarshal response: %v", err)
			}

			switch refundTokenResponse.Result {
			case "000":
				return &refundTokenResponse, nil
			case "801", "802", "803", "804", "950", "999":
			default:
				return &refundTokenResponse, fmt.Errorf("dpo error: %s", refundTokenResponse.ResultExplanation)
			}

		} else if resp.StatusCode >= 400 && resp.StatusCode < 500 {
			return nil, fmt.Errorf("invalid response code:%d body: %s", resp.StatusCode, string(bodyData))
		}
	}

	return nil, fmt.Errorf("failed to process request after %d attempts", p.maxAttempts)
}

func (p *AppDPO) VerifyPayment(trxToken string) (*entities.VerifyTokenResponse, error) {

	verifyTokenResponse := &entities.VerifyTokenResponse{}

	verifyRequest := &entities.VerifyTokenRequest{
		Request:          "verifyToken",
		CompanyToken:     p.dpoToken,
		TransactionToken: trxToken,
	}

	var xmlData []byte
	var err error

	xmlData, err = utils.XMLMarshalWithHeader(verifyRequest)
	if err != nil {
		return verifyTokenResponse, fmt.Errorf("failed to form XML request: %s got: %v", string(xmlData), err)
	}

	r := bytes.NewReader(xmlData)
	var created = false

	for i := 0; !created && i < p.maxAttempts; i++ {
		req, err := http.NewRequest("POST", p.dpoAPIURL, r)
		if err != nil {
			return nil, err
		}
		req.Header.Add("User-Agent", defaultUA)
		req.Header.Add("Content-Type", "application/xml")
		req.Header.Add("Cache-control", "no-cache")

		resp, err := p.httpClient.Do(req)
		if err != nil {
			return verifyTokenResponse, err
		}

		bodyData, err := io.ReadAll(resp.Body)
		if err != nil {
			return verifyTokenResponse, fmt.Errorf("failed to read body: %s got: %v", string(bodyData), err)
		}

		if resp.StatusCode == http.StatusOK {
			err = xml.Unmarshal(bodyData, &verifyTokenResponse)
			if err != nil {
				return verifyTokenResponse, fmt.Errorf("failed unmarshal response: %v", err)
			}
			return verifyTokenResponse, nil
		} else if resp.StatusCode >= 400 && resp.StatusCode < 500 {
			return verifyTokenResponse, fmt.Errorf("invalid response code:%d body: %s", resp.StatusCode, string(bodyData))
		}
	}

	return nil, fmt.Errorf("failed to process request after %d attempts", p.maxAttempts)
}

func (p *AppDPO) GeneratePaymentToken(form *forms.DPOPaymentRequestForm) (*entities.DPOPaymentToken, error) {

	paymentToken := &entities.DPOPaymentToken{}
	paymentAmount := strings.ReplaceAll(form.Amount, ",", "")

	createTokenRequest := &entities.CreateTokenRequest{
		CompanyToken: p.dpoToken,
		Request:      "createToken",
		Transaction: entities.CreateTokenTransaction{
			BackURL:           form.BackURL,
			CompanyRef:        form.AccountReference,
			CompanyRefUnique:  0, // 0 - not unique, 1 - duplicate request
			CustomerAddress:   form.CustomerAddress,
			CustomerCity:      form.CustomerCity,
			CustomerCountry:   form.CustomerCountry,
			CustomerEmail:     form.CustomerEmail,
			CustomerFirstName: form.CustomerFirstName,
			CustomerLastName:  form.CustomerLastName,
			CustomerPhone:     form.CustomerPhone,
			PaymentAmount:     paymentAmount,
			PaymentCurrency:   form.Currency,
			PTL:               "15", // Payment Time Limit
			PTLtype:           "minutes",
			RedirectURL:       form.RedirectURL,
		},
		Services: []entities.Service{},
	}

	// indicates which service the payment will be made for.
	service := entities.Service{
		ServiceType:        form.ServiceCode,
		ServiceDescription: form.ServiceDescription,
		ServiceDate:        form.ServiceDate.Format("2006/01/02 15:04"),
	}

	createTokenRequest.Services = append(createTokenRequest.Services, service)

	token, err := p.CreateToken(createTokenRequest)
	if err != nil {
		log.Printf("failed to create dpo payment token, err=[%v]\n", err)
		return paymentToken, err
	}

	if token.TransToken == "" {
		return paymentToken, fmt.Errorf("failed to get token")
	}

	paymentToken.TransToken = token.TransToken
	paymentURL := fmt.Sprintf("%s?ID=%s", p.dpoPayURL, token.TransToken)
	paymentToken.PaymentURL = paymentURL

	return paymentToken, nil
}
