package forms

type (
	VFMSItem struct {
		Discount int     `json:"discount"`
		ItemID   int64   `json:"itemId"`
		ItemName string  `json:"itemName"`
		Price    float64 `json:"price"`
		Quantity int     `json:"quantity"`
	}

	VFMSSaleForm struct {
		PhoneNumber     string     `json:"phoneNumber"`
		ReferenceNumber string     `json:"referenceNumber"`
		SalesCurrency   string     `json:"salesCurrency"`
		SalesCustomer   string     `json:"salesCustomer"`
		SalesItems      []VFMSItem `json:"salesItems"`
	}
)
