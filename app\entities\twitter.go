package entities

type (
	TwitterUser struct {
		UserData TwitterUserData `json:"data"`
	}

	TwitterUserData struct {
		ID          string       `json:"id"`
		Name        string       `json:"name"`
		Username    string       `json:"username"`
		TwitterFeed *TwitterFeed `json:"twitter_feed"`
	}

	TwitterFeed struct {
		Tweets []*Tweet `json:"tweets"`
		Meta   Meta     `json:"meta"`
	}

	TweetPostAPIRespose struct {
		Data TweetPostResposeData `json:"data"`
	}

	TweetPostResposeData struct {
		ID   string `json:"id"`
		Text string `json:"text"`
	}

	TweetsDataAPIResponse struct {
		Tweets        []*Tweet      `json:"data"`
		MediaIncludes MediaIncludes `json:"includes"`
		Meta          Meta          `json:"meta"`
	}

	Tweet struct {
		ID          string      `json:"id"`
		Text        string      `json:"text"`
		Attachments Attachments `json:"attachments"`
		MediaList   []*Media    `json:"media_list"`
	}

	Attachments struct {
		MediaKeys []string `json:"media_keys"`
	}

	MediaIncludes struct {
		MediaList []Media `json:"media"`
	}

	Media struct {
		TweetID  string `json:"tweet_id"`
		MediaKey string `json:"media_key"`
		Type     string `json:"type"`
		URL      string `json:"url"`
	}

	Meta struct {
		ResultCount int    `json:"result_count"`
		NewestID    string `json:"newest_id"`
		OldestID    string `json:"oldest_id"`
		NextToken   string `json:"next_token"`
	}
)
