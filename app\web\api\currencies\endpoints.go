package currencies

import (
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/web/auth"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	router *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	currencyService services.CurrencyService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	authenticatedAPI := router.Group("").Use(middleware.AllowOnlyActiveUser(transactionsDB, sessionAuthenticator, sessionService))
	{
		authenticatedAPI.GET("/currencies", filterCurrencies(transactionsDB, currencyService))
		authenticatedAPI.GET("/currencies/:id", getCurrency(transactionsDB, currencyService))
	}
}
