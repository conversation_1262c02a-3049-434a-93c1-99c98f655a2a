package utils

const (
	HEADER_TIN     = "tin"
	HEADER_BHF_ID  = "bhfId"
	HEADER_CMC_KEY = "cmcKey"

	URL_TEST_ECHO                  = "selectTestEcho"
	URL_MAIN_SERVERTIME            = "selectServerTime"
	URL_INIT_INFO                  = "selectInitVsdcInfo"
	URL_INIT_INFO_SEQ              = "selectInitInfoVsdcSeq"
	URL_CODE_SEARCH                = "selectCodeList"
	URL_ITEM_CLASS_SEARCH          = "selectItemClsList"
	URL_CUST_SEARCH                = "selectCustomer"
	URL_ITEM_SEARCH                = "selectItemList"
	URL_ITEM_SAVE                  = "saveItem"
	URL_ITEM_COMPOSITION_SAVE      = "saveItemComposition"
	URL_BHF_SEARCH                 = "selectBhfList"
	URL_BHF_USER_SAVE              = "saveBhfUser"
	URL_BHF_INSURANCE_SAVE         = "saveBhfInsurance"
	URL_BHF_CUST_SAVE              = "saveBhfCustomer"
	URL_TRNS_SALES_SAVES_VSDC      = "saveTrnsSalesVsdc"
	URL_TRNS_PURCHASE_SALES_SEARCH = "selectTrnsPurchaseSalesList"
	URL_TRNS_PURCHASE_SAVE         = "insertTrnsPurchase"
	URL_IMPORT_ITEM_SEARCH         = "selectImportItemList"
	URL_IMPORT_ITEM_UPDATE         = "updateImportItem"
	URL_STOCK_MASTER_SAVE          = "saveStockMaster"
	URL_STOCK_MOVE_SEARCH          = "selectStockMoveList"
	URL_STOCK_IO_SAVE              = "insertStockIO"
	URL_NOTICE_SEARCH              = "selectNoticeList"
	URL_REPORT_Z_SAVE              = "saveReportZ"
	URL_REPORT_Z_CHECK             = "checkReportZ"
)

var (
	FOLDER_PATH_TEST_ECHO                  = []string{"test", "echo"}
	FOLDER_PATH_INIT_INFO                  = []string{"initInfo"}
	FOLDER_PATH_INIT_INFO_SEQ              = []string{"initInfo"}
	FOLDER_PATH_CODE_SEARCH                = []string{"code", "search"}
	FOLDER_PATH_ITEM_CLASS_SEARCH          = []string{"item", "class", "search"}
	FOLDER_PATH_CUST_SEARCH                = []string{"cust", "search"}
	FOLDER_PATH_ITEM_SEARCH                = []string{"item", "base", "search"}
	FOLDER_PATH_ITEM_SAVE                  = []string{"item", "base", "save"}
	FOLDER_PATH_ITEM_COMPOSITION_SAVE      = []string{"item", "composition", "save"}
	FOLDER_PATH_BHF_SEARCH                 = []string{"bhf", "base", "search"}
	FOLDER_PATH_BHF_USER_SAVE              = []string{"bhf", "user", "save"}
	FOLDER_PATH_BHF_INSURANCE_SAVE         = []string{"bhf", "insurance", "save"}
	FOLDER_PATH_BHF_CUST_SAVE              = []string{"bhf", "cust", "save"}
	FOLDER_PATH_TRNS_SALES_SAVE            = []string{"trns", "sales", "base", "save"}
	FOLDER_PATH_TRNS_PURCHASE_SALES_SEARCH = []string{"trns", "purchase", "sales", "search"}
	FOLDER_PATH_TRNS_PURCHASE_SAVE         = []string{"trns", "purchase", "base", "save"}
	FOLDER_PATH_IMPORT_ITEM_SEARCH         = []string{"import", "item", "search"}
	FOLDER_PATH_IMPORTITEM_UPDATE          = []string{"import", "item", "update"}
	FOLDER_PATH_STOCK_MASTER_SAVE          = []string{"stock", "master", "save"}
	FOLDER_PATH_STOCK_MOVE_SEARCH          = []string{"stock", "move", "search"}
	FOLDER_PATH_STOCK_IO_SAVE              = []string{"stock", "io", "save"}
	FOLDER_PATH_NOTICE_SEARCH              = []string{"notice", "search"}
	FOLDER_PATH_REPORT_Z_SAVE              = []string{"report", "z", "save"}
	FOLDER_PATH_REPORT_Z_CHECK             = []string{"report", "z", "check"}
)
