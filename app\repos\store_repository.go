package repos

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"compliance-and-risk-management-backend/app/apperr"
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"strconv"
	"strings"
)

const (
	countStoresSQL = `SELECT COUNT(*) AS count FROM stores WHERE id=$1 AND deleted_at IS NULL`

	countStoresForUserSQL = countStoresSQL + ` AND user_id=$2`

	createStoreSQL = `INSERT INTO stores (avatar_url, email, etims_branch, etims_device_serial, etims_environment, location, name, organization_id, 
		phone_number, pin, vat, website, user_id, country_code, created_at, updated_at) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16) RETURNING id`

	filterStoresForUserSQL = selectStoreSQL + ` AND user_id=$1`

	selectUserDefaultStoreSQL = selectStoreSQL + ` AND user_id=$1 ORDER BY ID DESC LIMIT 1`

	selectStoreByIDSQL = selectStoreSQL + ` AND id=$1`

	selectStoreByNameAndOrganizationSQL = selectStoreSQL + ` AND name=$1 AND organization_id=$2`

	selectStoreSQL = `SELECT id, avatar_url, email, etims_branch, etims_device_serial, etims_environment, is_licensed, location, name, organization_id, phone_number, pin, 
		vat, website, user_id, country_code, created_at, updated_at FROM stores WHERE deleted_at IS NULL`

	updateStoreSQL = `UPDATE stores SET avatar_url=$1, email=$2, etims_branch=$3, etims_device_serial=$4, etims_environment=$5, is_licensed=$6, location=$7, name=$8, 
		phone_number=$9, pin=$10, vat=$11, website=$12, country_code=$13, updated_at=$14 WHERE id=$15`
)

type (
	StoreRepository interface {
		CountStores(context.Context, *entities.PaginationFilter) (int, error)
		CountStoresForUser(context.Context, txns_db.TransactionsSQLOperations, int64, *entities.PaginationFilter) (int, error)
		FilterStoresForUser(context.Context, txns_db.TransactionsSQLOperations, int64, *entities.PaginationFilter) ([]*entities.Store, error)
		FindByNameAndOrganization(context.Context, string, int64) (*entities.Store, error)
		FindByID(context.Context, int64) (*entities.Store, error)
		FindByIDs(ctx context.Context, operations txns_db.TransactionsSQLOperations, storeIDs []int64) ([]*entities.Store, error)
		GetStoreByID(int64) (*entities.Store, error)
		GetUserDefaultStore(userID int64) (*entities.Store, error)
		Save(context.Context, *entities.Store) error
	}

	AppStoreRepository struct {
		db *sql.DB
	}
)

func NewStoreRepository(db *sql.DB) StoreRepository {
	return &AppStoreRepository{db: db}
}

func (r *AppStoreRepository) CountStores(
	ctx context.Context,
	filter *entities.PaginationFilter,
) (int, error) {
	var count int

	args := make([]interface{}, 0)
	args = append(args, filter.StoreID)
	query := countStoresSQL
	currentIndex := 2

	query, args, _ = r.buildQueryFromFilter(query, filter, args, currentIndex)
	row := r.db.QueryRowContext(ctx, query, args...)
	err := row.Scan(&count)
	if err != nil {
		return count, err
	}
	return count, nil
}

func (r *AppStoreRepository) CountStoresForUser(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	userID int64,
	filter *entities.PaginationFilter,
) (int, error) {
	var count int

	args := make([]interface{}, 0)
	args = append(args, filter.StoreID)
	args = append(args, userID)
	currentIndex := 2

	query, args, _ := r.buildQueryFromFilter(countStoresForUserSQL, filter, args, currentIndex)

	row := operations.QueryRowContext(ctx, query, args...)
	err := row.Scan(&count)
	if err != nil {
		fmt.Printf("unable to count stores for user, err=[%v]\n", err.Error())
		return count, err
	}

	return count, nil
}

func (r *AppStoreRepository) FilterStoresForUser(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	userID int64,
	filter *entities.PaginationFilter,
) ([]*entities.Store, error) {

	stores := make([]*entities.Store, 0)

	args := make([]interface{}, 0)
	args = append(args, userID)
	query := filterStoresForUserSQL
	currentIndex := 2

	query, args, currentIndex = r.buildQueryFromFilter(query, filter, args, currentIndex)
	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return stores, err
	}

	defer rows.Close()

	for rows.Next() {
		invoice, err := r.scanRowIntoStore(rows)
		if err != nil {
			return stores, err
		}

		stores = append(stores, invoice)
	}

	return stores, rows.Err()
}

func (r *AppStoreRepository) FindByNameAndOrganization(
	ctx context.Context,
	name string,
	organizationID int64,
) (*entities.Store, error) {
	row := r.db.QueryRow(selectStoreByNameAndOrganizationSQL, name, organizationID)
	return r.scanRowIntoStore(row)
}

func (r *AppStoreRepository) FindByID(
	ctx context.Context,
	storeID int64,
) (*entities.Store, error) {
	row := r.db.QueryRow(selectStoreByIDSQL, storeID)
	return r.scanRowIntoStore(row)
}

func (r *AppStoreRepository) FindByIDs(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	storeIDs []int64,
) ([]*entities.Store, error) {

	stores := make([]*entities.Store, 0)
	args := []interface{}{}
	currentIndex := 1

	if len(storeIDs) == 0 {
		return stores, nil
	}

	storeIDsQuery := ""
	for _, assetID := range storeIDs {
		storeIDsQuery += "$" + strconv.Itoa(currentIndex) + ","
		args = append(args, assetID)
		currentIndex++
	}
	storeIDsQuery = strings.TrimRight(storeIDsQuery, ",")
	query := selectStoreSQL + " AND id IN (" + storeIDsQuery + ")"

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return stores, apperr.NewDatabaseError(err)
	}

	defer rows.Close()

	for rows.Next() {
		store, err := r.scanRowIntoStore(rows)
		if err != nil {
			return stores, apperr.NewDatabaseError(err)
		}

		stores = append(stores, store)
	}

	return stores, apperr.WrapNilableDatabaseError(rows.Err())
}

func (r *AppStoreRepository) GetStoreByID(storeID int64) (*entities.Store, error) {
	row := r.db.QueryRow(selectStoreByIDSQL, storeID)
	return r.scanRowIntoStore(row)
}

func (r *AppStoreRepository) GetUserDefaultStore(userID int64) (*entities.Store, error) {
	row := r.db.QueryRow(selectUserDefaultStoreSQL, userID)
	return r.scanRowIntoStore(row)
}

func (r *AppStoreRepository) Save(
	ctx context.Context,
	store *entities.Store,
) error {

	store.Timestamps.Touch()
	var err error

	if store.IsNew() {
		err = r.db.QueryRow(
			createStoreSQL,
			store.AvatarURL,
			store.Email,
			store.EtimsBranch,
			store.EtimsDeviceSerial,
			store.EtimsEnvironment,
			store.Location,
			store.Name,
			store.OrganizationID,
			store.PhoneNumber,
			store.PIN,
			store.VAT,
			store.Website,
			store.UserID,
			store.CountryCode,
			store.CreatedAt,
			store.UpdatedAt,
		).Scan(&store.ID)

	} else {

		_, err = r.db.Exec(
			updateStoreSQL,
			store.AvatarURL,
			store.Email,
			store.EtimsBranch,
			store.EtimsDeviceSerial,
			store.EtimsEnvironment,
			store.IsLicensed,
			store.Location,
			store.Name,
			store.PhoneNumber,
			store.PIN,
			store.VAT,
			store.Website,
			store.CountryCode,
			store.UpdatedAt,
			store.ID,
		)
	}

	if err != nil {
		log.Printf("error saving store, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppStoreRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	return query, args, currentIndex
}

func (r *AppStoreRepository) scanRowIntoStore(
	rowScanner txns_db.RowScanner,
) (*entities.Store, error) {

	var store entities.Store

	err := rowScanner.Scan(
		&store.ID,
		&store.AvatarURL,
		&store.Email,
		&store.EtimsBranch,
		&store.EtimsDeviceSerial,
		&store.EtimsEnvironment,
		&store.IsLicensed,
		&store.Location,
		&store.Name,
		&store.OrganizationID,
		&store.PhoneNumber,
		&store.PIN,
		&store.VAT,
		&store.Website,
		&store.UserID,
		&store.CountryCode,
		&store.CreatedAt,
		&store.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning store,  err=[%v]\n", err.Error())
		return &store, err
	}

	return &store, nil
}
