package middleware

import (
	"strings"

	"github.com/gin-gonic/gin"
)

func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {

		origin := c.Request.Header.Get("Origin") // Get Origin header
		if origin == "" {
			origin = "Unknown Origin"
		}
		// log.Println("Request Origin:", origin)

		allowedOrigins := map[string]bool{
			"https://hash.growthlogic.co.ke":        true,
			"http://localhost:8080":                 true,
			"http://localhost:3000":                 true,
			"https://mpesa-hash-decoder.vercel.app": true,
			"https://phone-hash-decoder.vercel.app": true,
		}

		// Check if origin is allowed
		if allowedOrigins[origin] {
			c.<PERSON>.Header().Set("Access-Control-Allow-Origin", origin)
		}

		allowedHeaders := []string{
			"AUTHORIZATION", "X-STRUTSPOS-APPLICATION, X-STRUTSPOS-TOKEN", "X-CLIENT-IDENTIFIER", "X-CLIENT-VERSION",
			"STORE_ID", "X-STORE-ID", "ACCESS-CONTROL-ALLOW-HEADERS", "ACCESS-CONTROL-ALLOW-ORIGIN", "X-FORWARDED-FOR"}

		c.Writer.Header().Set("Cache-Control", "no-cache, no-store, must-revalidate")
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With, "+strings.Join(allowedHeaders, ", "))
		c.Writer.Header().Set("Access-Control-Expose-Headers", "X-CSRF-Token, Authorization, X-Requested-With, "+strings.Join(allowedHeaders, ", "))
		c.Writer.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Writer.Header().Set("X-Frame-Options", "ALLOW FROM http://localhost")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}
