package repos

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"strings"
	"time"
)

const (
	countSaleItemsSQL = `SELECT COUNT(*) AS count FROM sale_items WHERE sale_id=$1`

	countSaleItemsForSaleIDSQL = countSaleItemsSQL + ` AND sale_id=$1`

	countSaleItemsForUserSQL = countSaleItemsSQL + ` AND created_by=$1`

	insertSaleItemSQL = `INSERT INTO sale_items (created_by, currency_id, discount, item_id, organization_id, quantity, sale_id, scale, total, total_amount_display, 
		created_at, updated_at) VALUES`

	createSaleItemSQL = insertSaleItemSQL + ` ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12) RETURNING id`

	filterSaleItemsForSaleIdSQL = selectSaleItemSQL + ` AND sale_id=$1`

	selectSaleItemByIDSQL = selectSaleItemSQL + ` AND id=$1`

	selectSaleItemByNameAndOrganizationSQL = selectSaleItemSQL + ` AND name=$1 AND organization_id=$2`

	selectSaleItemsByIDsSQL = selectSaleItemSQL + " AND id IN "

	selectSaleItemSQL = `SELECT id, created_by, currency_id, discount, item_id, organization_id, quantity, sale_id, scale, total, total_amount_display, created_at, 
		updated_at FROM sale_items WHERE 1=1`

	updateSaleItemSQL = `UPDATE sale_items SET currency_id=$1, discount=$2, quantity=$3, scale=$4, total=$5, total_amount_display=$6, updated_at=$7 WHERE id=$8`
)

type (
	SaleItemRepository interface {
		CountSaleItems(context.Context, *entities.PaginationFilter, int64) (int, error)
		CountSaleItemsForSaleID(context.Context, txns_db.TransactionsSQLOperations, int64, *entities.PaginationFilter) (int, error)
		CountSaleItemsForUser(context.Context, txns_db.TransactionsSQLOperations, int64, *entities.PaginationFilter) (int, error)
		FilterSaleItemsForSaleId(context.Context, txns_db.TransactionsSQLOperations, int64, *entities.PaginationFilter, int64) ([]*entities.SaleItem, error)
		FilterSaleItemsForSaleIds(context.Context, txns_db.TransactionsSQLOperations, []int64, *entities.PaginationFilter, int64) ([]*entities.SaleItem, error)
		FindByNameAndOrganization(context.Context, string, int64) (*entities.SaleItem, error)
		FindByID(context.Context, int64) (*entities.SaleItem, error)
		GetSaleItemsByIds(context.Context, txns_db.TransactionsSQLOperations, []int64, int64) ([]*entities.SaleItem, error)
		GetSaleItemByID(int64) (*entities.SaleItem, error)
		Save(context.Context, *entities.SaleItem) error
		SaveMultiple(context.Context, txns_db.TransactionsSQLOperations, []*entities.SaleItem) error
	}

	AppSaleItemRepository struct {
		db *sql.DB
	}
)

func NewSaleItemRepository(db *sql.DB) SaleItemRepository {
	return &AppSaleItemRepository{db: db}
}

func (r *AppSaleItemRepository) CountSaleItems(
	ctx context.Context,
	filter *entities.PaginationFilter,
	saleId int64,
) (int, error) {
	var count int

	args := make([]interface{}, 0)
	args = append(args, saleId)
	currentIndex := 1

	query, args, _ := r.buildQueryFromFilter(countSaleItemsSQL, filter, args, currentIndex)
	row := r.db.QueryRowContext(ctx, query, args...)

	err := row.Scan(&count)
	if err != nil {
		return count, err
	}
	return count, nil
}

func (r *AppSaleItemRepository) CountSaleItemsForSaleID(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	saleID int64,
	filter *entities.PaginationFilter,
) (int, error) {
	var count int

	args := make([]interface{}, 0)
	args = append(args, saleID)
	currentIndex := 1

	query, args, _ := r.buildQueryFromFilter(countSaleItemsForSaleIDSQL, filter, args, currentIndex)

	row := operations.QueryRowContext(ctx, query, args...)
	err := row.Scan(&count)
	if err != nil {
		fmt.Printf("unable to count saleItems for user, err=[%v]\n", err.Error())
		return count, err
	}

	return count, nil
}

func (r *AppSaleItemRepository) CountSaleItemsForUser(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	userID int64,
	filter *entities.PaginationFilter,
) (int, error) {
	var count int

	args := make([]interface{}, 0)
	args = append(args, userID)
	currentIndex := 1

	query, args, _ := r.buildQueryFromFilter(countSaleItemsForUserSQL, filter, args, currentIndex)

	row := operations.QueryRowContext(ctx, query, args...)
	err := row.Scan(&count)
	if err != nil {
		fmt.Printf("unable to count saleItems for user, err=[%v]\n", err.Error())
		return count, err
	}

	return count, nil
}

func (r *AppSaleItemRepository) FilterSaleItemsForSaleId(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	saleId int64,
	filter *entities.PaginationFilter,
	storeId int64,
) ([]*entities.SaleItem, error) {

	saleItems := make([]*entities.SaleItem, 0)

	args := make([]interface{}, 0)
	args = append(args, saleId)
	query := filterSaleItemsForSaleIdSQL
	currentIndex := 2

	query, args, currentIndex = r.buildQueryFromFilter(query, filter, args, currentIndex)
	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return saleItems, err
	}

	defer rows.Close()

	for rows.Next() {
		invoice, err := r.scanRowIntoSaleItem(rows)
		if err != nil {
			return saleItems, err
		}

		saleItems = append(saleItems, invoice)
	}

	return saleItems, rows.Err()
}

func (r *AppSaleItemRepository) FilterSaleItemsForSaleIds(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	saleIds []int64,
	filter *entities.PaginationFilter,
	storeId int64,
) ([]*entities.SaleItem, error) {

	saleItems := make([]*entities.SaleItem, 0)

	args := make([]interface{}, 0)
	query := selectSaleItemsByIDsSQL
	currentIndex := 1

	values := make([]string, len(saleIds))
	for index, itemID := range saleIds {
		values[index] = fmt.Sprintf("$%d", currentIndex)
		args = append(args, itemID)
		currentIndex++
	}

	query += " (" + strings.Join(values, ",") + ")"
	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return saleItems, err
	}

	defer rows.Close()

	for rows.Next() {
		invoice, err := r.scanRowIntoSaleItem(rows)
		if err != nil {
			return saleItems, err
		}

		saleItems = append(saleItems, invoice)
	}

	return saleItems, rows.Err()
}

func (r *AppSaleItemRepository) FindByNameAndOrganization(
	ctx context.Context,
	name string,
	organizationID int64,
) (*entities.SaleItem, error) {
	row := r.db.QueryRow(selectSaleItemByNameAndOrganizationSQL, name, organizationID)
	return r.scanRowIntoSaleItem(row)
}

func (r *AppSaleItemRepository) FindByID(
	ctx context.Context,
	saleItemID int64,
) (*entities.SaleItem, error) {
	row := r.db.QueryRow(selectSaleItemByIDSQL, saleItemID)
	return r.scanRowIntoSaleItem(row)
}

func (r *AppSaleItemRepository) GetSaleItemsByIds(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	itemIDs []int64,
	storeId int64,
) ([]*entities.SaleItem, error) {

	saleItems := make([]*entities.SaleItem, 0)

	args := make([]interface{}, 0)
	query := selectSaleItemsByIDsSQL

	values := make([]string, len(itemIDs))
	for index, currencyID := range itemIDs {
		values[index] = fmt.Sprintf("$%d", index+1)
		args = append(args, currencyID)
	}

	query += " (" + strings.Join(values, ",") + ")"
	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return saleItems, err
	}

	defer rows.Close()

	for rows.Next() {
		invoice, err := r.scanRowIntoSaleItem(rows)
		if err != nil {
			return saleItems, err
		}

		saleItems = append(saleItems, invoice)
	}

	return saleItems, rows.Err()
}

func (r *AppSaleItemRepository) GetSaleItemByID(saleItemID int64) (*entities.SaleItem, error) {
	row := r.db.QueryRow(selectSaleItemByIDSQL, saleItemID)
	return r.scanRowIntoSaleItem(row)
}

func (r *AppSaleItemRepository) Save(
	ctx context.Context,
	saleItem *entities.SaleItem,
) error {

	saleItem.Timestamps.Touch()
	var err error

	if saleItem.IsNew() {

		err = r.db.QueryRow(
			createSaleItemSQL,
			saleItem.CreatedBy,
			saleItem.CurrencyID,
			saleItem.Discount,
			saleItem.ItemID,
			saleItem.OrganizationID,
			saleItem.Quantity,
			saleItem.SaleID,
			saleItem.Scale,
			saleItem.Total,
			saleItem.TotalAmountDisplay,
			saleItem.CreatedAt,
			saleItem.UpdatedAt,
		).Scan(&saleItem.ID)

	} else {

		_, err = r.db.Exec(
			updateSaleItemSQL,
			saleItem.CurrencyID,
			saleItem.Discount,
			saleItem.Quantity,
			saleItem.Scale,
			saleItem.Total,
			saleItem.TotalAmountDisplay,
			saleItem.UpdatedAt,
			saleItem.ID,
		)
	}

	if err != nil {
		log.Printf("error saving saleItem, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppSaleItemRepository) SaveMultiple(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	saleItems []*entities.SaleItem,
) error {

	query := insertSaleItemSQL
	args := make([]interface{}, 0)
	currentIndex := 1

	values := make([]string, len(saleItems))

	for index, invoiceItem := range saleItems {
		values[index] = fmt.Sprintf("($%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d)",
			currentIndex, currentIndex+1, currentIndex+2, currentIndex+3, currentIndex+4, currentIndex+5,
			currentIndex+6, currentIndex+7, currentIndex+8, currentIndex+9, currentIndex+10, currentIndex+11)
		currentIndex += 12
		args = append(
			args,
			invoiceItem.CreatedBy,
			invoiceItem.CurrencyID,
			invoiceItem.Discount,
			invoiceItem.ItemID,
			invoiceItem.OrganizationID,
			invoiceItem.Quantity,
			invoiceItem.SaleID,
			invoiceItem.Scale,
			invoiceItem.Total,
			invoiceItem.TotalAmountDisplay,
			time.Now(),
			time.Now(),
		)
	}

	query += strings.Join(values, ",")
	// query += " ON CONFLICT(notice_number) DO UPDATE SET updated_at=Now() RETURNING id"

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return err
	}
	defer rows.Close()

	index := 0
	for rows.Next() {
		err := rows.Scan(&saleItems[index].ID)
		if err != nil {
			return err
		}
		index++
	}

	return rows.Err()
}

func (r *AppSaleItemRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	return query, args, currentIndex
}

func (r *AppSaleItemRepository) scanRowIntoSaleItem(
	rowScanner txns_db.RowScanner,
) (*entities.SaleItem, error) {

	var saleItem entities.SaleItem

	err := rowScanner.Scan(
		&saleItem.ID,
		&saleItem.CreatedBy,
		&saleItem.CurrencyID,
		&saleItem.Discount,
		&saleItem.ItemID,
		&saleItem.OrganizationID,
		&saleItem.Quantity,
		&saleItem.SaleID,
		&saleItem.Scale,
		&saleItem.Total,
		&saleItem.TotalAmountDisplay,
		&saleItem.CreatedAt,
		&saleItem.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning saleItem,  err=[%v]\n", err.Error())
		return &saleItem, err
	}

	return &saleItem, nil
}
