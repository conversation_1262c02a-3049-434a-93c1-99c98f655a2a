package api_keys

import (
	"log"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/logger"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/webutils"
)

func createAPIKey(
	transactionsDB *database.AppTransactionsDB,
	apiKeyService services.APIKeyService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		var apiKeyForm forms.CreateAPIKeyForm
		err := ctx.Bind(&apiKeyForm)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind apiKey form while creating apiKey",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		apiKey, err := apiKeyService.CreateAPIKey(ctx.Request.Context(), transactionsDB, &apiKeyForm)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to save apiKey. Err=" + err.Error(),
			})
			return
		}

		ctx.JSON(http.StatusOK, apiKey)
	}
}

func filterAPIKeys(
	transactionsDB *database.AppTransactionsDB,
	apiKeyService services.APIKeyService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering apiKeys",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		apiKeyList, err := apiKeyService.FilterAPIKeys(ctx.Request.Context(), transactionsDB, filter)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to filter apiKeys",
			})
			return
		}

		ctx.JSON(http.StatusOK, apiKeyList)
	}
}

func getAPIKey(
	transactionsDB *database.AppTransactionsDB,
	apiKeyService services.APIKeyService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		apiKeyIDStr := ctx.Param("id")
		apiKeyID, err := strconv.ParseInt(apiKeyIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse apiKeyID=[%v], err=[%v]\n", apiKeyIDStr, err)
		}

		storeIdStr := ctx.Request.Header.Get("x-store-id")
		storeId := utils.ConvertStringToInt64(storeIdStr)
		if storeId < 1 {
			ctx.JSON(500, gin.H{
				"message": "unable to process store_id",
			})
			return
		}

		apiKey, err := apiKeyService.FindAPIKeyByID(ctx.Request.Context(), apiKeyID, storeId)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to find apiKey by id=[%v]",
				apiKeyIDStr,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, apiKey)
	}
}

func deleteAPIKey(
	transactionsDB *database.AppTransactionsDB,
	apiKeyService services.APIKeyService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		apiKeyIDStr := ctx.Param("id")
		apiKeyID, err := strconv.ParseInt(apiKeyIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse apiKeyID=[%v], err=[%v]\n", apiKeyIDStr, err)
		}

		storeIdStr := ctx.Request.Header.Get("x-store-id")
		storeId := utils.ConvertStringToInt64(storeIdStr)
		if storeId < 1 {
			ctx.JSON(500, gin.H{
				"message": "unable to process store_id",
			})
			return
		}

		apiKey, err := apiKeyService.DeleteAPIKey(ctx.Request.Context(), storeId, apiKeyID)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to delete apiKey by id=[%v]",
				apiKeyIDStr,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, apiKey)
	}
}

func testAPIKey(
	transactionsDB *database.AppTransactionsDB,
	apiKeyService services.APIKeyService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		var form interface{}
		err := ctx.Bind(&form)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind apiKey form while testing apiKey",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		logger.Infof("Received form=[%+v]\n", form)

		ctx.JSON(http.StatusOK, "")
	}
}

func updateAPIKey(
	transactionsDB *database.AppTransactionsDB,
	apiKeyService services.APIKeyService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		apiKeyIDStr := ctx.Param("id")
		apiKeyID, err := strconv.ParseInt(apiKeyIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse apiKeyID=[%v], err=[%v]\n", apiKeyIDStr, err)
		}

		var form forms.UpdateAPIKeyForm
		err = ctx.Bind(&form)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind apiKey form while updating apiKey",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		storeIdStr := ctx.Request.Header.Get("x-store-id")
		storeId := utils.ConvertStringToInt64(storeIdStr)
		if storeId < 1 {
			ctx.JSON(500, gin.H{
				"message": "unable to process store_id",
			})
			return
		}

		apiKey, err := apiKeyService.UpdateAPIKey(ctx.Request.Context(), transactionsDB, apiKeyID, storeId, &form)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to update apiKey by id=[%v]",
				apiKeyIDStr,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, apiKey)
	}
}
