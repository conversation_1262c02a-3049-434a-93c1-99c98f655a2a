package forms

type (
	RegisterURLForm struct {
		ShortCode       string `binding:"required" json:"ShortCode"`
		ResponseType    string `binding:"required" json:"ResponseType"`
		ConfirmationURL string `binding:"required" json:"ConfirmationURL"`
		ValidationURL   string `binding:"required" json:"ValidationURL"`
	}

	RegisterMPesaURLsForm struct {
		ConfirmationURL string `binding:"required" json:"confirmation_url"`
		ValidationURL   string `binding:"required" json:"validation_url"`
	}

	RegisterClientMPesaURLsForm struct {
		ConsumerKey     string `binding:"required" json:"consumer_key"`
		ConsumerSecret  string `binding:"required" json:"consumer_secret"`
		ShortCode       string `binding:"required" json:"short_code"`
		ConfirmationURL string `binding:"required" json:"confirmation_url"`
		ValidationURL   string `binding:"required" json:"validation_url"`
	}
)
