package ctxhelper

import (
	"context"

	"compliance-and-risk-management-backend/app/entities"
)

func ClientOS(ctx context.Context) string {
	existing := ctx.Value(entities.ContextKeyClientOS)
	if existing == nil {
		return ""
	}

	return existing.(string)
}

func WithClientOS(ctx context.Context, clientOS string) context.Context {
	return context.WithValue(ctx, entities.ContextKeyClientOS, clientOS)
}
