package services

import (
	"context"
	"database/sql"
	"errors"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/ctxhelper"

	txns_db "compliance-and-risk-management-backend/app/database"
)

type CategoryService interface {
	CreateCategory(ctx context.Context, operations txns_db.TransactionsSQLOperations, category *forms.CreateCategoryForm, storeId int64) (*entities.Category, error)
	DownloadCategories(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter, filePath string) (*entities.CategoryList, error)
	FilterCategories(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter, storeId int64) (*entities.CategoryList, error)
	FindCategoryByID(ctx context.Context, categoryID int64) (*entities.Category, error)
	GetCategoryByName(ctx context.Context, categoryNumber string) (*entities.Category, error)
	SaveCategory(ctx context.Context, operations txns_db.TransactionsSQLOperations, category *entities.Category) error
	UpdateCategory(context.Context, txns_db.TransactionsSQLOperations, int64, *forms.UpdateCategoryForm) (*entities.Category, error)
}

type AppCategoryService struct {
	categoryRepository repos.CategoryRepository
	userRepository     repos.UserRepository
}

func NewCategoryService(
	categoryRepo repos.CategoryRepository,
	userRepository repos.UserRepository,
) CategoryService {
	return &AppCategoryService{
		categoryRepository: categoryRepo,
		userRepository:     userRepository,
	}
}

func (s *AppCategoryService) CreateCategory(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	form *forms.CreateCategoryForm,
	storeId int64,
) (*entities.Category, error) {

	tokenInfo := ctxhelper.TokenInfo(ctx)

	user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	if err != nil {
		return &entities.Category{}, err
	}

	category, err := s.categoryRepository.FindByNameAndStore(ctx, form.Name, storeId)
	if err != nil && !utils.IsErrNoRows(err) {
		return category, errors.New("category already exists")
	}

	category.Name = form.Name
	category.Description = form.Description
	category.OrganizationID = user.OrganizationID
	category.StoreID = form.StoreId

	err = s.categoryRepository.Save(ctx, category)
	if err != nil {
		return category, err
	}

	return category, nil
}

func (s *AppCategoryService) DownloadCategories(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
	filePath string,
) (*entities.CategoryList, error) {

	categoryList := &entities.CategoryList{}

	count := s.categoryRepository.CountCategories(ctx, filter, filter.StoreID)

	categories, err := s.categoryRepository.FilterCategories(ctx, operations, filter, filter.StoreID)
	if err != nil {
		return categoryList, err
	}

	categoryList.Categories = categories

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	categoryList.Pagination = pagination

	utils.CreateAndAppendCategoriesDataToExcelFile(filePath, categories)

	return categoryList, nil
}

func (s *AppCategoryService) FilterCategories(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
	storeId int64,
) (*entities.CategoryList, error) {

	categoryList := &entities.CategoryList{}

	count := s.categoryRepository.CountCategories(ctx, filter, storeId)

	categorys, err := s.categoryRepository.FilterCategories(ctx, operations, filter, storeId)
	if err != nil {
		return categoryList, err
	}

	categoryList.Categories = categorys

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	categoryList.Pagination = pagination

	return categoryList, nil
}

func (s *AppCategoryService) FindCategoryByID(
	ctx context.Context,
	categoryID int64,
) (*entities.Category, error) {

	category, err := s.categoryRepository.FindByID(ctx, categoryID)
	if err != nil {
		return category, err
	}

	return category, nil
}

func (s *AppCategoryService) GetCategoryByName(
	ctx context.Context,
	categoryNumber string,
) (*entities.Category, error) {

	tokenInfo := ctxhelper.TokenInfo(ctx)

	user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	if err != nil {
		return &entities.Category{}, err
	}

	category, err := s.categoryRepository.FindByNameAndOganization(ctx, categoryNumber, user.OrganizationID)
	if err != nil {
		return category, err
	}

	return category, nil
}

func (s *AppCategoryService) SaveCategory(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	category *entities.Category,
) error {

	err := s.categoryRepository.Save(ctx, category)
	if err != nil {
		return err
	}

	return nil
}

func (s *AppCategoryService) UpdateCategory(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	categoryID int64,
	form *forms.UpdateCategoryForm,
) (*entities.Category, error) {

	tokenInfo := ctxhelper.TokenInfo(ctx)

	user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	if err != nil {
		return &entities.Category{}, err
	}

	category, err := s.categoryRepository.FindByID(ctx, categoryID)
	if err != nil {
		return category, errors.New("unable to find category")
	}

	existingCategory, err := s.categoryRepository.FindByNameAndOganization(ctx, form.Name, user.OrganizationID)
	if err != nil && err != sql.ErrNoRows {
		return existingCategory, errors.New("category already exists")
	}

	category.Name = form.Name
	category.Description = form.Description

	err = s.categoryRepository.Save(ctx, category)
	if err != nil {
		return category, err
	}

	return category, nil
}
