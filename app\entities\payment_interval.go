package entities

import "database/sql/driver"

type PaymentInterval string

const (
	PaymentIntervalDaily      PaymentInterval = "daily"
	PaymentIntervalWeekly     PaymentInterval = "weekly"
	PaymentIntervalMonthly    PaymentInterval = "monthly"
	PaymentIntervalQuarterly  PaymentInterval = "quarterly"
	PaymentIntervalBiannually PaymentInterval = "biannually"
	PaymentIntervalAnnually   PaymentInterval = "annually"
)

func (s PaymentInterval) Equals(value PaymentInterval) bool {
	return s == value
}

func (s PaymentInterval) IsValid() bool {
	return s == PaymentIntervalDaily ||
		s == PaymentIntervalWeekly ||
		s == PaymentIntervalMonthly ||
		s == PaymentIntervalQuarterly ||
		s == PaymentIntervalBiannually ||
		s == PaymentIntervalAnnually
}

func (s *PaymentInterval) Scan(value interface{}) error {
	*s = PaymentInterval(string(value.([]uint8)))
	return nil
}

func (s PaymentInterval) Value() (driver.Value, error) {
	return s.String(), nil
}

func (s PaymentInterval) String() string {
	return string(s)
}
