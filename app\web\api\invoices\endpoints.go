package invoices

import (
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/web/auth"

	"compliance-and-risk-management-backend/app/services"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	invoiceService services.InvoiceService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyActiveUser(transactionsDB, sessionAuthenticator, sessionService))
	{
		protectedAPI.POST("/invoices", signInvoice(transactionsDB, invoiceService))
		protectedAPI.GET("/invoices", filterInvoices(transactionsDB, invoiceService))
		protectedAPI.GET("/invoices/:id", getInvoice(invoiceService))
		protectedAPI.GET("/invoices/download", downloadInvoicesExcel(transactionsDB, invoiceService))
	}

	unauthenticatedAPI := routerGroup.Group("")
	{
		unauthenticatedAPI.GET("/invoices/:id/pdf", getInvoicePDFFile(transactionsDB, invoiceService))
	}
}
