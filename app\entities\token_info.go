package entities

import (
	"time"
)

type (
	TokenInfo struct {
		APIKeyID        int64
		APIKey          string
		ApplicationType ApplicationType
		ClientID        string
		Exp             time.Time
		OrganizationID  int64
		Refresh         time.Time
		SessionID       int64
		Status          string
		StoreID         int64
		UserID          int64
	}
)

func (ti *TokenInfo) RequiresRefresh() bool {
	return time.Now().After(ti.Refresh)
}
