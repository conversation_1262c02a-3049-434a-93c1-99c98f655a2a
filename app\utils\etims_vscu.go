package utils

import (
	"context"
	"fmt"
	"os"
	"compliance-and-risk-management-backend/app/entities"
	"time"

	"github.com/skip2/go-qrcode"
)

func GenerateETIMSVSCUQRCode(
	ctx context.Context,
	invoice *entities.Invoice,
) error {

	// Generate and save QR Code
	qrCodeFolder := os.Getenv("QRCODE_FOLDER")
	qrCodePNGFile := qrCodeFolder + "\\" + invoice.InvoiceNumber + ".png"

	qrCodeData := fmt.Sprintf("%v", invoice.VerificationURL)

	etimsResultDateTime := *invoice.EtimsResultDateTime
	etimsDateTime, err := EtimsDateTimeToTime(etimsResultDateTime)
	if err != nil {
		fmt.Printf("failed to convert etims date time, err=[%v]\n", err)
		return err
	}

	etimsDate := FormatTime(etimsDateTime, "02/01/2006") //  25/01/2023
	etimsTime := FormatTime(etimsDateTime, "03:04:05")   // 20060102030405 11:07:35

	qrCodeData += "\n\nSCU INFORMATION\n"
	qrCodeData += fmt.Sprintf("Date: %v \n", etimsDate)
	qrCodeData += fmt.Sprintf("Time: %v \n", etimsTime)
	qrCodeData += fmt.Sprintf("Timestamp: %v \n", invoice.EtimsVSCUReceiptPublicationDate)
	qrCodeData += fmt.Sprintf("VAT: %v \n", invoice.Vat)
	qrCodeData += fmt.Sprintf("Grand Total: %v \n", invoice.GrandTotal)
	qrCodeData += fmt.Sprintf("SCU ID: %v \n", invoice.EtimsSDCID)
	qrCodeData += fmt.Sprintf("CU INVOICE NO: %v/%v \n", invoice.EtimsSDCID, invoice.EtimsReceiptNumber)
	qrCodeData += fmt.Sprintf("Internal Data: %v \n", invoice.EtimsInternalData)
	qrCodeData += fmt.Sprintf("Receipt Signature: %v \n", invoice.EtimsReceiptSignature)

	err = qrcode.WriteFile(qrCodeData, qrcode.Medium, 256, qrCodePNGFile)
	if err != nil {
		fmt.Printf("Error generating QR Code, Err=[%v]\n", err)
		return err
	}
	time.Sleep(100 * time.Millisecond) // Allow for QR Code file creation

	return nil
}
