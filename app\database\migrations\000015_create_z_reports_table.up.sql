CREATE TABLE IF NOT EXISTS z_reports (
    id                  BIGSERIAL                   PRIMARY KEY,    
    z_report_date       VARCHAR(150)                NOT NULL,
    z_report_time       VARCHAR(150)                NOT NULL,
    invoice_count       BIGINT                      NOT NULL default 0,
    net_amount          NUMERIC(15,2)               NOT NULL default 0,
    vat_amount          NUMERIC(15,2)               NOT NULL default 0,
    pmt_amount          NUMERIC(15,2)               NOT NULL default 0,
    total_amount        NUMERIC(15,2)               NOT NULL default 0,
    z_report_payload    TEXT, 
    is_synced           BOOLEAN                     NOT NULL DEFAULT FALSE, 
    status              VARCHAR(50)                 NOT NULL DEFAULT 'pending', 
    created_at          TIMESTAMP WITH TIME ZONE    DEFAULT now() NOT NULL, 
    updated_at          TIMESTAMP WITH TIME ZONE 
);
