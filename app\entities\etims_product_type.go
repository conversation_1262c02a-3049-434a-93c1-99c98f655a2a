package entities

import (
	"database/sql/driver"
)

type EtimsProductType string

const (
	EtimsProductTypeRawMaterial     EtimsProductType = "1"
	EtimsProductTypeFinishedProduct EtimsProductType = "2"
	EtimsProductTypeService         EtimsProductType = "3"
)

func (s EtimsProductType) IsValid() bool {
	return s == EtimsProductTypeRawMaterial || s == EtimsProductTypeFinishedProduct ||
		s == EtimsProductTypeService
}

// Scan implements the Scanner interface.
func (s *EtimsProductType) Scan(value interface{}) error {
	*s = EtimsProductType(string(value.([]uint8)))
	return nil
}

// Value implements the driver Valuer interface.
func (s EtimsProductType) Value() (driver.Value, error) {
	return s.String(), nil
}

func (s EtimsProductType) String() string {
	return string(s)
}
