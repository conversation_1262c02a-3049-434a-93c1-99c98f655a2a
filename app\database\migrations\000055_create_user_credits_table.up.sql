CREATE TABLE IF NOT EXISTS user_credits (
    id              BIGSERIAL                   PRIMARY KEY,    
    user_id         BIGINT                      NOT NULL REFERENCES users(id),         
    credit_balance  NUMERIC(15,2)               NOT NULL,        
    deleted_at      TIMESTAMP,
    created_at      TIMESTAMP WITH TIME ZONE    DEFAULT now() NOT NULL, 
    updated_at      TIMESTAMP WITH TIME ZONE 
);

CREATE UNIQUE INDEX IF NOT EXISTS user_credits_user_id_uidx ON user_credits(user_id);
