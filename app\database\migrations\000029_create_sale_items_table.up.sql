CREATE TABLE IF NOT EXISTS sale_items (
    id                    BIGSERIAL                   PRIMARY KEY,     
    item_id               BIGINT                      REFERENCES items (id),  
    organization_id       BIGINT                      NOT NULL REFERENCES organizations (id),  
    quantity              NUMERIC(15,2)               NOT NULL,  
    discount              BIGINT,  
    total                 BIGINT, 
    total_amount_display  VARCHAR(250)                NOT NULL,   
    scale                 BIGINT,  
    currency_id           BIGINT                      NOT NULL REFERENCES currencies (id),         
    created_by            BIGINT                      NOT NULL REFERENCES users (id),      
    sale_id               BIGINT                      NOT NULL REFERENCES sales (id),      
    created_at            TIMESTAMP WITH TIME ZONE    DEFAULT now() NOT NULL, 
    updated_at            TIMESTAMP WITH TIME ZONE 
);

