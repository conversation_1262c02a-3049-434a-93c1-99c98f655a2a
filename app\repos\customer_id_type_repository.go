package repos

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"compliance-and-risk-management-backend/app/entities"
	"strings"
	"time"

	txns_db "compliance-and-risk-management-backend/app/database"
)

const (
	countCustomerIDTypesSQL = `SELECT COUNT(*) AS count FROM customer_id_types WHERE 1=1`

	createCustomerIDTypeSQL = `INSERT INTO customer_id_types (customer_id_type, description, is_default, created_at)
		VALUES ($1, $2, $3, $4) RETURNING id`

	selectCustomerIDTypeByIDSQL = selectCustomerIDTypeSQL + ` WHERE id=$1`

	selectCustomerIDTypeSQL = `SELECT id, customer_id_type, description, is_default, created_at, updated_at FROM customer_id_types`

	selectDefaultCustomerIDTypeSQL = selectCustomerIDTypeSQL + ` WHERE is_default=true`

	updateCustomerIDTypesSQL = `UPDATE customer_id_types SET is_default=$1, updated_at=$2 WHERE id=$3`

	updateMultipleCustomerIDTypesSQL = `UPDATE customer_id_types AS c SET (is_default, updated_at) = (u.is_default, u.updated_at)`
)

type (
	CustomerIDTypeRepository interface {
		CountCustomerIDTypes(ctx context.Context, filter *entities.PaginationFilter) (int, error)
		CreateCustomerIDType(ctx context.Context, customerIDType *entities.CustomerIDType) error
		FilterCustomerIDTypes(ctx context.Context, operations txns_db.TransactionsSQLOperations) ([]*entities.CustomerIDType, error)
		GetCustomerIDTypeByID(ctx context.Context, customerIDTypeID int64) (*entities.CustomerIDType, error)
		GetDefaultCustomerIDType(ctx context.Context) (*entities.CustomerIDType, error)
		SetCustomerIDTypeAsDefault(ctx context.Context, operations txns_db.TransactionsSQLOperations, customerIDType *entities.CustomerIDType) error
	}

	AppCustomerIDTypeRepository struct {
		db *sql.DB
	}
)

func NewCustomerIDTypeRepository(db *sql.DB) CustomerIDTypeRepository {
	return &AppCustomerIDTypeRepository{db: db}
}

func (r *AppCustomerIDTypeRepository) CountCustomerIDTypes(
	ctx context.Context,
	filter *entities.PaginationFilter,
) (int, error) {
	var count int
	query, args, _ := r.buildQueryFromFilter(countCustomerIDTypesSQL, filter, []interface{}{}, 1)
	row := r.db.QueryRowContext(ctx, query, args...)
	row.Scan(&count)
	return count, nil
}

func (r *AppCustomerIDTypeRepository) CreateCustomerIDType(
	ctx context.Context,
	customerIDType *entities.CustomerIDType,
) error {

	err := r.db.QueryRow(
		createCustomerIDTypeSQL,
		customerIDType.CustomerIDType,
		customerIDType.Description,
		customerIDType.IsDefault,
		time.Now(),
	).Scan(&customerIDType.ID)

	if err != nil {
		log.Printf("error saving customerIDType, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppCustomerIDTypeRepository) FilterCustomerIDTypes(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
) ([]*entities.CustomerIDType, error) {

	customerIDTypes := make([]*entities.CustomerIDType, 0)

	args := make([]interface{}, 0)
	query := selectCustomerIDTypeSQL
	currentIndex := 1

	query, args, currentIndex = r.buildQueryFromFilter(query, &entities.PaginationFilter{}, args, currentIndex)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return customerIDTypes, err
	}

	defer rows.Close()

	for rows.Next() {
		invoice, err := r.scanRowIntoCustomerIDType(rows)
		if err != nil {
			return customerIDTypes, err
		}

		customerIDTypes = append(customerIDTypes, invoice)
	}

	return customerIDTypes, rows.Err()
}

func (r *AppCustomerIDTypeRepository) GetCustomerIDTypeByID(
	ctx context.Context,
	customerIDTypeID int64,
) (*entities.CustomerIDType, error) {
	row := r.db.QueryRow(selectCustomerIDTypeByIDSQL, customerIDTypeID)
	return r.scanRowIntoCustomerIDType(row)
}

func (r *AppCustomerIDTypeRepository) GetDefaultCustomerIDType(
	ctx context.Context,
) (*entities.CustomerIDType, error) {
	row := r.db.QueryRow(selectDefaultCustomerIDTypeSQL)
	return r.scanRowIntoCustomerIDType(row)
}

func (r *AppCustomerIDTypeRepository) SetCustomerIDTypeAsDefault(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	defaultCustomerIDType *entities.CustomerIDType,
) error {

	customerIDTypes, err := r.FilterCustomerIDTypes(ctx, operations)
	if err != nil {
		return err
	}

	query := updateMultipleCustomerIDTypesSQL + " FROM (VALUES "
	values := make([]string, len(customerIDTypes))
	args := make([]interface{}, 0)
	currentIndex := 1

	for index, customerIDType := range customerIDTypes {
		customerIDType.IsDefault = false
		if customerIDType.ID == defaultCustomerIDType.ID {
			customerIDType.IsDefault = true
		}
		values[index] = fmt.Sprintf("($%d::bigint, $%d::boolean, $%d::timestamptz)", currentIndex, currentIndex+1, currentIndex+2)
		currentIndex += 3
		args = append(args, customerIDType.ID, customerIDType.IsDefault, time.Now())
	}

	query += strings.Join(values, ",")
	query += ") AS u(id, is_default, updated_at) WHERE u.id = c.id"
	_, err = operations.ExecContext(ctx, query, args...)

	return err
}

func (r *AppCustomerIDTypeRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	return query, args, currentIndex
}

func (r *AppCustomerIDTypeRepository) scanRowIntoCustomerIDType(
	rowScanner txns_db.RowScanner,
) (*entities.CustomerIDType, error) {

	var customerIDType entities.CustomerIDType

	err := rowScanner.Scan(
		&customerIDType.ID,
		&customerIDType.CustomerIDType,
		&customerIDType.Description,
		&customerIDType.IsDefault,
		&customerIDType.CreatedAt,
		&customerIDType.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning customerIDType,  err=[%v]\n", err.Error())
		return &customerIDType, err
	}

	return &customerIDType, nil
}
