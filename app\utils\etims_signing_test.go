package utils

import (
	"fmt"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

func TestGenerateEtimsSignature(t *testing.T) {
	Convey("GenerateEtimsSignature", t, func() {

		store := &entities.Store{
			EtimsBranch:       "00",
			EtimsDeviceSerial: "SN5433Q09",
			EtimsEnvironment:  "sandbox",
			Name:              "Functional Oil",
			PIN:               "P051922564N",
		}

		tinBhfPath := fmt.Sprintf("%v_%v", store.PIN, store.EtimsBranch)
		commonDate := "20240814013856"

		Convey("can generate a signature for an invoice", func() {

			invoice, err := SimulateInvoice()
			if err != nil {
				fmt.Printf("error simulating invoice, err=[%v]\n", err)
			}

			currReceiptNumber := invoice.CurrentReceiptNumber
			invoiceNumberInt64 := ConvertStringToInt64(invoice.InvoiceNumber)

			etimsReceiptForm := &forms.EtimsReceiptForm{
				Adrs:         "Nairobi,Kenya",
				BtmMsg:       store.Name,
				CurRcptNo:    currReceiptNumber,
				CustMblNo:    invoice.CustomerPhone,
				CustTin:      invoice.CustomerTIN,
				PrchrAcptcYn: "Y",
				RcptPbctDt:   commonDate,
				RptNo:        currReceiptNumber,
				TopMsg:       store.Name,
				TotRcptNo:    currReceiptNumber,
				TrdeNm:       invoice.CustomerName,
			}

			totalTaxableAmount := invoice.GrandTotal - invoice.Vat
			totalTaxableAmountStr := fmt.Sprintf("%.2f", totalTaxableAmount)
			totalTaxableAmountFloat64 := ConvertStringToFloat64(totalTaxableAmountStr)
			totalTaxableAmountFloat64 = RoundFloat64(totalTaxableAmountFloat64, 2)
			totalVat := RoundFloat64(invoice.Vat, 2)
			taxAmountB := RoundFloat64(invoice.GrandTotal, 2)

			form := &forms.CreateEtimsSalesForm{
				CustTin:     invoice.CustomerTIN,
				InvcNo:      invoiceNumberInt64,
				RcptTyCd:    "S", // S for Sale, R for Credit Note
				Receipt:     etimsReceiptForm,
				SalesTyCd:   "N",
				TaxblAmtA:   0.00,
				TaxAmtA:     0.00,
				TaxblAmtB:   taxAmountB,
				TaxAmtB:     totalVat,
				Tin:         store.PIN,
				TotTaxAmt:   totalVat,
				TotTaxblAmt: totalTaxableAmountFloat64,
				TotAmt:      invoice.GrandTotal,
			}

			internalData, err := GenerateInternalData(invoice, form, tinBhfPath)
			if err != nil {
				fmt.Printf("error generating internal data, err=[%v]\n", err)
			}
			So(internalData, ShouldEqual, "AVA7XAGAAAATFAZAA2AVHASAAM")

			invoiceSignature, err := GenerateEtimsSignature(invoice, form, tinBhfPath)
			if err != nil {
				fmt.Printf("error generating invoice signature, err=[%v]\n", err)
			}
			So(invoiceSignature, ShouldEqual, "AZA2DAHAAEA4RA3A")
		})

		Convey("can round off a float64", func() {
			var val float64 = 98.189876656778
			roundedValue := RoundFloat64(val, 2)
			So(roundedValue, ShouldEqual, 98.19)
		})
	})
}
