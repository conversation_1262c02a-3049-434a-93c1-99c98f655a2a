package repos

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"strings"
	"time"
)

const (
	countStockSQL = `SELECT COUNT(*) AS count FROM etims_stock WHERE store_id=$1`

	insertEtimsStockSQL = `INSERT INTO etims_stock (tin, bhfId, sarNo, orgSarNo, regTyCd, custTin, custNm, custBhfId, sarTyCd, ocrnDt,  
		totItemCnt, totTaxblAmt, totTaxAmt, totAmt, remark, regrId, regrNm, modrNm, modrId, uuid, store_id, created_at, updated_at) VALUES`

	createEtimsStockSQL = insertEtimsStockSQL + ` ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, 
		$20, $21, $22, $23) RETURNING id`

	selectEtimsStockByIDSQL = selectEtimsStockSQL + ` WHERE id=$1`

	selectEtimsStockByUUIDSQL = selectEtimsStockSQL + ` WHERE uuid=$1`

	selectEtimsStockSQL = `SELECT id, tin, bhfId, sarNo, orgSarNo, regTyCd, custTin, custNm, custBhfId, sarTyCd, ocrnDt, totItemCnt,   
		totTaxblAmt, totTaxAmt, totAmt, remark, regrId, regrNm, modrNm, modrId, uuid, store_id, created_at, updated_at FROM etims_stock`
)

type (
	EtimsStockRepository interface {
		Count(context.Context, *entities.PaginationFilter) (int, error)
		FilterEtimsStocks(context.Context, txns_db.TransactionsSQLOperations, *entities.PaginationFilter) ([]*entities.EtimsStock, error)
		FindByID(context.Context, int64) (*entities.EtimsStock, error)
		FindByUUID(context.Context, string) (*entities.EtimsStock, error)
		Save(context.Context, txns_db.TransactionsSQLOperations, *entities.EtimsStock) error
		SaveMultiple(context.Context, txns_db.TransactionsSQLOperations, []*entities.EtimsStock) error
	}

	AppEtimsStockRepository struct {
		db *sql.DB
	}
)

func NewEtimsStockRepository(db *sql.DB) EtimsStockRepository {
	return &AppEtimsStockRepository{db: db}
}

func (r *AppEtimsStockRepository) Count(
	ctx context.Context,
	filter *entities.PaginationFilter,
) (int, error) {
	var count int
	query, args, _ := r.buildQueryFromFilter(countStockSQL, filter, []interface{}{}, 1)
	row := r.db.QueryRowContext(ctx, query, args...)
	row.Scan(&count)
	return count, nil
}

func (r *AppEtimsStockRepository) FilterEtimsStocks(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) ([]*entities.EtimsStock, error) {

	etimsStocks := make([]*entities.EtimsStock, 0)

	args := make([]interface{}, 0)
	query := selectEtimsStockSQL
	currentIndex := 1

	query, args, currentIndex = r.buildQueryFromFilter(query, filter, args, currentIndex)
	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return etimsStocks, err
	}

	defer rows.Close()

	for rows.Next() {
		etimsStock, err := r.scanRowIntoEtimsStock(rows)
		if err != nil {
			return etimsStocks, err
		}

		etimsStocks = append(etimsStocks, etimsStock)
	}

	return etimsStocks, rows.Err()
}

func (r *AppEtimsStockRepository) FindByID(
	ctx context.Context,
	id int64,
) (*entities.EtimsStock, error) {
	row := r.db.QueryRow(selectEtimsStockByIDSQL, id)
	return r.scanRowIntoEtimsStock(row)
}

func (r *AppEtimsStockRepository) FindByUUID(
	ctx context.Context,
	uuid string,
) (*entities.EtimsStock, error) {
	row := r.db.QueryRow(selectEtimsStockByUUIDSQL, uuid)
	return r.scanRowIntoEtimsStock(row)
}

func (r *AppEtimsStockRepository) Save(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	etimsStock *entities.EtimsStock,
) error {

	etimsStock.Timestamps.Touch()

	err := operations.QueryRowContext(
		ctx,
		createEtimsStockSQL,
		etimsStock.Tin,
		etimsStock.BhfID,
		etimsStock.SarNo,
		etimsStock.OrgSarNo,
		etimsStock.RegTyCd,
		etimsStock.CustTin,
		etimsStock.CustNm,
		etimsStock.CustBhfID,
		etimsStock.SarTyCd,
		etimsStock.OcrnDt,
		etimsStock.TotItemCnt,
		etimsStock.TotTaxblAmt,
		etimsStock.TotTaxAmt,
		etimsStock.TotAmt,
		etimsStock.Remark,
		etimsStock.RegrID,
		etimsStock.RegrNm,
		etimsStock.ModrNm,
		etimsStock.ModrID,
		etimsStock.UUID,
		etimsStock.StoreID,
		etimsStock.CreatedAt,
		etimsStock.UpdatedAt,
	).Scan(&etimsStock.ID)

	if err != nil {
		log.Printf("error saving etimsStock, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppEtimsStockRepository) SaveMultiple(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	etimsStocks []*entities.EtimsStock,
) error {

	query := insertEtimsStockSQL
	args := make([]interface{}, 0)
	currentIndex := 1

	values := make([]string, len(etimsStocks))

	for index, etimsStock := range etimsStocks {

		values[index] = fmt.Sprintf("($%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d)",
			currentIndex, currentIndex+1, currentIndex+2, currentIndex+3, currentIndex+4, currentIndex+5, currentIndex+6, currentIndex+7, currentIndex+8,
			currentIndex+9, currentIndex+10, currentIndex+11, currentIndex+12, currentIndex+13, currentIndex+14, currentIndex+15, currentIndex+16,
			currentIndex+17, currentIndex+18, currentIndex+19, currentIndex+20, currentIndex+21, currentIndex+22)

		currentIndex += 23

		args = append(
			args,
			etimsStock.Tin,
			etimsStock.BhfID,
			etimsStock.SarNo,
			etimsStock.OrgSarNo,
			etimsStock.RegTyCd,
			etimsStock.CustTin,
			etimsStock.CustNm,
			etimsStock.CustBhfID,
			etimsStock.SarTyCd,
			etimsStock.OcrnDt,
			etimsStock.TotItemCnt,
			etimsStock.TotTaxblAmt,
			etimsStock.TotTaxAmt,
			etimsStock.TotAmt,
			etimsStock.Remark,
			etimsStock.RegrID,
			etimsStock.RegrNm,
			etimsStock.ModrNm,
			etimsStock.ModrID,
			etimsStock.UUID,
			etimsStock.StoreID,
			time.Now(),
			time.Now(),
		)
	}

	query += strings.Join(values, ",")
	// query += " ON CONFLICT(uuid) DO UPDATE SET updated_at=Now() RETURNING id"

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return err
	}
	defer rows.Close()

	index := 0
	for rows.Next() {
		err := rows.Scan(&etimsStocks[index].ID)
		if err != nil {
			return err
		}
		index++
	}

	return rows.Err()
}

func (r *AppEtimsStockRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	if len(strings.TrimSpace(filter.Search)) > 0 {
		query += fmt.Sprintf(" AND (LOWER(custNm) LIKE '%%' || $%d || '%%' ", currentIndex)
		query += fmt.Sprintf(" OR LOWER(sarNo) LIKE '%%' || $%d || '%%' ", currentIndex)
		query += fmt.Sprintf(" OR LOWER(orgSarNo) LIKE '%%' || $%d || '%%' ", currentIndex)
		query += fmt.Sprintf(" OR LOWER(custTin) LIKE '%%' || $%d || '%%' )", currentIndex)
		args = append(args, strings.ToLower(filter.Search))
		currentIndex++
	}

	return query, args, currentIndex
}

func (r *AppEtimsStockRepository) scanRowIntoEtimsStock(
	rowScanner txns_db.RowScanner,
) (*entities.EtimsStock, error) {

	var etimsStock entities.EtimsStock

	err := rowScanner.Scan(
		&etimsStock.ID,
		&etimsStock.Tin,
		&etimsStock.BhfID,
		&etimsStock.SarNo,
		&etimsStock.OrgSarNo,
		&etimsStock.RegTyCd,
		&etimsStock.CustTin,
		&etimsStock.CustNm,
		&etimsStock.CustBhfID,
		&etimsStock.SarTyCd,
		&etimsStock.OcrnDt,
		&etimsStock.TotItemCnt,
		&etimsStock.TotTaxblAmt,
		&etimsStock.TotTaxAmt,
		&etimsStock.TotAmt,
		&etimsStock.Remark,
		&etimsStock.RegrID,
		&etimsStock.RegrNm,
		&etimsStock.ModrNm,
		&etimsStock.ModrID,
		&etimsStock.UUID,
		&etimsStock.StoreID,
		&etimsStock.CreatedAt,
		&etimsStock.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning etimsStock,  err=[%v]\n", err.Error())
		return &etimsStock, err
	}

	return &etimsStock, nil
}
