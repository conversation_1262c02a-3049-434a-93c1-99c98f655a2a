package providers

import (
	"bytes"
	"errors"
	"mime"

	"compliance-and-risk-management-backend/app/utils"

	"github.com/disintegration/imaging"
)

const maxLength = 200

var (
	validImageExts = []string{".gif", ".jpg", ".jpeg", ".png", ".jfif"}
	validVideoExts = []string{".avi", ".mpeg", ".mp4", ".m4v"}
)

type (
	Imaging interface {
		CheckIsImage(contentType string) (bool, error)
		GenerateThumbnail(b []byte, contentType string) (*ThumbnailInfo, error)
	}

	AppImaging struct {
	}

	ThumbnailInfo struct {
		Data        []byte
		ContentType string
		Length      int
		Width       int
	}
)

func NewImaging() Imaging {
	return &AppImaging{}
}

func (ai *AppImaging) CheckIsImage(contentType string) (bool, error) {
	extension, err := ai.getExtensionFromContentType(contentType)
	if err != nil {
		return false, err
	}

	return utils.SliceContains(validImageExts, extension), nil
}

func (ai *AppImaging) checkIsVideo(contentType string) (bool, error) {
	extension, err := ai.getExtensionFromContentType(contentType)
	if err != nil {
		return false, err
	}

	return utils.SliceContains(validVideoExts, extension), nil
}

func (ai *AppImaging) createThumbnailFromImage(b []byte, contentType string) (*ThumbnailInfo, error) {
	ioReader := bytes.NewReader(b)

	image, err := imaging.Decode(ioReader)
	if err != nil {
		return nil, utils.NewError(
			err,
			"Error decoding image from byte slice",
		)
	}

	// Set the smaller dimension to 0 to preserve aspect ratio
	var width, height int
	if image.Bounds().Dx() > image.Bounds().Dy() {
		width = maxLength
	} else {
		height = maxLength
	}

	dstImage := imaging.Resize(image, width, height, imaging.Lanczos)

	format, err := ai.getFormatFromContentType(contentType)
	if err != nil {
		return nil, utils.NewError(
			err,
			"Error getting file format from image content type=[%v]",
			contentType,
		)
	}

	var buffer bytes.Buffer
	err = imaging.Encode(&buffer, dstImage, format)
	if err != nil {
		return nil, utils.NewError(
			err,
			"Error encoding image to format=[%v] after resizing",
			format,
		)
	}

	return &ThumbnailInfo{
		Data:        buffer.Bytes(),
		ContentType: contentType,
		Length:      dstImage.Bounds().Dy(),
		Width:       dstImage.Bounds().Dx(),
	}, nil
}

func (ai *AppImaging) GenerateThumbnail(b []byte, contentType string) (*ThumbnailInfo, error) {
	isImage, err := ai.CheckIsImage(contentType)
	if err != nil {
		return nil, err
	}

	if isImage {
		return ai.createThumbnailFromImage(b, contentType)
	}

	isVideo, err := ai.checkIsVideo(contentType)
	if err != nil {
		return nil, err
	}

	if isVideo {
		return nil, utils.NewError(
			errors.New("generating video thumbnail not implemented"),
			"",
		)
	}

	return nil, utils.NewError(
		errors.New("invalid asset format"),
		"Asset format cannot be identified",
	)
}

func (ai *AppImaging) getExtensionFromContentType(contentType string) (string, error) {
	extensions, err := mime.ExtensionsByType(contentType)
	if err != nil {
		return "", utils.NewError(
			err,
			"Failed to get extension from content type=[%v]",
			contentType,
		)
	}

	return extensions[0], nil
}

func (ai *AppImaging) getFormatFromContentType(contentType string) (imaging.Format, error) {
	extension, err := ai.getExtensionFromContentType(contentType)
	if err != nil {
		return 0, err
	}

	// image/jpeg contentType now returns as extension jfif
	// imaging package does not recognize jfif extension which is an update to jpeg
	// https://en.wikipedia.org/wiki/JPEG_File_Interchange_Format
	if extension == ".jfif" {
		extension = ".jpeg"
	}

	return imaging.FormatFromExtension(extension)
}
