package database

import (
	"context"
	"database/sql"
	"fmt"
	"os"
)

type TransactionsSQLOperations interface {
	ExecContext(ctx context.Context, query string, args ...interface{}) (sql.Result, error)
	QueryContext(ctx context.Context, query string, args ...interface{}) (*sql.Rows, error)
	QueryRowContext(ctx context.Context, query string, args ...interface{}) *sql.Row
	ValidForTransactions() bool
}

type surveysSQLOperations struct {
	*sql.Tx
}

func (o *surveysSQLOperations) ValidForTransactions() bool {
	return true
}

type TransactionsDB interface {
	DB
	InTransaction(ctx context.Context, operations func(context.Context, TransactionsSQLOperations) error) error
	ValidForTransactions() bool
	Valid() bool
}

type AppTransactionsDB struct {
	DB
	valid bool
}

func InitTransactionsDB() *AppTransactionsDB {
	return InitTransactionsDBWithURL(
		os.Getenv("DATABASE_URL"),
	)
}

func InitTransactionsDBWithURL(databaseURL string) *AppTransactionsDB {
	appDB := NewPostgresDBWithURL(databaseURL)
	return &AppTransactionsDB{
		DB:    appDB,
		valid: true,
	}
}

func InitTransactionsTestDB() *AppTransactionsDB {
	return InitTransactionsDBWithURL(
		os.Getenv("TEST_DATABASE_URL"),
	)
}

func InitTransactionsSQLTestDB(databaseURL string) *sql.DB {
	txnsDB, err := sql.Open("postgres", databaseURL)
	if err != nil {
		panic(fmt.Sprintf("unable to connect to ESD database, err=[%v]", err))
	}

	// run db migrations
	storage := NewStorage(txnsDB)
	err = storage.RunMigrations(databaseURL)
	if err != nil {
		panic(fmt.Sprintf("unable to run postgres db migrations, err=[%v]", err))
	}

	return txnsDB
}

func (db *AppTransactionsDB) InTransaction(ctx context.Context, operations func(context.Context, TransactionsSQLOperations) error) error {

	tx, err := db.Begin()
	if err != nil {
		return err
	}

	sqlOperations := &surveysSQLOperations{
		Tx: tx,
	}

	if err = operations(ctx, sqlOperations); err != nil {
		if rollbackErr := tx.Rollback(); rollbackErr != nil {
			return rollbackErr
		}

		return err
	}

	return tx.Commit()
}

func (db *AppTransactionsDB) ValidForTransactions() bool {
	return true
}

func (db *AppTransactionsDB) Valid() bool {
	return db.valid
}
