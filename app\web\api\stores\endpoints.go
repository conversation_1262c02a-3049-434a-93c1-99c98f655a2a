package stores

import (
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/web/auth"

	"compliance-and-risk-management-backend/app/services"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	storeService services.StoreService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyActiveUser(transactionsDB, sessionAuthenticator, sessionService))
	{
		protectedAPI.POST("/stores", createStore(transactionsDB, storeService))
		protectedAPI.GET("/stores", filterStores(transactionsDB, storeService))
		protectedAPI.GET("/stores/:id", getStore(transactionsDB, storeService))
		protectedAPI.PUT("/stores/:id", updateStore(transactionsDB, storeService))
		protectedAPI.GET("/stores/download", downloadStoresExcel(transactionsDB, storeService))
	}

	verifiedAdminUserAPI := routerGroup.Group("").Use(middleware.AllowOnlyAdmin(transactionsDB, sessionAuthenticator, sessionService))
	{
		verifiedAdminUserAPI.PUT("/stores/:id/license", updateStoreLicense(transactionsDB, storeService))
	}
}
