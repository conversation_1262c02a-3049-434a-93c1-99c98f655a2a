package models

import "encoding/xml"

type RegistrationResponse struct {
	XMLName xml.Name  `xml:"EFDMS"`
	EFDMS   EFDMSRESP `xml:"EFDMSRESP" json:"-"`
}

type EFDMSRESP struct {
	XMLName        xml.Name `xml:"EFDMSRESP"`
	ACKCODE        string   `xml:"ACKCODE" json:"ACKCODE"`
	ACKMSG         string   `xml:"ACKMSG" json:"ACKMSG"`
	REGID          string   `xml:"REGID" json:"REGID"`
	SERIAL         string   `xml:"SERIAL" json:"SERIAL"`
	UIN            string   `xml:"UIN" json:"UIN"`
	TIN            string   `xml:"TIN" json:"TIN"`
	VRN            string   `xml:"VRN" json:"VRN"`
	MOBILE         string   `xml:"MOBILE" json:"MOBILE"`
	STREET         string   `xml:"STREET" json:"STREET"`
	CITY           string   `xml:"CITY" json:"CITY"`
	COUNTRY        string   `xml:"COUNTRY" json:"COUNTRY"`
	NAME           string   `xml:"NAME" json:"NAME"`
	RECEIPTCODE    string   `xml:"RECEIPTCODE" json:"RECEIPTCODE"`
	REGION         string   `xml:"REGION" json:"REGION"`
	ROUTINGKEY     string   `xml:"ROUTINGKEY" json:"ROUTINGKEY"`
	GC             string   `xml:"GC" json:"GC"`
	TAXOFFICE      string   `xml:"TAXOFFICE" json:"TAXOFFICE"`
	USERNAME       string   `xml:"USERNAME" json:"USERNAME"`
	PASSWORD       string   `xml:"PASSWORD" json:"PASSWORD"`
	TOKENPATH      string   `xml:"TOKENPATH" json:"TOKENPATH"`
	TAXCODES       TAXCODES `xml:"TAXCODES" json:"TAXCODES"`
	EFDMSSIGNATURE string   `xml:"EFDMSSIGNATURE" json:"EFDMSSIGNATURE"`
}

type TAXCODES struct {
	CODEA string `xml:"CODEA" json:"CODEA"`
	CODEB string `xml:"CODEB" json:"CODEB"`
	CODEC string `xml:"CODEC" json:"CODEC"`
	CODED string `xml:"CODED" json:"CODED"`
}

// <EFDMS>
//     <EFDMSRESP>
//         <ACKCODE>0</ACKCODE>
//         <ACKMSG>Registration Successful</ACKMSG>
//         <REGID>TZ0100552121</REGID>
//         <SERIAL>10TZ100483</SERIAL>
//         <UIN>09VFDWEBAPI-101317587*********10TZ100483</UIN>
//         <TIN>*********</TIN>
//         <VRN>NOT REGISTERED</VRN>
//         <MOBILE>**********</MOBILE>
//         <STREET>MWENGE</STREET>
//         <CITY>DAR ES SALAAM</CITY>
//         <COUNTRY>TANZANIA</COUNTRY>
//         <NAME>SIMON PARMENA SHILLAH</NAME>
//         <RECEIPTCODE>31BF7E</RECEIPTCODE>
//         <REGION>Kinondoni</REGION>
//         <ROUTINGKEY>vfdrct</ROUTINGKEY>
//         <GC>1</GC>
//         <TAXOFFICE>Tax Office Kinondoni</TAXOFFICE>
//         <USERNAME>babaaeid8490ngom</USERNAME>
//         <PASSWORD>ov=HV3TS1w2pofB(</PASSWORD>
//         <TOKENPATH>vfdtoken</TOKENPATH>
//         <TAXCODES>
//             <CODEA>18</CODEA>
//             <CODEB>0</CODEB>
//             <CODEC>0</CODEC>
//             <CODED>0</CODED>
//         </TAXCODES>
//     </EFDMSRESP>
//     <EFDMSSIGNATURE>MqCOrKrZVnY8qk4PkS8BFiyzcEVFYx+/8YbyZB2sRg6gDb8MDbt+HGtaHNDco5hHZEPpVV6QZhm63putHjWOp4AoyHN/LilhlvBL9QxfGqJFrj+opnLqWbmh7FS6d1G8dyCZX/or1aeOFicFzaKjv6WF3kofWO/GI7WXvVo3NysXUVb4mv9YQjiDAw48Elh7q4TDcqHU1b15z7I+oIQ3kC4gGDKvzJhOtbyIyIZhSM425lx/pjSb4haZqZvdFGcxJx/s49q/wKkDI9/Y1YyHnK/EjnByQABm1DLnwXBcAG+1b6Ro5j98IRgMcXKPs2FXyT448nWm1VCtdroRYdblNA==</EFDMSSIGNATURE>
// </EFDMS>
