package utils

import (
	"bytes"
	"crypto/tls"
	"fmt"
	"log"
	"compliance-and-risk-management-backend/app/entities"
	"net/smtp"
	"os"

	"gopkg.in/gomail.v2"
)

func SendInvoiceEmail_Test001(invoice *entities.Invoice) {

	// Only send emails on prod env
	m := gomail.NewMessage()
	m.<PERSON>eader("From", os.Getenv("SMTP_EMAIL"))

	recipients := []string{"<EMAIL>"}

	fmt.Printf("recipients=[%v]\n", recipients)
	m.SetHeader("To", recipients...)

	// m.SetAddressHeader("Cc", "<EMAIL>", "Dan")
	m.<PERSON>eader("Subject", "001 Garda World Security Limited Invoice #"+invoice.InvoiceNumber)

	emailText := "Dear Customer,<br/><br/>"
	emailText += "Thank you for giving Garda World Security Limited the opportunity to serve you.<br/>"
	emailText += "Enclosed please find your invoice no " + invoice.InvoiceNumber + ".<br/><br/>"
	emailText += "Please make your payments within 30 days to avoid overdue charges.<br/><br/>"
	emailText += "In case of any query, Please contact us on +255 784 555470.<br/>"
	emailText += "Thank you for choosing Garda World Security Limited as your preferred service provider<br/><br/>"
	emailText += "<br/>Regards<br/>Finance Team.<br/>Garda World Security Limited<br/>"
	// emailText += "<img src=\"https://www.sgasecurity.com//application/themes/sga/assets/img/logo.png\" alt=\"SGA\"/>"

	m.SetBody("text/html", emailText)
	// m.Attach("/home/<USER>/lolcat.jpg")
	m.Attach(invoice.FileName)

	port := os.Getenv("SMTP_PORT")
	username := os.Getenv("SMTP_USERNAME")
	password := os.Getenv("SMTP_PASSWORD")
	host := os.Getenv("SMTP_SERVER")
	portNum := ConvertStringToInt(port)

	d := gomail.NewDialer(host, portNum, username, password)
	// d := gomail.Dialer{Host:host, Port: 587, SSL: false, TLSConfig: nil}

	d.TLSConfig = &tls.Config{InsecureSkipVerify: true}

	// Send the email to Bob, Cora and Dan.
	if err := d.DialAndSend(m); err != nil {
		fmt.Printf("unable to send email [001], err=[%v]\n", err)
	} else {
		fmt.Printf("Email(s) sent for invoiceNo=[%v].\n", invoice.InvoiceNumber)
	}
}

func SendInvoiceEmail_Test002(invoice *entities.Invoice) {

	// Only send emails on prod env
	m := gomail.NewMessage()
	m.SetHeader("From", os.Getenv("SMTP_EMAIL"))

	recipients := []string{"<EMAIL>"}

	fmt.Printf("recipients=[%v]\n", recipients)
	m.SetHeader("To", recipients...)

	// m.SetAddressHeader("Cc", "<EMAIL>", "Dan")
	m.SetHeader("Subject", "002 Garda World Security Limited Invoice #"+invoice.InvoiceNumber)

	emailText := "Dear Customer,<br/><br/>"
	emailText += "Thank you for giving Garda World Security Limited the opportunity to serve you.<br/>"
	emailText += "Enclosed please find your invoice no " + invoice.InvoiceNumber + ".<br/><br/>"
	emailText += "Please make your payments within 30 days to avoid overdue charges.<br/><br/>"
	emailText += "In case of any query, Please contact us on +255 784 555470.<br/>"
	emailText += "Thank you for choosing Garda World Security Limited as your preferred service provider<br/><br/>"
	emailText += "<br/>Regards<br/>Finance Team.<br/>Garda World Security Limited<br/>"
	// emailText += "<img src=\"https://www.sgasecurity.com//application/themes/sga/assets/img/logo.png\" alt=\"SGA\"/>"

	m.SetBody("text/html", emailText)
	// m.Attach("/home/<USER>/lolcat.jpg")
	m.Attach(invoice.FileName)

	port := os.Getenv("SMTP_PORT")
	// username := os.Getenv("SMTP_USERNAME")
	// password := os.Getenv("SMTP_PASSWORD")
	host := os.Getenv("SMTP_SERVER")
	portNum := ConvertStringToInt(port)

	// d := gomail.NewDialer(host, portNum, username, password)
	d := gomail.Dialer{Host: host, Port: portNum, SSL: false, TLSConfig: nil}
	d.TLSConfig = &tls.Config{InsecureSkipVerify: true}

	// Send the email to Bob, Cora and Dan.
	if err := d.DialAndSend(m); err != nil {
		fmt.Printf("unable to send email [002], err=[%v]\n", err)
	} else {

		fmt.Printf("Email(s) sent for invoiceNo=[%v].\n", invoice.InvoiceNumber)
	}
}

func SendInvoiceEmail_Test003(invoice *entities.Invoice) {

	// port := os.Getenv("SMTP_PORT")
	username := os.Getenv("SMTP_USERNAME")
	password := os.Getenv("SMTP_PASSWORD")
	host := os.Getenv("SMTP_SERVER")
	// portNum := ConvertStringToInt(port)

	auth = smtp.PlainAuth("", username, password, host)
	templateData := struct {
		CustomerName  string
		InvoiceNumber string
	}{
		CustomerName:  "Gideon",
		InvoiceNumber: "https://google.com",
	}
	r := NewRequest([]string{"<EMAIL>"}, "003 Golang Email Test!", "Hello, World!")
	if err := r.ParseTemplate("templates/email_template.html", templateData); err == nil {
		ok, _ := r.SendEmail()
		log.Println(ok)
	} else {
		log.Println("Encountered error while sending email... [003]", err.Error())
	}
}

func SendInvoiceEmail_Test004(invoice *entities.Invoice) {
	port := os.Getenv("SMTP_PORT")
	username := os.Getenv("SMTP_USERNAME")
	// password := os.Getenv("SMTP_PASSWORD")
	host := os.Getenv("SMTP_SERVER")
	// portNum := ConvertStringToInt(port)

	mime := "MIME-version: 1.0;\nContent-Type: text/plain; charset=\"UTF-8\";\n\n"
	subject := "Subject: 004" + invoice.InvoiceNumber + "!\n"
	msg := []byte(subject + mime + "\nInvoice attachment.")
	addr := fmt.Sprintf("%v:%v", host, port) // "smtp.gmail.com:587"

	to := []string{"<EMAIL>"}

	if err := smtp.SendMail(addr, auth, username, to, msg); err != nil {
		fmt.Printf("error sending email [004], err=[%v]\n", err.Error())
	} else {
		fmt.Printf("Email sent. 004\n")
	}
}

func SendInvoiceEmail_Test005(invoice *entities.Invoice) {

	port := os.Getenv("SMTP_PORT")
	username := os.Getenv("SMTP_USERNAME")
	password := os.Getenv("SMTP_PASSWORD")
	host := os.Getenv("SMTP_SERVER")
	// portNum := ConvertStringToInt(port)

	smtp_server := host
	smtp_port := port

	senders_email := username
	senders_password := password

	recipient_email := "<EMAIL>"
	message := []byte("To: " + recipient_email + "\r\n" +
		"Subject: 005 Go SMTP Test\r\n" +
		"\r\n" +
		"Hello,\r\n" +
		"This is a test email sent from Go!\r\n")

	auth := smtp.PlainAuth("", senders_email, senders_password, smtp_server)

	err := smtp.SendMail(smtp_server+":"+smtp_port, auth, senders_email, []string{recipient_email}, message)
	if err != nil {
		fmt.Printf("err sending email [005], err=[%v]\n", err.Error())
	} else {
		fmt.Println("Email sent successfully!")
	}
}

func SendInvoiceEmail_Test006(invoice *entities.Invoice) {

	port := os.Getenv("SMTP_PORT")
	username := os.Getenv("SMTP_USERNAME")
	// password := os.Getenv("SMTP_PASSWORD")
	host := os.Getenv("SMTP_SERVER")

	addr := fmt.Sprintf("%v:%v", host, port) // "smtp.gmail.com:587"

	c, err := smtp.Dial(addr)
	if err != nil {
		log.Fatal(err)
	}
	defer c.Close()
	// Set the sender and recipient.
	c.Mail(username)
	c.Rcpt("<EMAIL>")
	// Send the email body.
	wc, err := c.Data()
	if err != nil {
		log.Fatal(err)
	}
	defer wc.Close()
	buf := bytes.NewBufferString("This is the email body. [006]")
	if _, err = buf.WriteTo(wc); err != nil {
		fmt.Printf("err sending email [006], err=[%v]\n", err.Error())
	} else {
		fmt.Println("Email sent successfully!")
	}
}
