package income_tracking

import (
	"context"
	"log"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/webutils"
)

func createIncomeTracking(
	transactionsDB *database.AppTransactionsDB,
	incomeTrackingService services.IncomeTrackingService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		var incomeTrackingForm forms.CreateIncomeTrackingForm
		err := ctx.Bind(&incomeTrackingForm)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind incomeTracking form while creating incomeTracking",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		incomeTracking, err := incomeTrackingService.CreateIncomeTracking(ctx.Request.Context(), transactionsDB, &incomeTrackingForm)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to save incomeTracking. Err=" + err.Error(),
			})
			return
		}

		ctx.JSON(http.StatusOK, incomeTracking)
	}
}

func filterIncomeTrackings(
	transactionsDB *database.AppTransactionsDB,
	incomeTrackingService services.IncomeTrackingService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering incomeTrackinges",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		incomeTrackingList, err := incomeTrackingService.FilterIncomeTrackings(context.Background(), transactionsDB, filter)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to filter incomeTrackinges",
			})
			return
		}

		ctx.JSON(http.StatusOK, incomeTrackingList)
	}
}

func getIncomeTrackingById(
	incomeTrackingService services.IncomeTrackingService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		incomeTrackingIDStr := ctx.Param("id")
		incomeTrackingID, err := strconv.ParseInt(incomeTrackingIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse incomeTrackingID=[%v], err=[%v]\n", incomeTrackingIDStr, err)
		}

		incomeTracking, err := incomeTrackingService.FindIncomeTrackingByID(ctx.Request.Context(), incomeTrackingID)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to find incomeTracking by id=[%v]",
				incomeTrackingIDStr,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, incomeTracking)
	}
}

func updateIncomeTracking(
	transactionsDB *database.AppTransactionsDB,
	IncomeTrackingService services.IncomeTrackingService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		incomeTrackingIDStr := ctx.Param("id")
		incomeTrackingID, err := strconv.ParseInt(incomeTrackingIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse incomeTrackingID=[%v], err=[%v]\n", incomeTrackingIDStr, err)
		}

		var form forms.UpdateIncomeTrackingForm
		err = ctx.Bind(&form)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind incomeTracking form while updating incomeTracking",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		incomeTracking, err := IncomeTrackingService.UpdateIncomeTracking(ctx.Request.Context(), transactionsDB, incomeTrackingID, &form)
		if err != nil {
			appError := utils.NewError(
				err,
				"Failed to update incomeTracking by id=[%v]",
				incomeTrackingIDStr,
			)

			webutils.HandleError(ctx, appError)
			return
		}

		ctx.JSON(http.StatusOK, incomeTracking)
	}
}
