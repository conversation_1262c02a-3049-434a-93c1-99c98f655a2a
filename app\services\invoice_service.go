package services

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"log"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/providers"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/ctxhelper"
	"strings"

	txns_db "compliance-and-risk-management-backend/app/database"
)

type InvoiceService interface {
	CreateInvoice(ctx context.Context, operations txns_db.TransactionsSQLOperations, invoice forms.InvoiceForm) (*entities.Invoice, error)
	DownloadInvoices(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter, filePath string) (*entities.InvoiceList, error)
	FilterInvoices(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter) (*entities.InvoiceList, error)
	FindInvoiceByID(ctx context.Context, invoiceID int64) (*entities.Invoice, error)
	GetInvoiceByInvoiceNumber(ctx context.Context, invoiceNumber string) (*entities.Invoice, error)
	SaveInvoice(ctx context.Context, operations txns_db.TransactionsSQLOperations, invoice *entities.Invoice) error
	SignInvoice(ctx context.Context, operations txns_db.TransactionsSQLOperations, form *forms.InvoiceForm) (*entities.Invoice, error)
}

type AppInvoiceService struct {
	invoiceItemRepository repos.InvoiceItemRepository
	invoiceRepository     repos.InvoiceRepository
	smtpSender            providers.SmtpSender
	storeRepository       repos.StoreRepository
	traVfdService         TRAVFDService
	userRepository        repos.UserRepository
}

func NewInvoiceService(
	invoiceItemRepository repos.InvoiceItemRepository,
	invoiceRepo repos.InvoiceRepository,
	smtpSender providers.SmtpSender,
	storeRepository repos.StoreRepository,
	traVfdService TRAVFDService,
	userRepository repos.UserRepository,
) InvoiceService {
	return &AppInvoiceService{
		invoiceItemRepository: invoiceItemRepository,
		invoiceRepository:     invoiceRepo,
		smtpSender:            smtpSender,
		storeRepository:       storeRepository,
		traVfdService:         traVfdService,
		userRepository:        userRepository,
	}
}

func (s *AppInvoiceService) CreateInvoice(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	form forms.InvoiceForm,
) (*entities.Invoice, error) {

	invoice := &entities.Invoice{}

	// do some basic validations
	if form.InvoiceNumber == "" {
		return invoice, errors.New("invoice service - invoiceNumber required")
	}

	invoice, err := s.invoiceRepository.GetInvoiceByInvoiceNumber(ctx, form.InvoiceNumber, 1)
	if err != nil && err != sql.ErrNoRows {
		// TODO : Update invoice if it already exists
		return invoice, errors.New("invoice already exists")
	}

	// do some basic normalisation
	invoice.GrossAmount = form.GrossAmount
	invoice.Vat = form.Vat
	invoice.GrandTotal = form.GrandTotal

	err = s.invoiceRepository.Save(ctx, operations, invoice)
	if err != nil {
		return invoice, err
	}

	return invoice, nil
}

func (s *AppInvoiceService) DownloadInvoices(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
	filePath string,
) (*entities.InvoiceList, error) {

	invoiceList := &entities.InvoiceList{}

	tokenInfo := ctxhelper.TokenInfo(ctx)

	defaultStore, err := s.storeRepository.GetUserDefaultStore(tokenInfo.UserID)
	if err != nil && !utils.IsErrNoRows(err) {
		log.Printf("failed to retrieve default store for user=[%v], err=[%v]\n", tokenInfo.UserID, err)
		return invoiceList, err
	}

	fmt.Printf("tokenInfo.UserID=[%v], defaultStore.ID=[%v] \n", tokenInfo.UserID, defaultStore.ID)

	filter.StoreID = defaultStore.ID

	count, err := s.invoiceRepository.CountInvoices(ctx, filter)
	if err != nil {
		return invoiceList, err
	}

	// Download all invoices in the system.
	filter.Offset = 0
	filter.Limit = 20000

	invoices, err := s.invoiceRepository.FilterInvoices(ctx, operations, filter)
	if err != nil {
		return invoiceList, err
	}

	invoiceList.Invoices = invoices

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	invoiceList.Pagination = pagination

	documentType := "Invoice"
	fmt.Printf("document type: [%v]\n", strings.ToUpper(filter.DocumentType))
	if strings.ToUpper(filter.DocumentType) == "CREDIT_NOTE" {
		documentType = "Credit Note"
	}

	utils.CreateAndAppendInvoiceDataToExcelFile(filePath, invoices, documentType)

	fmt.Printf("sending data export email, defaultStore=[%v]...\n", defaultStore.Name)

	subject := "Invoice Report Export"
	to := "<EMAIL>"

	items := map[string]string{
		"StoreName":         defaultStore.Name,
		"ReportType":        "Invoices Export",
		"StartDate":         filter.StartDate.Time.Format("2006-01-02"),
		"EndDate":           filter.EndDate.Time.Format("2006-01-02"),
		"TotalRecordsCount": fmt.Sprintf("%v", count),
		"DocumentType":      documentType,
	}

	// Send Email to admin with payment details.
	go s.smtpSender.SendEmailWithTemplate("optimus", "templates/reports-download.html", to, subject, items, nil)

	return invoiceList, nil
}

func (s *AppInvoiceService) FilterInvoices(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) (*entities.InvoiceList, error) {

	invoiceList := &entities.InvoiceList{}

	tokenInfo := ctxhelper.TokenInfo(ctx)
	user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	if err != nil {
		log.Printf("failed to find user by id=[%v], err=[%v]\n", tokenInfo.UserID, err)
		return invoiceList, err
	}
	tokenInfo.UserID = user.ID

	defaultStore, err := s.storeRepository.GetUserDefaultStore(tokenInfo.UserID)
	if err != nil {
		log.Printf("failed to retrieve default store for user=[%v], err=[%v]\n", tokenInfo.UserID, err)
		return nil, utils.NewErrorWithErrorCodeAndMessage(
			errors.New("failed to retrieve default store for user"),
			utils.ErrorCodeUnauthorized,
			"failed to retrieve default store for user",
			"cannot filter invoices for user with no store",
		)
	}

	filter.StoreID = defaultStore.ID

	count, err := s.invoiceRepository.CountInvoices(ctx, filter)
	if err != nil {
		return invoiceList, err
	}

	invoices, err := s.invoiceRepository.FilterInvoices(ctx, operations, filter)
	if err != nil {
		return invoiceList, err
	}

	invoiceList.Invoices = invoices

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	invoiceList.Pagination = pagination

	return invoiceList, nil
}

func (s *AppInvoiceService) FindInvoiceByID(
	ctx context.Context,
	invoiceID int64,
) (*entities.Invoice, error) {

	tokenInfo := ctxhelper.TokenInfo(ctx)
	user, err := s.userRepository.FindByID(ctx, tokenInfo.UserID)
	if err != nil {
		log.Printf("failed to find user by id=[%v], err=[%v]\n", tokenInfo.UserID, err)
		return &entities.Invoice{}, err
	}
	tokenInfo.UserID = user.ID

	defaultStore, err := s.storeRepository.GetUserDefaultStore(tokenInfo.UserID)
	if err != nil && utils.IsErrNoRows(err) {
		log.Printf("failed to retrieve default store for user=[%v], err=[%v]\n", tokenInfo.UserID, err)
		return &entities.Invoice{}, err
	}

	invoice, err := s.invoiceRepository.GetInvoiceByID(ctx, invoiceID, defaultStore.ID)
	if err != nil {
		return invoice, err
	}

	return invoice, nil
}

func (s *AppInvoiceService) GetInvoiceByInvoiceNumber(
	ctx context.Context,
	invoiceNumber string,
) (*entities.Invoice, error) {

	invoice, err := s.invoiceRepository.GetInvoiceByInvoiceNumber(ctx, invoiceNumber, 1)
	if err != nil {
		return invoice, err
	}

	return invoice, nil
}

func (s *AppInvoiceService) SaveInvoice(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	invoice *entities.Invoice,
) error {

	err := s.invoiceRepository.Save(ctx, operations, invoice)
	if err != nil {
		return err
	}

	return nil
}

func (s *AppInvoiceService) SignInvoice(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	form *forms.InvoiceForm,
) (*entities.Invoice, error) {

	if form.GrandTotal <= 0.00 {
		return &entities.Invoice{}, nil
	}

	invoice := &entities.Invoice{
		CustomerName:  form.CustomerName,
		CustomerTIN:   form.CustomerTIN,
		DC:            form.DC,
		GC:            form.GC,
		GrandTotal:    form.GrandTotal,
		GrossAmount:   form.GrossAmount,
		InvoiceNumber: form.InvoiceNumber,
		Vat:           form.Vat,
	}

	// Save invoice
	err := s.invoiceRepository.Save(ctx, operations, invoice)
	if err != nil {
		return invoice, err
	}

	invoiceItems := make([]*entities.InvoiceItem, 0)

	// Save invoice items
	for _, item := range form.Items {

		invoiceItem := &entities.InvoiceItem{
			InvoiceID:   invoice.ID,
			Description: item.Description,
			Quantity:    item.Quantity,
			TaxCode:     item.TaxCode,
			Total:       item.Total,
		}

		invoiceItems = append(invoiceItems, invoiceItem)
	}

	// Save invoice items
	err = s.invoiceItemRepository.SaveMultiple(ctx, operations, invoiceItems)
	if err != nil {
		return invoice, err
	}

	// invoice.Items = invoiceItems

	// Sign invoice
	err = s.traVfdService.SignInvoice(ctx, invoice)
	if err != nil {
		return invoice, err
	}

	// Update the invoice after signing it
	err = s.invoiceRepository.Save(ctx, operations, invoice)
	if err != nil {
		return invoice, err
	}

	return invoice, nil
}
