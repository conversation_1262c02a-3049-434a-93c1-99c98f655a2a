package providers

import (
	"fmt"
	"os"
	"reflect"
	"strconv"
	"compliance-and-risk-management-backend/app/utils"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/dynamodb"
	"github.com/aws/aws-sdk-go/service/dynamodb/dynamodbattribute"
)

type (
	DynamoDB interface {
		CreateItem(string, interface{}) error
		DeleteItem(string, *DynamoDBKey) error
		GetItem(string, *DynamoDBKey) (map[string]*dynamodb.AttributeValue, error)
		GetItems(tableName string, queryString string, mapAttributeValues map[string]*dynamodb.AttributeValue) ([]map[string]*dynamodb.AttributeValue, error)
		UpdateItem(string, *DynamoDBKey, *UpdateItem) error
	}

	AppDynamoDB struct {
		client *dynamodb.DynamoDB
	}

	DynamoDBKey struct {
		Name  string
		Value interface{}
	}

	UpdateItem struct {
		SetAttributeValues map[string]*dynamodb.AttributeValue
		RemoveAttributes   []string
	}
)

func NewDynamoDB() DynamoDB {
	return NewDynamoDBWithCredentials(
		os.Getenv("AWS_REGION"),
		os.Getenv("AWS_ACCESS_KEY_ID"),
		os.Getenv("AWS_SECRET_ACCESS_KEY"),
		os.Getenv("ENVIRONMENT"),
	)
}

func NewDynamoDBWithCredentials(
	region,
	accessKeyId,
	secretAccessKey,
	environment string,
) DynamoDB {

	config := &aws.Config{
		Region: aws.String(region),
		Credentials: credentials.NewStaticCredentials(
			accessKeyId,
			secretAccessKey,
			"",
		),
	}

	if isUsingLocalstack(environment) {
		config.Endpoint = aws.String(os.Getenv("LOCALSTACK_ENDPOINT"))
		config.S3ForcePathStyle = aws.Bool(true)
	}

	sess := session.Must(session.NewSession())
	client := dynamodb.New(sess, config)

	return &AppDynamoDB{client: client}
}

func (u *UpdateItem) setAndAddAttributesValues(dynamoDBUpdateItem *dynamodb.UpdateItemInput) {
	if len(u.SetAttributeValues) == 0 {
		return
	}

	if dynamoDBUpdateItem.ExpressionAttributeValues == nil {
		dynamoDBUpdateItem.ExpressionAttributeValues = map[string]*dynamodb.AttributeValue{}
	}

	if dynamoDBUpdateItem.UpdateExpression == nil {
		dynamoDBUpdateItem.UpdateExpression = aws.String("")
	}

	setIndex := 1
	updateExpression := "SET "
	for key, val := range u.SetAttributeValues {
		if setIndex == 1 {
			dynamoDBUpdateItem.ExpressionAttributeValues[fmt.Sprintf(":set%d", setIndex)] = val
			updateExpression += fmt.Sprintf("%s=:set%d", key, setIndex)
		} else {
			dynamoDBUpdateItem.ExpressionAttributeValues[fmt.Sprintf(":set%d", setIndex)] = val
			updateExpression += fmt.Sprintf(", %s=:set%d", key, setIndex)
		}

		setIndex++
	}

	if len(*dynamoDBUpdateItem.UpdateExpression) == 0 {
		dynamoDBUpdateItem.UpdateExpression = aws.String(*dynamoDBUpdateItem.UpdateExpression + updateExpression)
		return
	}

	dynamoDBUpdateItem.UpdateExpression = aws.String(*dynamoDBUpdateItem.UpdateExpression + " " + updateExpression)
}

func (u *UpdateItem) setRemoveAttributes(dynamoDBUpdateItem *dynamodb.UpdateItemInput) {
	if len(u.RemoveAttributes) == 0 {
		return
	}

	if dynamoDBUpdateItem.ExpressionAttributeNames == nil {
		dynamoDBUpdateItem.ExpressionAttributeNames = map[string]*string{}
	}

	removeIndex := 1
	updateExpression := "REMOVE "
	for _, val := range u.RemoveAttributes {
		if removeIndex == 1 {
			dynamoDBUpdateItem.ExpressionAttributeNames[fmt.Sprintf("#RM%d", removeIndex)] = aws.String(val)
			updateExpression += fmt.Sprintf("#RM%d", removeIndex)
		} else {
			dynamoDBUpdateItem.ExpressionAttributeNames[fmt.Sprintf("#RM%d", removeIndex)] = aws.String(val)
			updateExpression += fmt.Sprintf(", #RM%d", removeIndex)
		}

		removeIndex++
	}

	if len(*dynamoDBUpdateItem.UpdateExpression) == 0 {
		dynamoDBUpdateItem.UpdateExpression = aws.String(*dynamoDBUpdateItem.UpdateExpression + updateExpression)
		return
	}

	dynamoDBUpdateItem.UpdateExpression = aws.String(*dynamoDBUpdateItem.UpdateExpression + " " + updateExpression)
}

func (key *DynamoDBKey) DynamoDBKeyFormat() map[string]*dynamodb.AttributeValue {
	switch reflect.TypeOf(key.Value).String() {
	case "int64":
		return map[string]*dynamodb.AttributeValue{
			key.Name: {
				N: aws.String(strconv.FormatInt(key.Value.(int64), 10)),
			},
		}
	default:
		return map[string]*dynamodb.AttributeValue{
			key.Name: {
				S: aws.String(key.Value.(string)),
			},
		}
	}
}

func (db *AppDynamoDB) CreateItem(tableName string, payload interface{}) error {
	dataAttrVal, err := dynamodbattribute.MarshalMap(payload)
	if err != nil {
		return utils.NewError(
			err,
			"Failed to marshal dynamoDB document data.",
		)
	}

	input := &dynamodb.PutItemInput{
		Item:      dataAttrVal,
		TableName: aws.String(tableName),
	}

	_, err = db.client.PutItem(input)
	if err != nil {
		return utils.NewError(
			err,
			"Failed to create document on dynamoDB calling PutItem:",
		)
	}

	return nil
}

func (db *AppDynamoDB) DeleteItem(tableName string, key *DynamoDBKey) error {

	input := &dynamodb.DeleteItemInput{
		Key:       key.DynamoDBKeyFormat(),
		TableName: aws.String(tableName),
	}

	_, err := db.client.DeleteItem(input)
	if err != nil {
		return utils.NewError(
			err,
			"Failed to delete document on dynamoDB calling DeleteItem:",
		)
	}

	return nil
}

func (db *AppDynamoDB) GetItem(tableName string, key *DynamoDBKey) (map[string]*dynamodb.AttributeValue, error) {
	result, err := db.client.GetItem(&dynamodb.GetItemInput{
		TableName: aws.String(tableName),
		Key:       key.DynamoDBKeyFormat(),
	})

	if err != nil {
		return nil, utils.NewError(
			err,
			"Failed to get document on dynamoDB calling GetItem:",
		)
	}

	return result.Item, nil
}

// GetItems
// AWS has a page limit of 1MB which is a lot of data hence I don't see any need to paginate
func (db *AppDynamoDB) GetItems(tableName string, queryString string, mapAttributeValues map[string]*dynamodb.AttributeValue) ([]map[string]*dynamodb.AttributeValue, error) {
	result, err := db.client.Scan(&dynamodb.ScanInput{
		TableName:                 aws.String(tableName),
		FilterExpression:          aws.String(queryString),
		ExpressionAttributeValues: mapAttributeValues,
	})

	if err != nil {
		return nil, utils.NewError(
			err,
			"Failed to get documents on dynamoDB calling GetItems:",
		)
	}

	return result.Items, nil
}

func (db *AppDynamoDB) UpdateItem(tableName string, key *DynamoDBKey, updateItem *UpdateItem) error {

	input := &dynamodb.UpdateItemInput{
		Key:       key.DynamoDBKeyFormat(),
		TableName: aws.String(tableName),
	}

	updateItem.setAndAddAttributesValues(input)
	updateItem.setRemoveAttributes(input)

	_, err := db.client.UpdateItem(input)
	if err != nil {
		return utils.NewError(
			err,
			"Failed to update document on dynamoDB calling UpdateItem:",
		)
	}

	return nil
}
