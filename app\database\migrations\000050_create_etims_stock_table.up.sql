CREATE TABLE IF NOT EXISTS etims_stock (
    id             BIGSERIAL                   PRIMARY KEY, 
    tin            VARCHAR(120)                NOT NULL,    
    bhfId          VARCHAR(255)                NOT NULL,    
    sarNo          BIGINT,
    orgSarNo       BIGINT,
    regTyCd        VARCHAR(20),
    custTin        VARCHAR(120),
    custNm         VARCHAR(255),
    custBhfId      VARCHAR(255),
    sarTyCd        VARCHAR(120),
    ocrnDt         VARCHAR(120),
    totItemCnt     BIGINT,    
    totTaxblAmt    NUMERIC(18,2),
    totTaxAmt      NUMERIC(18,2),
    totAmt         NUMERIC(18,2),
    remark         TEXT,    
    regrNm         VARCHAR(255),
    regrId         VARCHAR(255),
    modrNm         VARCHAR(255),
    modrId         VARCHAR(255),
    uuid           VARCHAR(250)                NOT NULL, 
    store_id       BIGINT                      NOT NULL REFERENCES stores (id),
    created_at     TIMESTAMP WITH TIME ZONE    NOT NULL DEFAULT now(),
    updated_at     TIMESTAMP WITH TIME ZONE
);

CREATE UNIQUE INDEX IF NOT EXISTS etims_stock_uuid_uidx ON etims_stock(uuid);
