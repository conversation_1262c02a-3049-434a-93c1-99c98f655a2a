package utils

import (
	"encoding/json"
	"fmt"
	"strings"
	"compliance-and-risk-management-backend/app/entities"
)

// PETROL          3000    2795    2801
// KEROSENE                2000    3185    1542

func PrintInvoiceItemsOutNicely(invoiceItems []*entities.InvoiceItem) string {

	invoiceContent := ""
	itemNameLen := 20

	for _, item := range invoiceItems {

		// PrintStructToConsole(item)

		itemName := strings.TrimSpace(item.Name)

		numSpacesToAdd := itemNameLen - len(itemName)
		for i := 0; i < numSpacesToAdd; i++ {
			itemName += " "
		}

		serialNoLen := len(strings.TrimSpace(item.SerialNumber))

		if serialNoLen > 0 {
			// SNO|QUANTITY |ITEM DESCRIPTION | RATE | VAT | AMOUNT
			invoiceContent += fmt.Sprintf("%v\t%v\t%v\t%v\t%v\t%v\n", item.SerialNumber, item.Quantity, itemName, item.Rate, item.Vat, item.Total)
		}
	}
	return invoiceContent
}

func PrintStructToConsole(structObe interface{}) {
	jsonData, err := json.Marshal(structObe)
	if err != nil {
		fmt.Println("Error marshaling JSON:", err)
		return
	}

	// Convert JSON data to byte array
	byteArray := []byte(jsonData)

	PrettyPrintJSON([]byte(byteArray))
}
