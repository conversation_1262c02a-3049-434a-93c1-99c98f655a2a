package repos

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
)

const (
	countSuppliersSQL = `SELECT COUNT(*) AS count FROM suppliers WHERE 1=1`

	createSupplierSQL = ` INSERT INTO suppliers (name, contact_person, email, phone_number, organization_id, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING id`

	selectSupplierByEmailAndOrganizationSQL = selectSupplierSQL + ` WHERE email=$1 AND organization_id=$2`

	selectSupplierByIDSQL = selectSupplierSQL + ` WHERE id=$1`

	selectSupplierSQL = `SELECT id, name, contact_person, email, phone_number, organization_id, created_at, updated_at FROM suppliers`

	updateSupplierSQL = `UPDATE suppliers SET name=$1, contact_person=$2, email=$3, phone_number=$4, updated_at=$5 WHERE id=$6`
)

type (
	SupplierRepository interface {
		CountSuppliers(context.Context, *entities.PaginationFilter) int
		FilterSuppliers(context.Context, txns_db.TransactionsSQLOperations, *entities.PaginationFilter) ([]*entities.Supplier, error)
		FindByEmailAndOganization(context.Context, string, int64) (*entities.Supplier, error)
		FindByID(context.Context, int64) (*entities.Supplier, error)
		GetSupplierByID(int64) (*entities.Supplier, error)
		Save(context.Context, *entities.Supplier) error
	}

	AppSupplierRepository struct {
		db *sql.DB
	}
)

func NewSupplierRepository(db *sql.DB) SupplierRepository {
	return &AppSupplierRepository{db: db}
}

func (r *AppSupplierRepository) CountSuppliers(
	ctx context.Context,
	filter *entities.PaginationFilter,
) int {
	var count int
	query, args, _ := r.buildQueryFromFilter(countSuppliersSQL, filter, []interface{}{}, 1)
	row := r.db.QueryRowContext(ctx, query, args...)
	row.Scan(&count)
	return count
}

func (r *AppSupplierRepository) FilterSuppliers(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) ([]*entities.Supplier, error) {

	categories := make([]*entities.Supplier, 0)

	args := make([]interface{}, 0)
	query := selectSupplierSQL
	currentIndex := 1

	query, args, currentIndex = r.buildQueryFromFilter(query, filter, args, currentIndex)
	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return categories, err
	}

	defer rows.Close()

	for rows.Next() {
		invoice, err := r.scanRowIntoSupplier(rows)
		if err != nil {
			return categories, err
		}

		categories = append(categories, invoice)
	}

	return categories, rows.Err()
}

func (r *AppSupplierRepository) FindByEmailAndOganization(
	ctx context.Context,
	name string,
	organizationID int64,
) (*entities.Supplier, error) {
	row := r.db.QueryRow(selectSupplierByEmailAndOrganizationSQL, name, organizationID)
	return r.scanRowIntoSupplier(row)
}

func (r *AppSupplierRepository) FindByID(
	ctx context.Context,
	supplierID int64,
) (*entities.Supplier, error) {
	row := r.db.QueryRow(selectSupplierByIDSQL, supplierID)
	return r.scanRowIntoSupplier(row)
}

func (r *AppSupplierRepository) GetSupplierByID(supplierID int64) (*entities.Supplier, error) {
	row := r.db.QueryRow(selectSupplierByIDSQL, supplierID)
	return r.scanRowIntoSupplier(row)
}

func (r *AppSupplierRepository) Save(
	ctx context.Context,
	supplier *entities.Supplier,
) error {

	supplier.Timestamps.Touch()
	var err error

	if supplier.IsNew() {
		err = r.db.QueryRow(
			createSupplierSQL,
			supplier.Name,
			supplier.ContactPerson,
			supplier.Email,
			supplier.PhoneNumber,
			supplier.OrganizationID,
			supplier.CreatedAt,
			supplier.UpdatedAt,
		).Scan(&supplier.ID)

	} else {

		_, err = r.db.Exec(
			updateSupplierSQL,
			supplier.Name,
			supplier.ContactPerson,
			supplier.Email,
			supplier.PhoneNumber,
			supplier.UpdatedAt,
			supplier.ID,
		)
	}

	if err != nil {
		log.Printf("error saving supplier, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppSupplierRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	return query, args, currentIndex
}

func (r *AppSupplierRepository) scanRowIntoSupplier(
	rowScanner txns_db.RowScanner,
) (*entities.Supplier, error) {

	var supplier entities.Supplier

	err := rowScanner.Scan(
		&supplier.ID,
		&supplier.Name,
		&supplier.ContactPerson,
		&supplier.Email,
		&supplier.PhoneNumber,
		&supplier.OrganizationID,
		&supplier.CreatedAt,
		&supplier.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning supplier,  err=[%v]\n", err.Error())
		return &supplier, err
	}

	return &supplier, nil
}
