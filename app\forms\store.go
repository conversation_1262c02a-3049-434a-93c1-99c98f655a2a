package forms

type (
	CreateStoreForm struct {
		AvatarURL         string `json:"avatar_url"`
		CountryCode       string `json:"country_code" default:"KE"`
		Email             string `json:"email"`
		EtimsBranch       string `json:"etims_branch"`
		EtimsDeviceSerial string `json:"etims_device_serial"`
		EtimsEnvironment  string `json:"etims_environment"`
		Location          string `json:"location"`
		Name              string `json:"name"`
		OrganizationID    int64  `json:"organization_id"`
		PhoneNumber       string `json:"phone_number"`
		PIN               string `json:"pin"`
		VAT               string `json:"vat"`
		Website           string `json:"website"`
		UserID            int64  `json:"user_id"`
	}

	UpdateStoreForm struct {
		AvatarURL         string `json:"avatar_url"`
		CountryCode       string `json:"country_code"`
		Email             string `json:"email"`
		EtimsBranch       string `json:"etims_branch"`
		EtimsDeviceSerial string `json:"etims_device_serial"`
		EtimsEnvironment  string `json:"etims_environment"`
		Location          string `json:"location"`
		Name              string `json:"name"`
		OrganizationID    int64  `json:"organization_id"`
		PhoneNumber       string `json:"phone_number"`
		PIN               string `json:"pin"`
		VAT               string `json:"vat"`
		Website           string `json:"website"`
		UserID            int64  `json:"user_id"`
	}

	UpdateStoreLicenseForm struct {
		IsLicensed bool `json:"is_licensed"`
	}
)
