package repos

import (
	"context"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/testutils"
	"os"
	"testing"

	esd_db "compliance-and-risk-management-backend/app/database"

	"github.com/joho/godotenv"
	. "github.com/smartystreets/goconvey/convey"
)

func TestAskactivityRepository(t *testing.T) {

	// TODO: Setup env picking properly
	err := godotenv.Load("../../.env")
	if err != nil {
		panic("unable to load .env file!")
	}

	// Run migrations in test db

	databaseURL := os.Getenv("TEST_DATABASE_URL")
	transactionsDB := esd_db.InitTransactionsSQLTestDB(databaseURL)

	testTxnsDB := esd_db.InitTransactionsTestDB()
	defer testTxnsDB.Close()

	ctx := context.Background()

	activityRepository := NewActivityRepository(transactionsDB)
	userRepository := NewUserRepository(transactionsDB)

	Convey("ActivityRepository", t, testutils.WithTestDBs(ctx, testTxnsDB,
		func(ctx context.Context, txnsDB esd_db.TransactionsDB) {

			// Setup a user
			user := &entities.User{}

			err := userRepository.Save(ctx, user)
			So(err, ShouldBeNil)

			// Setup an activity
			activity := &entities.Activity{
				Description: "Things to do over the weekend",
			}

			err = activityRepository.CreateActivity(ctx, activity)
			So(err, ShouldBeNil)
		},
	))
}
