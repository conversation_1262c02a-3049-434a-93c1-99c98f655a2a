package repos

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
)

const (
	countOrganizationsSQL = `SELECT COUNT(*) AS count FROM organizations WHERE 1=1`

	createOrganizationSQL = ` INSERT INTO organizations (name, address, branch, contact_person, email, password, 
		created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING id`

	selectDefaultOrganizationSQL = selectOrganizationSQL + ` ORDER BY id ASC LIMIT 1`

	selectOrganizationByEmailSQL = selectOrganizationSQL + ` WHERE email=$1`

	selectOrganizationByIDSQL = selectOrganizationSQL + ` WHERE id=$1`

	selectOrganizationSQL = `SELECT id, name, address, branch, contact_person, email, password, created_at, updated_at FROM organizations`
)

type (
	OrganizationRepository interface {
		CountOrganizations(ctx context.Context, filter *entities.QueryFilter) int
		CreateOrganization(organization *entities.Organization) error
		FindByEmail(ctx context.Context, email string) (*entities.Organization, error)
		FindByID(ctx context.Context, organizationID int64) (*entities.Organization, error)
		GetDefaultOrganization(ctx context.Context, operations db.TransactionsSQLOperations) (*entities.Organization, error)
		GetOrganizationByID(organizationID int64) (*entities.Organization, error)
		Save(ctx context.Context, organization *entities.Organization) error
	}

	AppOrganizationRepository struct {
		db *sql.DB
	}
)

func NewOrganizationRepository(db *sql.DB) OrganizationRepository {
	return &AppOrganizationRepository{db: db}
}

func (r *AppOrganizationRepository) CountOrganizations(
	ctx context.Context,
	filter *entities.QueryFilter,
) int {
	var count int
	query, args, _ := r.buildQueryFromFilter(countOrganizationsSQL, filter, []interface{}{}, 1)
	row := r.db.QueryRowContext(ctx, query, args...)
	row.Scan(&count)
	return count
}

func (r *AppOrganizationRepository) CreateOrganization(
	organization *entities.Organization,
) error {

	organization.Timestamps.Touch()

	err := r.db.QueryRow(
		createOrganizationSQL,
		organization.Name,
		organization.Address,
		organization.Branch,
		organization.ContactPerson,
		organization.Email,
		organization.Password,
		organization.CreatedAt,
		organization.UpdatedAt,
	).Scan(&organization.ID)

	if err != nil {
		log.Printf("error saving organization, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppOrganizationRepository) FindByEmail(
	ctx context.Context,
	email string,
) (*entities.Organization, error) {
	row := r.db.QueryRow(selectOrganizationByEmailSQL, email)
	return r.scanRowIntoOrganization(row)
}

func (r *AppOrganizationRepository) FindByID(
	ctx context.Context,
	organizationID int64,
) (*entities.Organization, error) {
	row := r.db.QueryRow(selectOrganizationByIDSQL, organizationID)
	return r.scanRowIntoOrganization(row)
}

func (r *AppOrganizationRepository) GetDefaultOrganization(
	ctx context.Context,
	operations db.TransactionsSQLOperations,
) (*entities.Organization, error) {
	row := operations.QueryRowContext(ctx, selectDefaultOrganizationSQL)
	return r.scanRowIntoOrganization(row)
}

func (r *AppOrganizationRepository) GetOrganizationByID(organizationID int64) (*entities.Organization, error) {
	row := r.db.QueryRow(selectOrganizationByIDSQL, organizationID)
	return r.scanRowIntoOrganization(row)
}

func (r *AppOrganizationRepository) Save(
	ctx context.Context,
	organization *entities.Organization,
) error {

	organization.Timestamps.Touch()

	err := r.db.QueryRow(
		createOrganizationSQL,
		organization.Name,
		organization.Address,
		organization.Branch,
		organization.ContactPerson,
		organization.Email,
		organization.Password,
		organization.CreatedAt,
		organization.UpdatedAt,
	).Scan(&organization.ID)

	if err != nil {
		log.Printf("error saving organization, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppOrganizationRepository) buildQueryFromFilter(
	query string,
	filter *entities.QueryFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	return query, args, currentIndex
}

func (r *AppOrganizationRepository) scanRowIntoOrganization(
	row *sql.Row,
) (*entities.Organization, error) {

	var organization entities.Organization

	err := row.Scan(
		&organization.ID,
		&organization.Name,
		&organization.Address,
		&organization.Branch,
		&organization.ContactPerson,
		&organization.Email,
		&organization.Password,
		&organization.CreatedAt,
		&organization.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning organization,  err=[%v]\n", err.Error())
		return &organization, err
	}

	return &organization, nil
}
