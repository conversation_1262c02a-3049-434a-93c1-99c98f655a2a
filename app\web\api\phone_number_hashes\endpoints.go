package phone_number_hashes

import (
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/web/auth"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	apiKeyRepository repos.APIKeyRepository,
	phoneNumberHasheservice services.PhoneNumberHashService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyActiveUser(transactionsDB, sessionAuthenticator, sessionService))
	{
		protectedAPI.GET("/phone_number_hashes/phone_number/:phoneNumber", getPhoneNumberHashByPhoneNumber(transactionsDB, phoneNumberHasheservice))
		protectedAPI.GET("/phone_number_hashes/hash/:hash", getPhoneNumberFromHash(transactionsDB, phoneNumberHasheservice))
	}

	adminAPI := routerGroup.Group("").Use(middleware.AllowOnlyAdmin(transactionsDB, sessionAuthenticator, sessionService))
	{
		adminAPI.POST("/phone_number_hashes", createPhoneNumberHash(transactionsDB, phoneNumberHasheservice))
		adminAPI.POST("/phone_number_hashes/upload", uploadMultiplePhoneNumberHashes(transactionsDB, phoneNumberHasheservice))
		adminAPI.GET("/phone_number_hashes", filterphoneNumberHashes(transactionsDB, phoneNumberHasheservice))
		adminAPI.GET("/phone_number_hashes/:id", getPhoneNumberHashById(phoneNumberHasheservice))
		adminAPI.PUT("/phone_number_hashes/:id", updatePhoneNumberHash(transactionsDB, phoneNumberHasheservice))
	}

	userAPI := routerGroup.Group("").Use(middleware.AllowOnlyApiKeyUser(transactionsDB, apiKeyRepository))
	{
		userAPI.GET("/hashes/phone_number/:phoneNumber", getPhoneNumberHashByPhoneNumber(transactionsDB, phoneNumberHasheservice))
		userAPI.GET("/hashes/hash/:hash", getPhoneNumberFromHash(transactionsDB, phoneNumberHasheservice))
	}
}
