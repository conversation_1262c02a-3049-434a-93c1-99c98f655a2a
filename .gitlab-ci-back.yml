stages:
  - build
  - deploy

variables:
  APP_NAME: "optimus-etims-pos-backend"
  APP_DIR: "/home/<USER>/Apps/StrutsPOS/compliance-and-risk-management-backend"
  GOLANG_VERSION: "1.22.2"
  SSH_HOST: "**************"
  SSH_USER: "gidemn"

before_script:
  - apt-get update -qq && apt-get install -y -qq ssh

build:
  stage: build
  image: golang:${GOLANG_VERSION}
  script:
    - go build -o ${APP_NAME}

deploy:
  stage: deploy
  before_script:
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - ssh-keyscan $SSH_HOST >> ~/.ssh/known_hosts
  script:
    - ssh ${SSH_USER}@${SSH_HOST} "cd ${APP_DIR} && ./deploy.sh"
  only:
    - main
