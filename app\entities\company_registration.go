package entities

type (
	CompanyRegistration struct {
		SequentialIdentifier
		RegID          string `json:"reg_id"`
		Serial         string `json:"serial"`
		UIN            string `json:"uin"`
		TIN            string `json:"tin"`
		VRN            string `json:"vrn"`
		Mobile         string `json:"mobile"`
		Street         string `json:"street"`
		City           string `json:"city"`
		Country        string `json:"country"`
		Name           string `json:"name"`
		ReceiptCode    string `json:"receipt_code"`
		Region         string `json:"region"`
		RoutingKey     string `json:"routing_key"`
		GC             string `json:"gc"`
		TaxOffice      string `json:"tax_office"`
		Username       string `json:"username"`
		Password       string `json:"password"`
		TokenPath      string `json:"token_path"`
		EFDMSSignature string `json:"efdms_signature"`
		Timestamps
	}

	CompanyRegistrationList struct {
		CompanyRegistrations []*CompanyRegistration `json:"company_registrations"`
		PaginationFilter     PaginationFilter       `json:"pagination_filter"`
	}
)
