CREATE TABLE IF NOT EXISTS receivings (
    id                BIGSERIAL                   PRIMARY KEY,    
    item_id           BIGINT                      NOT NULL REFERENCES items (id),
    quantity          NUMERIC(15,2)               NOT NULL,
    cost_price        NUMERIC(15,2)               NOT NULL,  
    total             NUMERIC(15,2)               NOT NULL,
    received_at       TIMESTAMP WITH TIME ZONE    DEFAULT now() NOT NULL, 
    created_by        BIGINT                      NOT NULL REFERENCES users (id),
    supplier_id       BIGINT                      REFERENCES suppliers (id),
    organization_id   BIGINT                      NOT NULL REFERENCES organizations (id),
    description       TEXT,
    deleted_at        TIMESTAMP,
    created_at        TIMESTAMP WITH TIME ZONE    DEFAULT now() NOT NULL, 
    updated_at        TIMESTAMP WITH TIME ZONE 
);

