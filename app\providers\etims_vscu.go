package providers

import (
	"encoding/json"
	"fmt"
	"io"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/logger"
	"compliance-and-risk-management-backend/app/utils"

	"net/http"
	"os"
	"strings"
)

const lastReqDt = "20180520000000"

type (
	EtimsVSCU interface {
		CreateStockItem(form *forms.CreateEtimsStockItemsForm) (*entities.EtimsInfoResponse, error)
		CreateStockItems(form *forms.CreateEtimsStockItemsForm) (*entities.EtimsInfoResponse, error)
		SaveItem(form *forms.CreateEtimsItemForm) (*entities.EtimsItemCreationResponse, error)
		SaveSale(form *forms.CreateEtimsSalesForm) (*entities.EtimsSaveSaleResponse, error)
		SelectBranches() (*entities.EtimsBrancesResponse, error)
		SelectCodes() (*entities.EtimsCodesResponse, error)
		SelectCustomer(customerPIN string) (*entities.EtimsCodesResponse, error)
		SelectInfo() (*entities.EtimsInfoResponse, error)
		SelectItems() (*entities.EtimsSelectItemsResponse, error)
		SelectItemsClass() (*entities.EtimsItemClassResponse, error)
		SelectNotices() (*entities.EtimsNoticesResponse, error)
	}

	AppEtimsVSCU struct {
		apiBaseURL         string
		branchID           string
		deviceSerialNumber string
		pinNumber          string
	}
)

func NewEtimsVSCU(etimsAPIBaseURL string) EtimsVSCU {
	appEtimsVSCU := &AppEtimsVSCU{
		apiBaseURL:         etimsAPIBaseURL,
		branchID:           os.Getenv("ETIMS_BRANCH_ID"),
		deviceSerialNumber: os.Getenv("ETIMS_DEVICE_SERIAL_NUMBER"),
		pinNumber:          os.Getenv("ETIMS_PIN_NUMBER"),
	}

	return appEtimsVSCU
}

func NewEtimsVSCUWithParams(
	etimsAPIBaseURL string,
	etimsBranchID string,
	etimsDeviceSerialNumber string,
	businessPinNumber string,
) EtimsVSCU {
	appEtimsVSCU := &AppEtimsVSCU{
		apiBaseURL:         etimsAPIBaseURL,
		branchID:           etimsBranchID,
		deviceSerialNumber: etimsDeviceSerialNumber,
		pinNumber:          businessPinNumber,
	}

	return appEtimsVSCU
}

func (s *AppEtimsVSCU) CreateStockItem(
	form *forms.CreateEtimsStockItemsForm,
) (*entities.EtimsInfoResponse, error) {

	response := &entities.EtimsInfoResponse{}

	apiURL := fmt.Sprintf("%v/stock/saveStockItems", s.apiBaseURL)

	payloadBytes, err := json.Marshal(form)
	if err != nil {
		logger.Errorf("failed to marshal CreateStockItems form payload, err=[%v]\n", err)
		return response, err
	}
	payload := strings.NewReader(string(payloadBytes))

	req, err := http.NewRequest("POST", apiURL, payload)
	if err != nil {
		return response, utils.NewError(
			err,
			"unable to create CreateStockItems request",
		)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return response, utils.NewError(
			err,
			"unable to process CreateStockItems request",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return response, utils.NewError(
			err,
			"unable to process CreateStockItems response body.",
		)
	}

	responseBodyString := string(body)
	utils.PrettyPrintJSON(body)
	err = json.Unmarshal([]byte(responseBodyString), &response)
	if err != nil {
		return response, utils.NewError(
			err,
			"unable to Unmarshal CreateStockItems response body.",
		)
	}

	return response, nil
}

func (s *AppEtimsVSCU) CreateStockItems(
	form *forms.CreateEtimsStockItemsForm,
) (*entities.EtimsInfoResponse, error) {

	response := &entities.EtimsInfoResponse{}

	apiURL := fmt.Sprintf("%v/stock/saveStockItems", s.apiBaseURL)

	payloadBytes, err := json.Marshal(form)
	if err != nil {
		logger.Errorf("failed to marshal CreateStockItems form payload, err=[%v]\n", err)
		return response, err
	}
	payload := strings.NewReader(string(payloadBytes))

	req, err := http.NewRequest("POST", apiURL, payload)
	if err != nil {
		return response, utils.NewError(
			err,
			"unable to create CreateStockItems request",
		)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return response, utils.NewError(
			err,
			"unable to process CreateStockItems request",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return response, utils.NewError(
			err,
			"unable to process CreateStockItems response body.",
		)
	}

	responseBodyString := string(body)
	utils.PrettyPrintJSON(body)
	err = json.Unmarshal([]byte(responseBodyString), &response)
	if err != nil {
		return response, utils.NewError(
			err,
			"unable to Unmarshal CreateStockItems response body.",
		)
	}

	return response, nil
}

func (s *AppEtimsVSCU) SaveItem(
	form *forms.CreateEtimsItemForm,
) (*entities.EtimsItemCreationResponse, error) {

	response := &entities.EtimsItemCreationResponse{}

	apiURL := fmt.Sprintf("%v/items/saveItems", s.apiBaseURL)

	payloadBytes, err := json.Marshal(form)
	if err != nil {
		logger.Errorf("failed to marshal SaveItem form payload, err=[%v]\n", err)
		return response, err
	}
	payload := strings.NewReader(string(payloadBytes))

	req, err := http.NewRequest("POST", apiURL, payload)
	if err != nil {
		return response, utils.NewError(
			err,
			"unable to create SaveItem request",
		)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return response, utils.NewError(
			err,
			"unable to process SaveItem request",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return response, utils.NewError(
			err,
			"unable to process SaveItem response body.",
		)
	}

	responseBodyString := string(body)

	fmt.Printf("--------------------------- Create ETIMS Item Response ----------------------------")
	utils.PrettyPrintJSON(body)
	fmt.Printf("--------------------------- Create ETIMS Item Response ----------------------------")

	err = json.Unmarshal([]byte(responseBodyString), &response)
	if err != nil {
		return response, utils.NewError(
			err,
			"unable to Unmarshal response body",
		)
	}

	return response, nil
}

func (s *AppEtimsVSCU) SaveSale(
	form *forms.CreateEtimsSalesForm,
) (*entities.EtimsSaveSaleResponse, error) {

	response := &entities.EtimsSaveSaleResponse{}

	apiURL := fmt.Sprintf("%v/trnsSales/saveSales", s.apiBaseURL)

	fmt.Printf("SaveSale apiURL=[%v]\n", apiURL)

	payloadBytes, err := json.Marshal(form)
	if err != nil {
		logger.Errorf("failed to marshal SaveSale form payload, err=[%v]\n", err)
		return response, err
	}
	payload := strings.NewReader(string(payloadBytes))

	req, err := http.NewRequest("POST", apiURL, payload)
	if err != nil {
		return response, utils.NewError(
			err,
			"unable to create SaveSale request",
		)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return response, utils.NewError(
			err,
			"unable to process SaveSale request",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return response, utils.NewError(
			err,
			"unable to process SaveSale response body.",
		)
	}

	responseBodyString := string(body)
	utils.PrettyPrintJSON(body)
	err = json.Unmarshal([]byte(responseBodyString), &response)
	if err != nil {
		return response, utils.NewError(
			err,
			"unable to Unmarshal response body",
		)
	}

	return response, nil
}

func (s *AppEtimsVSCU) SelectBranches() (*entities.EtimsBrancesResponse, error) {

	branches := &entities.EtimsBrancesResponse{}

	apiURL := fmt.Sprintf("%v/branches/selectBranches", s.apiBaseURL)

	form := forms.CreateTinBranchLastRequstForm{
		Tin:       s.pinNumber,
		BhfId:     s.branchID,
		LastReqDt: lastReqDt,
	}

	payloadBytes, err := json.Marshal(form)
	if err != nil {
		logger.Errorf("failed to marshal SelectBranches form payload, err=[%v]\n", err)
		return branches, err
	}
	payload := strings.NewReader(string(payloadBytes))

	req, err := http.NewRequest("POST", apiURL, payload)
	if err != nil {
		return branches, utils.NewError(
			err,
			"unable to create SelectBranches request",
		)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return branches, utils.NewError(
			err,
			"unable to process SelectBranches request",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return branches, utils.NewError(
			err,
			"unable to process SelectBranches response body.",
		)
	}

	responseBodyString := string(body)
	utils.PrettyPrintJSON(body)
	err = json.Unmarshal([]byte(responseBodyString), &branches)
	if err != nil {
		return branches, utils.NewError(
			err,
			"unable to Unmarshal response body",
		)
	}

	return branches, nil
}

func (s *AppEtimsVSCU) SelectCodes() (*entities.EtimsCodesResponse, error) {

	info := &entities.EtimsCodesResponse{}

	apiURL := fmt.Sprintf("%v/code/selectCodes", s.apiBaseURL)

	form := forms.CreateTinBranchLastRequstForm{
		Tin:       s.pinNumber,
		BhfId:     s.branchID,
		LastReqDt: lastReqDt,
	}

	payloadBytes, err := json.Marshal(form)
	if err != nil {
		logger.Errorf("failed to marshal SelectCodes form payload, err=[%v]\n", err)
		return info, err
	}
	payload := strings.NewReader(string(payloadBytes))

	req, err := http.NewRequest("POST", apiURL, payload)
	if err != nil {
		return info, utils.NewError(
			err,
			"unable to create SelectCodes request",
		)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return info, utils.NewError(
			err,
			"unable to process SelectCodes request",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return info, utils.NewError(
			err,
			"unable to process SelectCodes response body.",
		)
	}

	responseBodyString := string(body)
	utils.PrettyPrintJSON(body)
	err = json.Unmarshal([]byte(responseBodyString), &info)
	if err != nil {
		return info, utils.NewError(
			err,
			"unable to Unmarshal response body",
		)
	}

	return info, nil
}

func (s *AppEtimsVSCU) SelectCustomer(customerPIN string) (*entities.EtimsCodesResponse, error) {

	info := &entities.EtimsCodesResponse{}

	apiURL := fmt.Sprintf("%v/customers/selectCustomer", s.apiBaseURL)

	form := forms.CreateTinBranchCustomerTINForm{
		Tin:      s.pinNumber,
		BhfId:    s.branchID,
		CustmTin: customerPIN,
	}

	payloadBytes, err := json.Marshal(form)
	if err != nil {
		logger.Errorf("failed to marshal SelectCustomer form payload, err=[%v]\n", err)
		return info, err
	}
	payload := strings.NewReader(string(payloadBytes))

	req, err := http.NewRequest("POST", apiURL, payload)
	if err != nil {
		return info, utils.NewError(
			err,
			"unable to create SelectCustomer request",
		)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return info, utils.NewError(
			err,
			"unable to process SelectCustomer request",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return info, utils.NewError(
			err,
			"unable to process SelectCustomer response body.",
		)
	}

	responseBodyString := string(body)
	utils.PrettyPrintJSON(body)
	err = json.Unmarshal([]byte(responseBodyString), &info)
	if err != nil {
		return info, utils.NewError(
			err,
			"unable to Unmarshal response body",
		)
	}

	return info, nil
}

func (s *AppEtimsVSCU) SelectInfo() (*entities.EtimsInfoResponse, error) {

	info := &entities.EtimsInfoResponse{}

	apiURL := fmt.Sprintf("%v/initializer/selectInitInfo", s.apiBaseURL)

	form := forms.CreateSelectInfoForm{
		Tin:      s.pinNumber,
		BhfId:    s.branchID,
		DvcSrlNo: s.deviceSerialNumber,
	}

	payloadBytes, err := json.Marshal(form)
	if err != nil {
		logger.Errorf("failed to marshal selectInitInfo form payload, err=[%v]\n", err)
		return info, err
	}
	payload := strings.NewReader(string(payloadBytes))

	req, err := http.NewRequest("POST", apiURL, payload)
	if err != nil {
		return info, utils.NewError(
			err,
			"unable to create selectInfo request",
		)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return info, utils.NewError(
			err,
			"unable to process SelectInfo request",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return info, utils.NewError(
			err,
			"unable to process SelectInfo response body.",
		)
	}

	responseBodyString := string(body)
	utils.PrettyPrintJSON(body)
	err = json.Unmarshal([]byte(responseBodyString), &info)
	if err != nil {
		return info, utils.NewError(
			err,
			"unable to unmarshal response body",
		)
	}

	return info, nil
}

func (s *AppEtimsVSCU) SelectItems() (*entities.EtimsSelectItemsResponse, error) {

	items := &entities.EtimsSelectItemsResponse{}

	apiURL := fmt.Sprintf("%v/items/selectItems", s.apiBaseURL)

	form := forms.CreateTinBranchLastRequstForm{
		Tin:       s.pinNumber,
		BhfId:     s.branchID,
		LastReqDt: lastReqDt,
	}

	payloadBytes, err := json.Marshal(form)
	if err != nil {
		logger.Errorf("failed to marshal SelectItems form payload, err=[%v]\n", err)
		return items, err
	}
	payload := strings.NewReader(string(payloadBytes))

	req, err := http.NewRequest("POST", apiURL, payload)
	if err != nil {
		return items, utils.NewError(
			err,
			"unable to create SelectItems request",
		)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return items, utils.NewError(
			err,
			"unable to process SelectItems request",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return items, utils.NewError(
			err,
			"unable to process SelectItems response body.",
		)
	}

	responseBodyString := string(body)
	utils.PrettyPrintJSON(body)
	err = json.Unmarshal([]byte(responseBodyString), &items)
	if err != nil {
		return items, utils.NewError(
			err,
			"unable to unmarshal response body",
		)
	}

	return items, nil
}

func (s *AppEtimsVSCU) SelectItemsClass() (*entities.EtimsItemClassResponse, error) {

	itemsClass := &entities.EtimsItemClassResponse{}

	apiURL := fmt.Sprintf("%v/itemClass/selectItemsClass", s.apiBaseURL)

	form := forms.CreateTinBranchLastRequstForm{
		Tin:       s.pinNumber,
		BhfId:     s.branchID,
		LastReqDt: lastReqDt,
	}

	payloadBytes, err := json.Marshal(form)
	if err != nil {
		logger.Errorf("failed to marshal SelectItemsClass form payload, err=[%v]\n", err)
		return itemsClass, err
	}
	payload := strings.NewReader(string(payloadBytes))

	req, err := http.NewRequest("POST", apiURL, payload)
	if err != nil {
		return itemsClass, utils.NewError(
			err,
			"unable to create SelectItemsClass request",
		)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return itemsClass, utils.NewError(
			err,
			"unable to process SelectItemsClass request",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return itemsClass, utils.NewError(
			err,
			"unable to process SelectItemsClass response body.",
		)
	}

	responseBodyString := string(body)
	utils.PrettyPrintJSON(body)
	err = json.Unmarshal([]byte(responseBodyString), &itemsClass)
	if err != nil {
		return itemsClass, utils.NewError(
			err,
			"unable to unmarshal response body",
		)
	}

	return itemsClass, nil
}

func (s *AppEtimsVSCU) SelectNotices() (*entities.EtimsNoticesResponse, error) {

	notices := &entities.EtimsNoticesResponse{}

	apiURL := fmt.Sprintf("%v/notices/selectNotices", s.apiBaseURL)

	form := forms.CreateTinBranchLastRequstForm{
		Tin:       s.pinNumber,
		BhfId:     s.branchID,
		LastReqDt: lastReqDt,
	}

	payloadBytes, err := json.Marshal(form)
	if err != nil {
		logger.Errorf("failed to marshal SelectNotices form payload, err=[%v]\n", err)
		return notices, err
	}
	payload := strings.NewReader(string(payloadBytes))

	req, err := http.NewRequest("POST", apiURL, payload)
	if err != nil {
		return notices, utils.NewError(
			err,
			"unable to create SelectNotices request",
		)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return notices, utils.NewError(
			err,
			"unable to process SelectNotices request",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return notices, utils.NewError(
			err,
			"unable to process SelectNotices response body.",
		)
	}

	responseBodyString := string(body)
	utils.PrettyPrintJSON(body)
	err = json.Unmarshal([]byte(responseBodyString), &notices)
	if err != nil {
		return notices, utils.NewError(
			err,
			"unable to unmarshal response body",
		)
	}

	return notices, nil
}
