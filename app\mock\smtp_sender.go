package mock

import (
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/providers"
)

type (
	MockSmtpSender struct {
	}
)

func NewSmtpSender() providers.SmtpSender {
	return &MockSmtpSender{}
}

func (s *MockSmtpSender) SendSmtp(to, subject, message string, emailConfig *entities.EmailConfig) error {
	return nil
}

func (s *MockSmtpSender) SendEmailWithTemplate(application, htmlTemplate, to, subject string, items interface{}, attachments []string) error {
	return nil
}
