CREATE TABLE IF NOT EXISTS etims_stock_items (
    id               BIGSERIAL                   PRIMARY KEY, 
    etims_stock_id   BIGINT                      NOT NULL REFERENCES etims_stock(id),         
    itemSeq          BIGINT                      NOT NULL,        
    itemCd           VARCHAR(120),
    itemClsCd        VARCHAR(120), 
    itemNm           TEXT,
    bcd              VARCHAR(120),
    pkg              BIGINT,   
    pkgUnitCd        VARCHAR(120),
    qty              NUMERIC(13,2),
    qtyUnitCd        VARCHAR(120),
    itemExprDt       VARCHAR(255), 
    prc              NUMERIC(18,2),
    splyAmt          NUMERIC(18,2),
    totDcAmt         NUMERIC(18,2),
    taxblAmt         NUMERIC(18,2),
    taxTyCd          VARCHAR(10),
    taxAmt           NUMERIC(18,2),
    totAmt           NUMERIC(18,2),   
    uuid             VARCHAR(250)                NOT NULL, 
    created_at       TIMESTAMP WITH TIME ZONE    NOT NULL DEFAULT now(),
    updated_at       TIMESTAMP WITH TIME ZONE
);

CREATE UNIQUE INDEX IF NOT EXISTS etims_stock_items_uuid_uidx ON etims_stock_items(uuid);
