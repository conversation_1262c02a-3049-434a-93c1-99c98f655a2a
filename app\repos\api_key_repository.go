package repos

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"strings"
)

const (
	countAPIKeysSQL = `SELECT COUNT(*) AS count FROM api_keys WHERE deleted_at IS NULL AND store_id=$1`

	countAPIKeysForUserSQL = `SELECT COUNT(*) AS count FROM api_keys WHERE deleted_at IS NULL AND user_id=$1`

	createAPIKeySQL = `INSERT INTO api_keys (api_key, description, organization_id, store_id, user_id, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING id`

	deleteAPIKeyByIDSQL = `UPDATE api_keys SET deleted_at=now(), updated_at=now() WHERE id=$1`

	selectAPIKeyByIDAndStoreIDSQL = selectAPIKeySQL + ` AND id=$1 AND store_id=$2`

	selectAPIKeyByKeySQL = selectAPIKeySQL + ` AND api_key=$1`

	filterAPIKeysSQL = selectAPIKeySQL + ` AND store_id=$1`

	selectAPIKeySQL = `SELECT id, api_key, description, deleted_at, last_used_at, organization_id, store_id, user_id, created_at, 
		updated_at FROM api_keys WHERE deleted_at IS NULL`

	updateAPIKeySQL = `UPDATE api_keys SET store_id=$1, updated_at=$2 WHERE id=$3`

	updateAPIKeyLastUsedAtSQL = `UPDATE api_keys SET last_used_at=now(), updated_at=now() WHERE id=$1`
)

type (
	APIKeyRepository interface {
		CountAPIKeys(ctx context.Context, storeId int64, filter *entities.PaginationFilter) (int, error)
		CountAPIKeysForUser(ctx context.Context, userID int64, filter *entities.PaginationFilter) (int, error)
		Delete(ctx context.Context, apiKey *entities.APIKey) error
		FilterAPIKeys(ctx context.Context, db txns_db.TransactionsSQLOperations, storeId int64, filter *entities.PaginationFilter) ([]*entities.APIKey, error)
		FindByID(ctx context.Context, apiKeyID int64, storeId int64) (*entities.APIKey, error)
		FindByKey(ctx context.Context, key string) (*entities.APIKey, error)
		Save(ctx context.Context, apiKey *entities.APIKey) error
		UpdateLastUsedAt(ctx context.Context, apiKey *entities.APIKey) error
	}

	AppAPIKeyRepository struct {
		db *sql.DB
	}
)

func NewAPIKeyRepository(db *sql.DB) APIKeyRepository {
	return &AppAPIKeyRepository{db: db}
}

func (r *AppAPIKeyRepository) CountAPIKeys(
	ctx context.Context,
	storeId int64,
	filter *entities.PaginationFilter,
) (int, error) {
	var count int

	args := make([]interface{}, 0)
	args = append(args, storeId)
	query := countAPIKeysSQL
	currentIndex := 2

	query, args, _ = r.buildQueryFromFilter(query, filter, args, currentIndex)
	row := r.db.QueryRowContext(ctx, query, args...)
	err := row.Scan(&count)
	if err != nil {
		return count, err
	}

	return count, nil
}

func (r *AppAPIKeyRepository) CountAPIKeysForUser(
	ctx context.Context,
	userID int64,
	filter *entities.PaginationFilter,
) (int, error) {
	var count int

	args := make([]interface{}, 0)
	args = append(args, userID)
	query := countAPIKeysForUserSQL
	currentIndex := 2

	query, args, _ = r.buildQueryFromFilter(query, filter, args, currentIndex)
	row := r.db.QueryRowContext(ctx, query, args...)
	err := row.Scan(&count)
	if err != nil {
		return count, err
	}

	return count, nil
}

func (r *AppAPIKeyRepository) Delete(
	ctx context.Context,
	apiKey *entities.APIKey,
) error {
	_, err := r.db.ExecContext(ctx, deleteAPIKeyByIDSQL, apiKey.ID)
	return err
}

func (r *AppAPIKeyRepository) FilterAPIKeys(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	storeId int64,
	filter *entities.PaginationFilter,
) ([]*entities.APIKey, error) {

	categories := make([]*entities.APIKey, 0)

	args := make([]interface{}, 0)
	args = append(args, storeId)
	query := filterAPIKeysSQL
	currentIndex := 2

	query, args, currentIndex = r.buildQueryFromFilter(query, filter, args, currentIndex)
	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return categories, err
	}

	defer rows.Close()

	for rows.Next() {
		invoice, err := r.scanRowIntoAPIKey(rows)
		if err != nil {
			return categories, err
		}

		categories = append(categories, invoice)
	}

	return categories, rows.Err()
}

func (r *AppAPIKeyRepository) FindByID(
	ctx context.Context,
	apiKeyID int64,
	storeId int64,
) (*entities.APIKey, error) {
	row := r.db.QueryRow(selectAPIKeyByIDAndStoreIDSQL, apiKeyID, storeId)
	return r.scanRowIntoAPIKey(row)
}

func (r *AppAPIKeyRepository) FindByKey(
	ctx context.Context,
	key string,
) (*entities.APIKey, error) {
	row := r.db.QueryRow(selectAPIKeyByKeySQL, key)
	return r.scanRowIntoAPIKey(row)
}

func (r *AppAPIKeyRepository) Save(
	ctx context.Context,
	apiKey *entities.APIKey,
) error {

	apiKey.Timestamps.Touch()
	var err error

	if apiKey.IsNew() {

		err = r.db.QueryRow(
			createAPIKeySQL,
			apiKey.APIKey,
			apiKey.Description,
			apiKey.OrganizationID,
			apiKey.StoreID,
			apiKey.UserID,
			apiKey.CreatedAt,
			apiKey.UpdatedAt,
		).Scan(&apiKey.ID)

	} else {

		_, err = r.db.Exec(
			updateAPIKeySQL,
			apiKey.StoreID,
			apiKey.UpdatedAt,
			apiKey.ID,
		)
	}

	if err != nil {
		log.Printf("error saving api_key, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppAPIKeyRepository) UpdateLastUsedAt(
	ctx context.Context,
	apiKey *entities.APIKey,
) error {
	_, err := r.db.ExecContext(ctx, updateAPIKeyLastUsedAtSQL, apiKey.ID)
	return err
}

func (r *AppAPIKeyRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	if filter.UserID > 0 {
		query += fmt.Sprintf(" AND user_id = $%d", currentIndex)
		args = append(args, filter.UserID)
		currentIndex += 1
	}

	if len(strings.TrimSpace(filter.Search)) > 0 {
		query += fmt.Sprintf(" AND (LOWER(api_key) LIKE '%%' || $%d || '%%' ", currentIndex)
		query += fmt.Sprintf(" OR LOWER(description) LIKE '%%' || $%d || '%%' )", currentIndex)
		args = append(args, strings.ToLower(strings.TrimSpace(filter.Search)))
		currentIndex++
	}

	return query, args, currentIndex
}

func (r *AppAPIKeyRepository) scanRowIntoAPIKey(
	rowScanner txns_db.RowScanner,
) (*entities.APIKey, error) {

	var apiKey entities.APIKey

	err := rowScanner.Scan(
		&apiKey.ID,
		&apiKey.APIKey,
		&apiKey.Description,
		&apiKey.DeletedAt,
		&apiKey.LastUsedAt,
		&apiKey.OrganizationID,
		&apiKey.StoreID,
		&apiKey.UserID,
		&apiKey.CreatedAt,
		&apiKey.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning apiKey,  err=[%v]\n", err.Error())
		return &apiKey, err
	}

	return &apiKey, nil
}
