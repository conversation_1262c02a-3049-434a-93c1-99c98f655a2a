package entities

import (
	"database/sql/driver"
)

type CurrencyStatus string

const (
	CurrencyStatusActive   CurrencyStatus = "active"
	CurrencyStatusInactive CurrencyStatus = "inactive"
)

func (s CurrencyStatus) IsValid() bool {
	return s == CurrencyStatusActive || s == CurrencyStatusInactive
}

func (s *CurrencyStatus) Scan(value interface{}) error {
	*s = CurrencyStatus(string(value.([]uint8)))
	return nil
}

func (s CurrencyStatus) Value() (driver.Value, error) {
	return s.String(), nil
}

func (s CurrencyStatus) String() string {
	return string(s)
}
