package forms

import (
	"math/big"
	"time"
)

type (
	DPOPaymentRequestForm struct {
		AccountReference   string          `binding:"required" json:"account_reference"`
		Amount             string          `binding:"required" json:"amount"`
		BackURL            string          `json:"back_url"`
		CreditCardForm     *CreditCardForm `json:"credit_card_form"`
		Currency           string          `binding:"required" json:"currency"`
		CustomerAddress    string          `xml:"customer_address"`
		CustomerCity       string          `xml:"customer_city"`
		CustomerCountry    string          `xml:"customer_country"`
		CustomerEmail      string          `xml:"customer_email"`
		CustomerFirstName  string          `xml:"customer_first_name"`
		CustomerLastName   string          `xml:"customer_last_name"`
		CustomerPhone      int             `xml:"customer_phone"`
		RedirectURL        string          `json:"redirect_url"`
		ServiceCode        string          `binding:"required" json:"service_code"`
		ServiceDate        time.Time       `binding:"required" json:"service_date"`
		ServiceDescription string          `json:"service_description"`
	}

	DPOPaymentRefundForm struct {
		RefundAmount     *big.Float `binding:"required" json:"card_expiry"`
		RefundRef        string     `binding:"required" json:"refund_ref"`
		Description      string     `binding:"required" json:"description"`
		RequiresApproval bool       `binding:"required" json:"requires_approval"`
	}

	CreditCardForm struct {
		CardExpiry     string `binding:"required" json:"card_expiry"`
		CardHolderName string `binding:"required" json:"card_holder"`
		CardNumber     string `binding:"required" json:"card_number"`
		CVV            string `binding:"required" json:"cvv"`
	}
)
