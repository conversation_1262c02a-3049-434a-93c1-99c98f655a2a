package testutils

import (
	"context"
	"database/sql"

	esd_db "compliance-and-risk-management-backend/app/database"

	"github.com/smartystreets/goconvey/convey"
)

func WithTestDBs(
	ctx context.Context,
	testTransactionsDB esd_db.TransactionsDB,
	f func(context.Context, esd_db.TransactionsDB),
) func() {

	return func() {

		var transactionsTx *sql.Tx

		transactionsDB := esd_db.NewTestTransactionsDB(transactionsTx)

		if testTransactionsDB.Valid() {
			_, err := testTransactionsDB.ExecContext(ctx, "SET TRANSACTION ISOLATION LEVEL SERIALIZABLE")
			convey.So(err, convey.ShouldBeNil)

			transactionsTx, err = testTransactionsDB.Begin()
			convey.So(err, convey.ShouldBeNil)
			transactionsDB = esd_db.NewTestTransactionsDB(transactionsTx)
		}

		convey.Reset(func() {
			if transactionsTx != nil {
				err := transactionsTx.Rollback()
				convey.So(err, convey.ShouldBeNil)
			}
		})

		f(ctx, transactionsDB)
	}
}
