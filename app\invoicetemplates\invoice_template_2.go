package invoicetemplates

import (
	"bufio"
	"log"
	"compliance-and-risk-management-backend/app/models"
	"compliance-and-risk-management-backend/app/utils"
	"os"
	"strings"
)

// ProcessTemplateTwoInvoice - ProcessTemplateTwoInvoice
func ProcessTemplateTwoInvoice(textFile string) models.Invoice {

	log.Println("Processing Template TWO Invoice File >>>> ", textFile)
	invoice := models.Invoice{}

	file, err := os.Open(textFile)
	utils.CheckError(err, "Error reading signed input file >>> "+textFile)
	defer file.Close()

	scanner := bufio.NewScanner(file)
	scanner.Split(bufio.ScanLines)

	var lines []string
	var lineNumber = 1
	var lastLine = ""
	totalsLineNumber := 1

	isMultiplePageInvoice := false

	for scanner.Scan() {

		invoiceLine := scanner.Text()
		invoiceLine = strings.ToUpper(invoiceLine)

		trimedInvoiceLine := strings.Replace(invoiceLine, " ", "", -1)

		lines = append(lines, invoiceLine)
		log.Println("[TMPL_2] invoiceLine ["+utils.ConvertIntToString(lineNumber)+"]  >>> ", invoiceLine)

		if strings.Contains(invoiceLine, "INVOICE") && strings.Contains(invoiceLine, "NO") {

			log.Println(":::::::::::::::   Processing invoice number line >>> ", invoiceLine)
			lineArr := strings.Split(invoiceLine, " ")
			index := utils.IndexOf("INVOICE", lineArr)
			log.Println(index)
			invoiceNumber := lineArr[index+2]
			log.Println("[TMPL_2] invoiceNumber >>> ", invoiceNumber)
			invoice.InvoiceNumber = invoiceNumber

			if strings.Contains(invoiceLine, "DUE") && strings.Contains(invoiceLine, "AMOUNT") &&
				strings.Contains(invoiceLine, "PAYMENT") {

				log.Println("Processing invoice totals >>>> ", invoiceLine)
				isMultiplePageInvoice = true

				lineArr := strings.Split(invoiceLine, " ")
				lineArrLen := len(lineArr)
				vat, grandTotal := lineArr[lineArrLen-2], lineArr[lineArrLen-1]

				vat = strings.Replace(vat, ",", "", -1)
				grandTotal = strings.Replace(grandTotal, ",", "", -1)

				log.Println("[TMPL_2] invoice VAT >>> ", vat)
				log.Println("[TMPL_2] invoice Grand Total >>> ", grandTotal)

				invoice.Vat = vat
				invoice.GrandTotal = grandTotal
			}

		} else if strings.Contains(invoiceLine, "PAGE") {

			invoiceLine = strings.TrimSpace(invoiceLine)
			dateArr := strings.Split(invoiceLine, " ")
			dateString := dateArr[len(dateArr)-1]
			formatedDate := utils.FormatDateTypeOneString(dateString)
			invoice.InvoiceDate = formatedDate

		} else if strings.Contains(invoiceLine, "VAT") && strings.Contains(invoiceLine, "TZS") &&
			strings.Contains(invoiceLine, "TOTAL") && strings.Contains(invoiceLine, "AMOUNT") {

			totalsLineNumber = lineNumber + 2

		} else if lineNumber == totalsLineNumber {

			log.Println("Processing totals >>>> ", invoiceLine)
			totalsLine := strings.TrimSpace(invoiceLine)
			lineArr := strings.Split(totalsLine, " ")

			totalsArrLen := len(lineArr)
			log.Println("totalsArrLen   >>>>>   ", totalsArrLen)

			if totalsArrLen >= 2 {
				vat, grandTotal := lineArr[0], lineArr[1]

				vat = strings.Replace(vat, ",", "", -1)
				grandTotal = strings.Replace(grandTotal, ",", "", -1)

				log.Println("[TMPL_2] invoice VAT >>> ", vat)
				log.Println("[TMPL_2] invoice Grand Total >>> ", grandTotal)

				invoice.Vat = vat
				invoice.GrandTotal = grandTotal
			}

		} else if strings.Contains(invoiceLine, "#") && strings.Contains(invoiceLine, "/") {

			invoiceSignature := strings.Replace(invoiceLine, " ", "", -1)
			invoice.Signature = invoiceSignature

		}

		if len(trimedInvoiceLine) > 1 && !strings.Contains(invoiceLine, "#") {
			lastLine = invoiceLine
		}

		lineNumber++
	}

	log.Println("isMultiplePageInvoice  >>>>   ", isMultiplePageInvoice)

	if !isMultiplePageInvoice {

		log.Println("Not multipage invoice...")

		lastLine = strings.TrimSpace(lastLine)
		log.Println("[TMPL_2] lastLine >>> ", lastLine)
		lineArr := strings.Split(lastLine, " ")
		vat, grandTotal := lineArr[0], lineArr[1]

		vat = strings.Replace(vat, ",", "", -1)
		grandTotal = strings.Replace(grandTotal, ",", "", -1)

		log.Println("[TMPL_2] invoice VAT >>> ", vat)
		log.Println("[TMPL_2] invoice Grand Total >>> ", grandTotal)

		invoice.Vat = vat
		invoice.GrandTotal = grandTotal

	}

	return invoice
}
