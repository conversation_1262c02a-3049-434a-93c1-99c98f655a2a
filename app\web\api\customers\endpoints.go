package customers

import (
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/web/auth"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	customerService services.CustomerService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyActiveUser(transactionsDB, sessionAuthenticator, sessionService))
	{
		protectedAPI.POST("/customers", createCustomer(transactionsDB, customerService))
		protectedAPI.GET("/customers", filterCustomers(transactionsDB, customerService))
		protectedAPI.GET("/customers/:id", getCustomer(transactionsDB, customerService))
		protectedAPI.PUT("/customers/:id", updateCustomer(transactionsDB, customerService))
		protectedAPI.GET("/customers/download", downloadCustomersExcel(transactionsDB, customerService))
	}
}
