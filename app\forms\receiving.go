package forms

import "time"

type (
	CreateReceivingForm struct {
		ItemID         int64     `json:"item_id"`
		Quantity       float64   `json:"quantity"`
		CostPrice      float64   `json:"cost_price"`
		Total          float64   `json:"total"`
		OrganizationID int64     `json:"organization_id"`
		ReceivedAt     time.Time `json:"received_at"`
		CreatedBy      int64     `json:"created_by"`
		SupplierID     int64     `json:"supplier_id"`
		Description    string    `json:"description"`
	}

	UpdateReceivingForm struct {
		Quantity    float64   `json:"quantity"`
		CostPrice   float64   `json:"cost_price"`
		Total       float64   `json:"total"`
		ReceivedAt  time.Time `json:"received_at"`
		CreatedBy   int64     `json:"created_by"`
		SupplierID  int64     `json:"supplier_id"`
		Description string    `json:"description"`
	}
)
