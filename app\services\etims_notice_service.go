package services

import (
	"context"
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/repos"
)

type EtimsNoticeService interface {
	FilterEtimsNotices(context.Context, txns_db.TransactionsSQLOperations, *entities.PaginationFilter) (*entities.EtimsNoticeList, error)
	FindByID(context.Context, txns_db.TransactionsSQLOperations, int64) (*entities.EtimsNotice, error)
}

type AppEtimsNoticeService struct {
	etimsNoticeRepository repos.EtimsNoticeRepository
}

func NewEtimsNoticeService(
	etimsNoticeRepository repos.EtimsNoticeRepository,
) EtimsNoticeService {
	return &AppEtimsNoticeService{
		etimsNoticeRepository: etimsNoticeRepository,
	}
}

func (s *AppEtimsNoticeService) FilterEtimsNotices(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) (*entities.EtimsNoticeList, error) {

	list := &entities.EtimsNoticeList{}

	count, err := s.etimsNoticeRepository.Count(ctx, filter)
	if err != nil {
		return list, err
	}

	notices, err := s.etimsNoticeRepository.FilterEtimsNotices(ctx, operations, filter)
	if err != nil {
		return list, err
	}

	list.EtimsNotices = notices

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	list.Pagination = pagination

	return list, nil
}

func (s *AppEtimsNoticeService) FindByID(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	id int64,
) (*entities.EtimsNotice, error) {

	return s.etimsNoticeRepository.FindByID(ctx, id)
}
