package entities

import "database/sql/driver"

type AssetUploadStatus string

const (
	AssetUploadStatusSuccess AssetUploadStatus = "success"
	AssetUploadStatusFailed  AssetUploadStatus = "failed"
)

func (s AssetUploadStatus) IsValid() bool {
	return s == AssetUploadStatusSuccess || s == AssetUploadStatusFailed
}

func (s AssetUploadStatus) IsSuccess() bool {
	return s == AssetUploadStatusSuccess
}

func (s *AssetUploadStatus) Scan(value interface{}) error {
	*s = AssetUploadStatus(string(value.([]uint8)))
	return nil
}

func (s AssetUploadStatus) Value() (driver.Value, error) {
	return s.String(), nil
}

func (s AssetUploadStatus) String() string {
	return string(s)
}
