API_BASE_URL=https://optimusapi.strutstechnology.com/api
BACKUP_FOLDER=C:\\ESD_DOCUMENTS\\Backup
DATABASE_URL=postgres://postgres:root@localhost:5432/mpesa_hash_decoder?sslmode=disable
DATABASE_URL_PROD=postgres://gidemn:<EMAIL>:5432/mpesa_hash_decoder?sslmode=disable
EMAIL_SENDING_BACKUP_FOLDER=C:\\ESD_DOCUMENTS\\Email_Sending_Backup
EMAIL_SENDING_INPUT_FOLDER=C:\\ESD_DOCUMENTS\\Email_Sending
ENVIRONMENT=dev
EXCHANGE_RATE_API_KEY=XXXX
PDF_FOLDER=C:\\ESD_DOCUMENTS\\PDF
ERROR_FOLDER=C:\\ESD_DOCUMENTS\\Error_Documents
ESD_INPUT_FOLDER=C:\\ESD_DOCUMENTS\\ESD_Input
ESD_OUTPUT_FOLDER=C:\\ESD_DOCUMENTS\\ESD_Output
ETIMS_API_BASE_URL=https://27c8-41-90-179-152.ngrok-free.app
ETIMS_API_BASE_URL_DOCKER_1=http://host.docker.internal:8088
ETIMS_API_BASE_URL_DOCKER=https://27c8-41-90-179-152.ngrok-free.app
ETIMS_BRANCH_ID=00
ETIMS_DEVICE_SERIAL_NUMBER=SN5433Q09
ETIMS_PIN_NUMBER_PROD=P051629857B
ETIMS_PIN_NUMBER=P051922564N
ETIMS_VERIFICATION_BASE_URL_SANDBOX=https://etims-sbx.kra.go.ke
ETIMS_VERIFICATION_BASE_URL_PRODUCTION=https://etims.kra.go.ke
INPUT_FOLDER=C:\\ESD_DOCUMENTS\\Input
OLD_SIGNED_INPUT_FOLDER=C:\\ESD_DOCUMENTS\\OLD_Signed
PORT=9015
QRCODE_FOLDER=C:\\ESD_DOCUMENTS\\QR_Codes
SIGNED_OUTPUT_FOLDER=C:\\ESD_DOCUMENTS\\Signed_Output
MPESA_API_URL=https://api.safaricom.co.ke
MPESA_CONSUMER_KEY=XXXX
MPESA_CONSUMER_SECRET=XXXX
MPESA_SHORTCODE=4044085
MPESA_PASSKEY=XXXXX
MPESA_HASH_API_KEY=a2a23a9445bdcefcffce
MPESA_HASH_API_URL=https://hashesapi-1-t9408137.deta.app
OUTPUT_FOLDER=C:\\ESD_DOCUMENTS\\Output
SERVER_IP=localhost
SIGNATURE_POSITIONING_BOTTOM_OFFSET=45
SIGNATURE_POSITIONING_LEFT_OFFSET=110 
SIGNED_INPUT_FOLDER=C:\\ESD_DOCUMENTS\\Signed
SMTP_EMAIL=<EMAIL>
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=EmailPasss
SMTP_PORT=25
SMTP_SERVER=mail.domain.com
TXT_FOLDER=C:\\ESD_DOCUMENTS\\Txt
CC_EMAILS=<EMAIL>
