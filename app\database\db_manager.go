package database

import (
	"context"
	"database/sql"
	"fmt"
	"os"

	// postgresql driver
	_ "github.com/lib/pq"
)

type DB interface {
	Begin() (*sql.Tx, error)
	Close() error
	ExecContext(ctx context.Context, query string, args ...interface{}) (sql.Result, error)
	QueryContext(ctx context.Context, query string, args ...interface{}) (*sql.Rows, error)
	QueryRowContext(ctx context.Context, query string, args ...interface{}) *sql.Row
}

type RowScanner interface {
	Scan(dest ...interface{}) error
}

func NewPostgresDB() DB {
	return NewPostgresDBWithURL(
		os.Getenv("DATABASE_URL"),
	)
}

func NewPostgresDBWithURL(databaseURL string) DB {
	if databaseURL == "" {
		panic("database url is required")
	}

	db, err := sql.Open("postgres", databaseURL)
	if err != nil {
		panic(fmt.Sprintf("sql.Open failed because err=[%v]", err))
	}

	return db
}
