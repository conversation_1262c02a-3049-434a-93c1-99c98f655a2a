package stores

import (
	"context"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/webutils"
)

func createStore(
	transactionsDB *database.AppTransactionsDB,
	storeService services.StoreService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		var storeForm forms.CreateStoreForm
		err := ctx.Bind(&storeForm)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind store form while creating store",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		store, err := storeService.CreateStore(ctx.Request.Context(), transactionsDB, &storeForm)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to create store. Err=" + err.Error(),
			})
			return
		}

		ctx.JSON(http.StatusOK, store)
	}
}

func downloadStoresExcel(
	transactionsDB *database.AppTransactionsDB,
	storeService services.StoreService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering stores",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		t := time.Now()
		dateString := t.Format("Stores-2006-01-02-150405")
		outputFolder := os.Getenv("OUTPUT_FOLDER")
		excelFileName := fmt.Sprintf("%v.xlsx", dateString)
		excelFilePath := fmt.Sprintf("%v//%v", outputFolder, excelFileName)

		_, err = storeService.DownloadStores(context.Background(), transactionsDB, filter, excelFilePath)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to download stores",
			})
			return
		}

		// ctx.JSON(http.StatusOK, storeList)
		file, err := os.Open(excelFilePath)
		if err != nil {
			ctx.String(http.StatusInternalServerError, fmt.Sprintf("Error opening file: %s", err))
			return
		}
		defer file.Close()

		// Set the headers for the response
		ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
		ctx.Header("Content-Disposition", "attachment; filename="+excelFileName)

		// Copy the file content to the response writer
		_, err = io.Copy(ctx.Writer, file)
		if err != nil {
			ctx.String(500, fmt.Sprintf("Error copying file to response: %s", err))
			return
		}
	}
}

func filterStores(
	transactionsDB *database.AppTransactionsDB,
	storeService services.StoreService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		filter, err := middleware.PaginationFilterFromContext(ctx)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to parse pagination filter while filtering stores",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		storeList, err := storeService.FilterStores(ctx.Request.Context(), transactionsDB, filter)
		if err != nil {
			ctx.JSON(500, gin.H{
				"error": err.Error(),
			})
			return
		}

		ctx.JSON(http.StatusOK, storeList)
	}
}

func getStore(
	transactionsDB *database.AppTransactionsDB,
	storeService services.StoreService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		storeIDStr := ctx.Param("id")
		storeID, err := strconv.ParseInt(storeIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse storeID=[%v], err=[%v]\n", storeIDStr, err)
		}

		store, err := storeService.FindStoreByID(ctx.Request.Context(), storeID)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to get store",
			})
			return
		}

		ctx.JSON(http.StatusOK, store)
	}
}

func updateStore(
	transactionsDB *database.AppTransactionsDB,
	storeService services.StoreService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		storeIDStr := ctx.Param("id")
		storeID, err := strconv.ParseInt(storeIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse storeID=[%v], err=[%v]\n", storeIDStr, err)
		}

		var form forms.UpdateStoreForm
		err = ctx.Bind(&form)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind store form while updating store",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		store, err := storeService.UpdateStore(ctx.Request.Context(), transactionsDB, storeID, &form)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to update store",
			})
			return
		}

		ctx.JSON(http.StatusOK, store)
	}
}

func updateStoreLicense(
	transactionsDB *database.AppTransactionsDB,
	storeService services.StoreService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		storeIDStr := ctx.Param("id")
		storeID, err := strconv.ParseInt(storeIDStr, 10, 64)
		if err != nil {
			log.Printf("failed to parse storeID=[%v], err=[%v]\n", storeIDStr, err)
		}

		var form forms.UpdateStoreLicenseForm
		err = ctx.Bind(&form)
		if err != nil {
			appError := utils.NewErrorWithCode(
				err,
				utils.ErrorCodeInvalidArgument,
				"Failed to bind store form while updating store",
			)

			webutils.HandleError(ctx, appError)
			return
		}

		store, err := storeService.UpdateStoreLicense(ctx.Request.Context(), transactionsDB, storeID, &form)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to update store license",
			})
			return
		}

		ctx.JSON(http.StatusOK, store)
	}
}
