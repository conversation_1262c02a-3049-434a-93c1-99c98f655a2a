CREATE TABLE IF NOT EXISTS etims_notices (
    id                     BIGSE<PERSON>AL                   PRIMARY KEY, 
    notice_number          BIGINT                      NOT NULL,
    title                  VARCHAR(255)                NOT NULL,    
    content                TEXT,  
    detail_url             TEXT, 
    registration_date_time VARCHAR(255),
    registration_name      <PERSON><PERSON><PERSON><PERSON>(255),    
    created_at             TIMESTAMP WITH TIME ZONE    NOT NULL DEFAULT now(),
    updated_at             TIMESTAMP WITH TIME ZONE
);

CREATE UNIQUE INDEX IF NOT EXISTS etims_notices_notice_number_uidx ON etims_notices(notice_number);
