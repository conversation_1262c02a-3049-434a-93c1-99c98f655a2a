package utils

import (
	"fmt"
	"time"
)

// Custom function to format time.Time value to a specified date format
func FormatTime(t time.Time, dateFormat string) string {
	formatedTime := t.Format(dateFormat)
	return formatedTime
}

func EtimsDateTimeToTime(input string) (time.Time, error) {
	// Define layout for parsing the string
	layout := "20060102150405"

	// Parse the input string into a time.Time object
	t, err := time.Parse(layout, input)
	if err != nil {
		fmt.Println("Error parsing date:", err)
		return t, err
	}

	return t, nil
}
