package repos

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"compliance-and-risk-management-backend/app/entities"
	"strings"
	"time"

	txns_db "compliance-and-risk-management-backend/app/database"
)

const (
	countVFMSItemsSQL = `SELECT COUNT(*) AS count FROM vfms_items WHERE 1=1`

	insertVFMSItemSQL = `INSERT INTO vfms_items (name, vfms_id, unit_measure, is_taxable, created_at, updated_at) VALUES`

	createVFMSItemSQL = insertVFMSItemSQL + ` ($1, $2, $3, $4, $5, $6) 
		ON CONFLICT(name) DO UPDATE SET vfms_id = EXCLUDED.vfms_id, unit_measure = EXCLUDED.unit_measure,  is_taxable = EXCLUDED.is_taxable, 		
		updated_at=Now() RETURNING id`

	selectVFMSItemByIDSQL = selectVFMSItemSQL + ` AND id=$1`

	selectVFMSItemByNameSQL = selectVFMSItemSQL + ` AND name=$1`

	selectVFMSItemByVFMSIDSQL = selectVFMSItemSQL + ` AND vfms_id=$1`

	selectVFMSItemSQL = `SELECT id, name, vfms_id, unit_measure, is_taxable, created_at, updated_at FROM vfms_items WHERE 1=1`

	updateVFMSItemSQL = `UPDATE vfms_items SET vfms_id=$1, unit_measure=$2, is_taxable=$3, updated_at=$4 WHERE id=$5`

	updateMultipleVFMSItemsSQL = `UPDATE vfms_items AS c SET (vfms_id, unit_measure, is_taxable, updated_at) = 
		(u.vfms_id, u.unit_measure, u.is_taxable, u.updated_at)`
)

type (
	VFMSItemRepository interface {
		CountVFMSItems(ctx context.Context, filter *entities.PaginationFilter) (int, error)
		FilterVFMSItems(context.Context, txns_db.TransactionsSQLOperations, *entities.PaginationFilter) ([]*entities.VFMSItem, error)
		FindVFMSItemsByNames(context.Context, txns_db.TransactionsSQLOperations, []string) ([]*entities.VFMSItem, error)
		GetVFMSItemByVFMSID(ctx context.Context, email string) (*entities.VFMSItem, error)
		GetVFMSItemByID(ctx context.Context, vfmsItemID int64) (*entities.VFMSItem, error)
		GetVFMSItemByName(ctx context.Context, name string) (*entities.VFMSItem, error)
		Save(ctx context.Context, operations txns_db.TransactionsSQLOperations, vfmsItem *entities.VFMSItem) error
		SaveMultiple(context.Context, txns_db.TransactionsSQLOperations, []*entities.VFMSItem) error
		UpdateMultipleVFMSItems(ctx context.Context, operations txns_db.TransactionsSQLOperations, vfmsItems []*entities.VFMSItem) error
	}

	AppVFMSItemRepository struct {
		db *sql.DB
	}
)

func NewVFMSItemRepository(db *sql.DB) VFMSItemRepository {
	return &AppVFMSItemRepository{db: db}
}

func (r *AppVFMSItemRepository) CountVFMSItems(
	ctx context.Context,
	filter *entities.PaginationFilter,
) (int, error) {
	var count int
	query, args, _ := r.buildQueryFromFilter(countVFMSItemsSQL, filter, []interface{}{}, 1)
	row := r.db.QueryRowContext(ctx, query, args...)
	row.Scan(&count)
	return count, nil
}

func (r *AppVFMSItemRepository) FilterVFMSItems(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) ([]*entities.VFMSItem, error) {

	vfmsItems := make([]*entities.VFMSItem, 0)

	args := make([]interface{}, 0)
	query := selectVFMSItemSQL
	currentIndex := 1

	query, args, currentIndex = r.buildQueryFromFilter(query, filter, args, currentIndex)
	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return vfmsItems, err
	}

	defer rows.Close()

	for rows.Next() {
		vfmsItem, err := r.scanRowIntoVFMSItem(rows)
		if err != nil {
			return vfmsItems, err
		}

		vfmsItems = append(vfmsItems, vfmsItem)
	}

	return vfmsItems, rows.Err()
}

func (r *AppVFMSItemRepository) FindVFMSItemsByNames(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	itemNames []string,
) ([]*entities.VFMSItem, error) {

	vfmsItems := make([]*entities.VFMSItem, 0)

	if len(itemNames) == 0 {
		return vfmsItems, nil
	}

	args := make([]interface{}, 0)
	currentIndex := 1
	itemNamesStr := ""
	for _, name := range itemNames {
		args = append(args, strings.ToUpper(name))
		itemNamesStr += fmt.Sprintf("$%d,", currentIndex)
		currentIndex += 1
	}

	itemNamesStr = strings.TrimSuffix(itemNamesStr, ",")
	query := selectVFMSItemSQL
	query += fmt.Sprintf(" AND UPPER(name) IN (" + itemNamesStr + ")")

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return vfmsItems, err
	}

	for rows.Next() {
		vfmsItem, err := r.scanRowIntoVFMSItem(rows)
		if err != nil {
			return vfmsItems, err
		}

		vfmsItems = append(vfmsItems, vfmsItem)
	}

	return vfmsItems, nil
}

func (r *AppVFMSItemRepository) GetVFMSItemByID(
	ctx context.Context,
	vfmsItemID int64,
) (*entities.VFMSItem, error) {
	row := r.db.QueryRow(selectVFMSItemByIDSQL, vfmsItemID)
	return r.scanRowIntoVFMSItem(row)
}

func (r *AppVFMSItemRepository) GetVFMSItemByName(
	ctx context.Context,
	name string,
) (*entities.VFMSItem, error) {
	row := r.db.QueryRow(selectVFMSItemByNameSQL, name)
	return r.scanRowIntoVFMSItem(row)
}

func (r *AppVFMSItemRepository) GetVFMSItemByVFMSID(
	ctx context.Context,
	vfmsID string,
) (*entities.VFMSItem, error) {
	row := r.db.QueryRow(selectVFMSItemByVFMSIDSQL, vfmsID)
	return r.scanRowIntoVFMSItem(row)
}

func (r *AppVFMSItemRepository) Save(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	vfmsItem *entities.VFMSItem,
) error {

	vfmsItem.Timestamps.Touch()
	var err error

	if vfmsItem.IsNew() {
		res := operations.QueryRowContext(
			ctx,
			createVFMSItemSQL,
			vfmsItem.Name,
			vfmsItem.VFMSID,
			vfmsItem.UnitMeasure,
			vfmsItem.IsTaxable,
			vfmsItem.CreatedAt,
			vfmsItem.UpdatedAt,
		)

		err = res.Scan(&vfmsItem.ID)

	} else {

		_, err = operations.ExecContext(
			ctx,
			updateVFMSItemSQL,
			vfmsItem.VFMSID,
			vfmsItem.UnitMeasure,
			vfmsItem.IsTaxable,
			vfmsItem.UpdatedAt,
			vfmsItem.ID,
		)
	}

	if err != nil {
		log.Printf("error saving vfmsItem, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppVFMSItemRepository) SaveMultiple(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	vfmsItems []*entities.VFMSItem,
) error {

	query := insertVFMSItemSQL
	args := make([]interface{}, 0)
	currentIndex := 1

	values := make([]string, len(vfmsItems))

	for index, vfmsItem := range vfmsItems {
		values[index] = fmt.Sprintf("($%d, $%d, $%d, $%d, $%d, $%d)",
			currentIndex, currentIndex+1, currentIndex+2, currentIndex+3, currentIndex+4, currentIndex+5)
		currentIndex += 6
		args = append(args, vfmsItem.Name, vfmsItem.ID, vfmsItem.UnitMeasure, vfmsItem.IsTaxable, time.Now(), time.Now())
	}

	query += strings.Join(values, ",")
	query += " ON CONFLICT(name) DO UPDATE SET vfms_id = EXCLUDED.vfms_id, updated_at=Now() RETURNING id"

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return err
	}
	defer rows.Close()

	index := 0
	for rows.Next() {
		err := rows.Scan(&vfmsItems[index].ID)
		if err != nil {
			return err
		}
		index++
	}

	return rows.Err()
}

func (r *AppVFMSItemRepository) SelectZReportSummary(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) (*entities.ZReportTotals, error) {

	zReportTotals := &entities.ZReportTotals{}

	return zReportTotals, nil
}

func (r *AppVFMSItemRepository) UpdateMultipleVFMSItems(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	vfmsItems []*entities.VFMSItem,
) error {

	query := updateMultipleVFMSItemsSQL + " FROM (VALUES "
	values := make([]string, len(vfmsItems))
	args := make([]interface{}, 0)
	currentIndex := 1

	for index, vfmsItem := range vfmsItems {
		values[index] = fmt.Sprintf("($%d::bigint, $%d::bool, $%d::timestamptz)", currentIndex, currentIndex+1, currentIndex+2)
		currentIndex += 3
		args = append(args, vfmsItem.ID, vfmsItem.VFMSID, time.Now())
	}

	query += strings.Join(values, ",")
	query += ") AS u(id, is_z_reported, updated_at) where u.id = c.id"
	_, err := operations.ExecContext(ctx, query, args...)

	return err
}

func (r *AppVFMSItemRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	if len(strings.TrimSpace(filter.Search)) > 0 {
		query += fmt.Sprintf(" AND LOWER(name) LIKE '%%' || $%d || '%%' ", currentIndex)
		args = append(args, strings.ToLower(filter.Search))
		currentIndex++
	}

	return query, args, currentIndex
}

func (r *AppVFMSItemRepository) scanRowIntoVFMSItem(
	rowScanner txns_db.RowScanner,
) (*entities.VFMSItem, error) {

	var vfmsItem entities.VFMSItem

	err := rowScanner.Scan(
		&vfmsItem.ID,
		&vfmsItem.Name,
		&vfmsItem.VFMSID,
		&vfmsItem.UnitMeasure,
		&vfmsItem.IsTaxable,
		&vfmsItem.CreatedAt,
		&vfmsItem.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning vfmsItem,  err=[%v]\n", err.Error())
		return &vfmsItem, err
	}

	return &vfmsItem, nil
}
