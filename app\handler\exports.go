package handler

import (
	"encoding/csv"
	"fmt"
	"io"
	"log"
	"compliance-and-risk-management-backend/app/models"
	"compliance-and-risk-management-backend/app/utils"
	"net"
	"net/http"
	"os"
	"time"

	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/gorilla/mux"
	"github.com/jinzhu/gorm"
	"github.com/jung-kurt/gofpdf"
	"github.com/tealeg/xlsx"
)

func ExportInvoicesExcel(db *gorm.DB, w http.ResponseWriter, r *http.Request) {

	// Retrieve data
	vars := mux.Vars(r)
	startDate := utils.FormatReportDateStringTimestamp(vars["startdate"])
	endDate := utils.FormatReportDateStringTimestamp(vars["enddate"])

	fmt.Printf("Generating excel report with startDate=[%v], and endDate=[%v] \n", startDate, endDate)

	invoices := []models.Invoice{}
	db.Order("Id DESC").Where("date_created BETWEEN ? and ? ", startDate, endDate).Find(&invoices)

	/************************************/
	xlsx := excelize.NewFile()
	// Create a new sheet.
	index := xlsx.NewSheet("Invoices")

	// headers := []string{"#", "ID", "Invoice Number", "Date Created", "VAT", "Grand Total", "Signature", "File Name"}

	xlsx.SetCellValue("Invoices", "A1", "#ID")
	xlsx.SetColWidth("Invoices", "A", "A", 10)

	xlsx.SetCellValue("Invoices", "B1", "Invoice Number")
	xlsx.SetColWidth("Invoices", "B", "B", 22)

	xlsx.SetCellValue("Invoices", "C1", "Date Created")
	xlsx.SetColWidth("Invoices", "C", "D", 30)

	xlsx.SetCellValue("Invoices", "D1", "VAT")
	xlsx.SetColWidth("Invoices", "D", "D", 25)

	xlsx.SetCellValue("Invoices", "E1", "Grand Total")
	xlsx.SetColWidth("Invoices", "E", "E", 30)

	xlsx.SetCellValue("Invoices", "F1", "Signature")
	xlsx.SetColWidth("Invoices", "F", "F", 20)

	xlsx.SetCellValue("Invoices", "G1", "File Name")
	xlsx.SetColWidth("Invoices", "G", "G", 20)

	// Set active sheet of the workbook.
	xlsx.SetActiveSheet(index)

	for i, invoice := range invoices {
		// xlsx.SetCellValue("Sheet1", "A1", "#")
		columnLetter := "A" + utils.ConvertIntToString(i+2)
		xlsx.SetCellValue("Invoices", columnLetter, invoice.ID)

		columnLetter = "B" + utils.ConvertIntToString(i+2)
		xlsx.SetCellValue("Invoices", columnLetter, invoice.InvoiceNumber)

		columnLetter = "C" + utils.ConvertIntToString(i+2)
		xlsx.SetCellValue("Invoices", columnLetter, invoice.DateCreated.String())

		columnLetter = "D" + utils.ConvertIntToString(i+2)
		xlsx.SetCellValue("Invoices", columnLetter, invoice.Vat)

		columnLetter = "E" + utils.ConvertIntToString(i+2)
		xlsx.SetCellValue("Invoices", columnLetter, invoice.GrandTotal)

		columnLetter = "F" + utils.ConvertIntToString(i+2)
		xlsx.SetCellValue("Invoices", columnLetter, invoice.Signature)

		columnLetter = "G" + utils.ConvertIntToString(i+2)
		xlsx.SetCellValue("Invoices", columnLetter, invoice.FileName)
	}

	// Save xlsx file by the given path.
	err := xlsx.SaveAs("./downloads/Invoices_Excel.xlsx")
	if err != nil {
		fmt.Println(err)
	}
	/*************************************/

	time.Sleep(1 * time.Second)

	url := "http://localhost:9010/downloads/Invoices_Excel.xlsx"

	timeout := time.Duration(5) * time.Second
	transport := &http.Transport{
		ResponseHeaderTimeout: timeout,
		Dial: func(network, addr string) (net.Conn, error) {
			return net.DialTimeout(network, addr, timeout)
		},
		DisableKeepAlives: true,
	}
	client := &http.Client{
		Transport: transport,
	}
	resp, err := client.Get(url)
	if err != nil {
		fmt.Println(err)
	}
	defer resp.Body.Close()

	//copy the relevant headers. If you want to preserve the downloaded file name, extract it with go's url parser.
	w.Header().Set("Content-Disposition", "attachment; filename=Invoices_Excel.xlsx")
	w.Header().Set("Content-Type", r.Header.Get("Content-Type"))
	w.Header().Set("Content-Length", r.Header.Get("Content-Length"))

	//stream the body to the client without fully loading it into memory
	io.Copy(w, resp.Body)
}

func ExportInvoicesCsv(db *gorm.DB, w http.ResponseWriter, r *http.Request) {

	// Retrieve data
	vars := mux.Vars(r)
	startDate := utils.FormatReportDateStringTimestamp(vars["startdate"])
	endDate := utils.FormatReportDateStringTimestamp(vars["enddate"])
	reportsFilter := models.ReportsFilter{}
	reportsFilter.StartDate = startDate
	reportsFilter.EndDate = endDate
	invoices := []models.Invoice{}
	db.Order("Id DESC").Where("date_created BETWEEN ? and ? ", startDate, endDate).Find(&invoices)

	fileName := "./downloads/Invoices_CSV.csv"
	file, _ := os.Create(fileName)
	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	data := []string{}
	data = append(data, "#")
	data = append(data, "Invoice Number")
	data = append(data, "Date Created")
	data = append(data, "VAT")
	data = append(data, "Grand Total")
	data = append(data, "Signature")
	data = append(data, "File Name")
	writer.Write(data)

	for _, invoice := range invoices {

		data := []string{}
		data = append(data, utils.ConvertIntToString(invoice.ID))
		data = append(data, invoice.InvoiceNumber)
		data = append(data, invoice.DateCreated.String())
		data = append(data, invoice.Vat)
		data = append(data, invoice.GrandTotal)
		data = append(data, invoice.Signature)
		data = append(data, invoice.FileName)
		writer.Write(data)
	}

	respondJSON(w, http.StatusOK, invoices)
}

func ExportInvoicesPdf(db *gorm.DB, w http.ResponseWriter, r *http.Request) {

	// Retrieve data
	log.Println("Exporting data to Pdf....")
	vars := mux.Vars(r)
	startDate := utils.FormatReportDateStringTimestamp(vars["startdate"])
	endDate := utils.FormatReportDateStringTimestamp(vars["enddate"])
	reportsFilter := models.ReportsFilter{}
	reportsFilter.StartDate = startDate
	reportsFilter.EndDate = endDate
	invoices := []models.Invoice{}
	db.Order("Id DESC").Where("date_created BETWEEN ? and ? ", startDate, endDate).Find(&invoices)

	fileName := "./downloads/Invoices_PDF.pdf"
	file, _ := os.Create(fileName)
	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	log.Println("Writting data to PDF....")

	pdf := gofpdf.New("P", "mm", "A4", "")
	pdf.AddPage()
	pdf.SetFont("Arial", "B", 14)
	pdf.Cell(40, 10, "# | Invoice Number | Date Created | Vat | Grand Total | Signature | Filename ")
	pdf.OutputFileAndClose(fileName)

	for _, invoice := range invoices {

		data := []string{}
		data = append(data, utils.ConvertIntToString(invoice.ID))
		data = append(data, invoice.InvoiceNumber)
		data = append(data, invoice.DateCreated.String())
		data = append(data, invoice.Vat)
		data = append(data, invoice.GrandTotal)
		data = append(data, invoice.Signature)
		data = append(data, invoice.FileName)
		writer.Write(data)
	}

	log.Println("Data written to PDF.")

	respondJSON(w, http.StatusOK, invoices)
}

func ExportUsersExcel(db *gorm.DB, w http.ResponseWriter, r *http.Request) {

	// Retrieve data
	vars := mux.Vars(r)
	startDate := utils.FormatReportDateStringTimestamp(vars["startdate"])
	endDate := utils.FormatReportDateStringTimestamp(vars["enddate"])
	reportsFilter := models.ReportsFilter{}
	reportsFilter.StartDate = startDate
	reportsFilter.EndDate = endDate
	users := []models.User{}
	db.Order("Id DESC").Find(&users)

	// Create Excel File
	var file *xlsx.File
	var sheet *xlsx.Sheet
	var row *xlsx.Row
	var cell *xlsx.Cell
	var err error

	file = xlsx.NewFile()
	sheet, err = file.AddSheet("Sheet1")
	if err != nil {
		fmt.Printf(err.Error())
	}

	row = sheet.AddRow()
	headers := []string{"#", "First Name", "Last Name", "Email", "Phone", "Date Created"}

	for _, header := range headers {
		cell = row.AddCell()
		cell.Value = header
	}

	for i, user := range users {
		row = sheet.AddRow()
		cell = row.AddCell()
		cell.Value = utils.ConvertIntToString(i + 1)
		cell = row.AddCell()
		cell.Value = user.Firstname
		cell = row.AddCell()
		cell.Value = user.Lastname
		cell = row.AddCell()
		cell.Value = user.Email
		cell = row.AddCell()
		cell.Value = user.Msisdn
		cell = row.AddCell()
		cell.Value = user.CreationTime.String()
	}

	fileName := "./downloads/Users_Excel.xlsx"
	err = file.Save(fileName)
	if err != nil {
		fmt.Printf(err.Error())
	}

	url := "http://localhost:9010/downloads/Users_Excel.xlsx"

	timeout := time.Duration(5) * time.Second
	transport := &http.Transport{
		ResponseHeaderTimeout: timeout,
		Dial: func(network, addr string) (net.Conn, error) {
			return net.DialTimeout(network, addr, timeout)
		},
		DisableKeepAlives: true,
	}
	client := &http.Client{
		Transport: transport,
	}
	resp, err := client.Get(url)
	if err != nil {
		fmt.Println(err)
	}
	defer resp.Body.Close()

	//copy the relevant headers. If you want to preserve the downloaded file name, extract it with go's url parser.
	w.Header().Set("Content-Disposition", "attachment; filename=Users_Excel.xlsx")
	w.Header().Set("Content-Type", r.Header.Get("Content-Type"))
	w.Header().Set("Content-Length", r.Header.Get("Content-Length"))

	//stream the body to the client without fully loading it into memory
	io.Copy(w, resp.Body)
}

func ExportUsersCsv(db *gorm.DB, w http.ResponseWriter, r *http.Request) {

	// Retrieve data
	vars := mux.Vars(r)
	startDate := utils.FormatReportDateStringTimestamp(vars["startdate"])
	endDate := utils.FormatReportDateStringTimestamp(vars["enddate"])
	reportsFilter := models.ReportsFilter{}
	reportsFilter.StartDate = startDate
	reportsFilter.EndDate = endDate
	users := []models.User{}
	db.Order("Id DESC").Find(&users)

	fileName := "./downloads/Users_CSV.csv"
	file, _ := os.Create(fileName)
	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	data := []string{}
	data = append(data, "#")
	data = append(data, "First Name")
	data = append(data, "Last Name")
	data = append(data, "Email")
	data = append(data, "Phone")
	data = append(data, "Date Created")
	writer.Write(data)

	for _, user := range users {

		data := []string{}
		data = append(data, utils.ConvertIntToString(user.ID))
		data = append(data, user.Firstname)
		data = append(data, user.Lastname)
		data = append(data, user.Email)
		data = append(data, user.Phone)
		data = append(data, user.CreationTime.String())
		writer.Write(data)
	}

	respondJSON(w, http.StatusOK, users)
}

func ExportUsersPdf(db *gorm.DB, w http.ResponseWriter, r *http.Request) {

	// Retrieve data
	log.Println("Exporting data to Pdf....")
	vars := mux.Vars(r)
	startDate := utils.FormatReportDateStringTimestamp(vars["startdate"])
	endDate := utils.FormatReportDateStringTimestamp(vars["enddate"])
	reportsFilter := models.ReportsFilter{}
	reportsFilter.StartDate = startDate
	reportsFilter.EndDate = endDate
	invoices := []models.Invoice{}
	db.Order("Id DESC").Where("date_created BETWEEN ? and ? ", startDate, endDate).Find(&invoices)

	fileName := "./downloads/Users_PDF.pdf"
	file, _ := os.Create(fileName)
	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	log.Println("Writting data to PDF....")

	pdf := gofpdf.New("P", "mm", "A4", "")
	pdf.AddPage()
	pdf.SetFont("Arial", "B", 14)
	pdf.Cell(40, 10, "# | Invoice Number | Date Created | Vat | Grand Total | Signature | Filename ")
	pdf.OutputFileAndClose(fileName)

	for _, invoice := range invoices {

		data := []string{}
		data = append(data, utils.ConvertIntToString(invoice.ID))
		data = append(data, invoice.InvoiceNumber)
		data = append(data, invoice.DateCreated.String())
		data = append(data, invoice.Vat)
		data = append(data, invoice.GrandTotal)
		data = append(data, invoice.Signature)
		data = append(data, invoice.FileName)
		writer.Write(data)
	}

	log.Println("Data written to PDF.")

	respondJSON(w, http.StatusOK, invoices)
}
