package repos

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"time"
)

const (
	countIncomeTrackingsSQL = `SELECT COUNT(*) AS count FROM income_trackings WHERE 1=1`

	createIncomeTrackingSQL = `INSERT INTO income_trackings (balance, tracking_date, created_at, 
		updated_at) VALUES ($1, $2, $3, $4) RETURNING id`

	selectIncomeTrackingByTrackingDateSQL = selectIncomeTrackingSQL + ` WHERE tracking_date=$1`

	selectIncomeTrackingByIDSQL = selectIncomeTrackingSQL + ` WHERE id=$1`

	selectIncomeTrackingSQL = `SELECT id, balance, tracking_date, created_at, updated_at FROM income_trackings`

	updateIncomeTrackingSQL = `UPDATE income_trackings SET balance=$1, tracking_date=$2, updated_at=$3 WHERE id=$4`
)

type (
	IncomeTrackingRepository interface {
		CountIncomeTrackings(context.Context, *entities.PaginationFilter) (int, error)
		FilterIncomeTrackings(context.Context, txns_db.TransactionsSQLOperations, *entities.PaginationFilter) ([]*entities.IncomeTracking, error)
		FindByDate(context.Context, time.Time) (*entities.IncomeTracking, error)
		FindByID(context.Context, int64) (*entities.IncomeTracking, error)
		Save(context.Context, txns_db.TransactionsSQLOperations, *entities.IncomeTracking) error
	}

	AppIncomeTrackingRepository struct {
		db *sql.DB
	}
)

func NewIncomeTrackingRepository(db *sql.DB) IncomeTrackingRepository {
	return &AppIncomeTrackingRepository{db: db}
}

func (r *AppIncomeTrackingRepository) CountIncomeTrackings(
	ctx context.Context,
	filter *entities.PaginationFilter,
) (int, error) {
	var count int
	query, args, _ := r.buildQueryFromFilter(countIncomeTrackingsSQL, filter, []interface{}{}, 1)
	row := r.db.QueryRowContext(ctx, query, args...)
	err := row.Scan(&count)
	return count, err
}

func (r *AppIncomeTrackingRepository) FilterIncomeTrackings(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) ([]*entities.IncomeTracking, error) {

	incomeTrackings := make([]*entities.IncomeTracking, 0)

	args := make([]interface{}, 0)
	query := selectItemSQL
	currentIndex := 1

	query, args, currentIndex = r.buildQueryFromFilter(query, filter, args, currentIndex)
	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return incomeTrackings, err
	}

	defer rows.Close()

	for rows.Next() {
		incomeTracking, err := r.scanRowIntoIncomeTracking(rows)
		if err != nil {
			return incomeTrackings, err
		}

		incomeTrackings = append(incomeTrackings, incomeTracking)
	}

	return incomeTrackings, rows.Err()
}

func (r *AppIncomeTrackingRepository) FindByDate(
	ctx context.Context,
	trackingDate time.Time,
) (*entities.IncomeTracking, error) {
	row := r.db.QueryRow(selectIncomeTrackingByTrackingDateSQL, trackingDate)
	return r.scanRowIntoIncomeTracking(row)
}

func (r *AppIncomeTrackingRepository) FindByID(
	ctx context.Context,
	id int64,
) (*entities.IncomeTracking, error) {
	row := r.db.QueryRow(selectIncomeTrackingByIDSQL, id)
	return r.scanRowIntoIncomeTracking(row)
}

func (r *AppIncomeTrackingRepository) Save(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	incomeTracking *entities.IncomeTracking,
) error {

	incomeTracking.Timestamps.Touch()
	var err error

	if incomeTracking.IsNew() {

		res := operations.QueryRowContext(
			ctx,
			createIncomeTrackingSQL,
			incomeTracking.Balance,
			incomeTracking.TrackingDate,
			incomeTracking.CreatedAt,
			incomeTracking.UpdatedAt,
		)

		err = res.Scan(&incomeTracking.ID)

	} else {

		_, err = operations.ExecContext(
			ctx,
			updateIncomeTrackingSQL,
			incomeTracking.Balance,
			incomeTracking.TrackingDate,
			incomeTracking.UpdatedAt,
			incomeTracking.ID,
		)
	}

	if err != nil {
		log.Printf("error saving user Credit Balance, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppIncomeTrackingRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	if filter.UserID > 0 {
		query += fmt.Sprintf(" AND user_id = $%d", currentIndex)
		args = append(args, filter.UserID)
		currentIndex += 1
	}

	return query, args, currentIndex
}

func (r *AppIncomeTrackingRepository) scanRowIntoIncomeTracking(
	rowScanner txns_db.RowScanner,
) (*entities.IncomeTracking, error) {

	var incomeTracking entities.IncomeTracking

	err := rowScanner.Scan(
		&incomeTracking.ID,
		&incomeTracking.Balance,
		&incomeTracking.TrackingDate,
		&incomeTracking.CreatedAt,
		&incomeTracking.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning incomeTracking,  err=[%v]\n", err.Error())
		return &incomeTracking, err
	}

	return &incomeTracking, nil
}
