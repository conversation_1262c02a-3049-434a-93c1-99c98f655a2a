package repos

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
)

const (
	countTransactionsSQL = `SELECT COUNT(*) AS count FROM transactions WHERE 1=1`

	createTransactionSQL = `INSERT INTO transactions (user_id, amount, description, status, transaction_type, created_at, 
		updated_at) VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING id`

	selectTransactionByIDSQL = selectTransactionSQL + ` WHERE id=$1`

	selectTransactionSQL = `SELECT id, user_id, credit_balance, deleted_at, created_at, updated_at FROM transactions`

	updateTransactionSQL = `UPDATE transactions SET credit_balance=$1, updated_at=$2 WHERE id=$3`
)

type (
	TransactionRepository interface {
		CountTransactions(context.Context, *entities.PaginationFilter) int
		FindByID(context.Context, int64) (*entities.Transaction, error)
		Save(context.Context, txns_db.TransactionsSQLOperations, *entities.Transaction) error
	}

	AppTransactionRepository struct {
		db *sql.DB
	}
)

func NewTransactionRepository(db *sql.DB) TransactionRepository {
	return &AppTransactionRepository{db: db}
}

func (r *AppTransactionRepository) CountTransactions(
	ctx context.Context,
	filter *entities.PaginationFilter,
) int {
	var count int
	query, args, _ := r.buildQueryFromFilter(countTransactionsSQL, filter, []interface{}{}, 1)
	row := r.db.QueryRowContext(ctx, query, args...)
	row.Scan(&count)
	return count
}

func (r *AppTransactionRepository) FindByID(
	ctx context.Context,
	id int64,
) (*entities.Transaction, error) {
	row := r.db.QueryRow(selectTransactionByIDSQL, id)
	return r.scanRowIntoTransaction(row)
}

func (r *AppTransactionRepository) Save(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	transaction *entities.Transaction,
) error {

	transaction.Timestamps.Touch()
	var err error

	if transaction.IsNew() {

		res := operations.QueryRowContext(
			ctx,
			createTransactionSQL,
			transaction.UserID,
			transaction.Amount,
			transaction.Description,
			transaction.Status,
			transaction.TransactionType,
			transaction.CreatedAt,
			transaction.UpdatedAt,
		)

		err = res.Scan(&transaction.ID)

	} else {

		_, err = operations.ExecContext(
			ctx,
			updateTransactionSQL,
			transaction.UpdatedAt,
			transaction.ID,
		)
	}

	if err != nil {
		log.Printf("error saving user Credit Balance, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppTransactionRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	if filter.UserID > 0 {
		query += fmt.Sprintf(" AND user_id = $%d", currentIndex)
		args = append(args, filter.UserID)
		currentIndex += 1
	}

	return query, args, currentIndex
}

func (r *AppTransactionRepository) scanRowIntoTransaction(
	rowScanner txns_db.RowScanner,
) (*entities.Transaction, error) {

	var transaction entities.Transaction

	err := rowScanner.Scan(
		&transaction.ID,
		&transaction.UserID,
		&transaction.Amount,
		&transaction.Description,
		&transaction.Status,
		&transaction.CreatedAt,
		&transaction.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning transaction,  err=[%v]\n", err.Error())
		return &transaction, err
	}

	return &transaction, nil
}
