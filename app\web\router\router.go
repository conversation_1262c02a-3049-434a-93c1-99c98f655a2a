package router

import (
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/services"

	"compliance-and-risk-management-backend/app/web/api/analytics"
	"compliance-and-risk-management-backend/app/web/api/api_keys"
	"compliance-and-risk-management-backend/app/web/api/categories"
	"compliance-and-risk-management-backend/app/web/api/credit_notes"
	"compliance-and-risk-management-backend/app/web/api/currencies"
	"compliance-and-risk-management-backend/app/web/api/customers"
	"compliance-and-risk-management-backend/app/web/api/etims_items"
	"compliance-and-risk-management-backend/app/web/api/etims_oscu"
	"compliance-and-risk-management-backend/app/web/api/etims_stock"
	"compliance-and-risk-management-backend/app/web/api/etims_vscu"
	"compliance-and-risk-management-backend/app/web/api/excel_file_uploads"
	"compliance-and-risk-management-backend/app/web/api/income_tracking"
	"compliance-and-risk-management-backend/app/web/api/instagram"
	"compliance-and-risk-management-backend/app/web/api/invoices"
	"compliance-and-risk-management-backend/app/web/api/items"
	"compliance-and-risk-management-backend/app/web/api/payments"
	"compliance-and-risk-management-backend/app/web/api/phone_number_hashes"
	"compliance-and-risk-management-backend/app/web/api/receivings"
	"compliance-and-risk-management-backend/app/web/api/sales"
	"compliance-and-risk-management-backend/app/web/api/sessions"
	"compliance-and-risk-management-backend/app/web/api/settings"
	"compliance-and-risk-management-backend/app/web/api/stores"
	"compliance-and-risk-management-backend/app/web/api/suppliers"
	"compliance-and-risk-management-backend/app/web/api/user_credits"
	"compliance-and-risk-management-backend/app/web/api/users"
	"compliance-and-risk-management-backend/app/web/auth"
	analytics_ui "compliance-and-risk-management-backend/app/web/ui/analytics"
	dashboard_ui "compliance-and-risk-management-backend/app/web/ui/dashboard"
	invoices_ui "compliance-and-risk-management-backend/app/web/ui/invoices"
	login_ui "compliance-and-risk-management-backend/app/web/ui/login"
	logout_ui "compliance-and-risk-management-backend/app/web/ui/logout"

	"github.com/gin-gonic/gin"
)

func AddStrutsOptimusEndpoints(
	appRouter *gin.Engine,
	transactionsDB *database.AppTransactionsDB,
	sessionAuthenticator auth.SessionAuthenticator,
	activityRepository repos.ActivityRepository,
	apiKeyRepository repos.APIKeyRepository,
	apiKeyService services.APIKeyService,
	creditNoteService services.CreditNoteService,
	etimsItemService services.EtimsItemService,
	etimsStockService services.EtimsStockService,
	etimsVSCUService services.EtimsVSCUService,
	excelFileUploadService services.ExcelFileUploadService,
	userRepository repos.UserRepository,
	analyticsService services.AnalyticsService,
	categoryService services.CategoryService,
	currencyService services.CurrencyService,
	customerService services.CustomerService,
	incomeTrackingService services.IncomeTrackingService,
	invoiceService services.InvoiceService,
	itemService services.ItemService,
	paymentService services.PaymentService,
	paymentTransactionsService services.PaymentTransactionsService,
	phoneNumberHasheservice services.PhoneNumberHashService,
	receivingService services.ReceivingService,
	saleService services.SaleService,
	settingService services.SettingService,
	sessionService services.SessionService,
	storeRepository repos.StoreRepository,
	storeService services.StoreService,
	supplierService services.SupplierService,
	userCreditService services.UserCreditService,
	userService services.UserService,
) {

	strutsRouterGroup := appRouter.Group("/api")

	analytics.AddEndpoints(strutsRouterGroup, transactionsDB, analyticsService, sessionAuthenticator, sessionService, storeRepository)
	api_keys.AddEndpoints(strutsRouterGroup, transactionsDB, apiKeyService, sessionAuthenticator, sessionService)
	categories.AddEndpoints(strutsRouterGroup, transactionsDB, categoryService, sessionAuthenticator, sessionService)

	credit_notes.AddEndpoints(
		strutsRouterGroup,
		transactionsDB,
		apiKeyRepository,
		creditNoteService,
		sessionAuthenticator,
		sessionService,
	)

	currencies.AddEndpoints(strutsRouterGroup, transactionsDB, currencyService, sessionAuthenticator, sessionService)
	customers.AddEndpoints(strutsRouterGroup, transactionsDB, customerService, sessionAuthenticator, sessionService)

	etims_oscu.AddEndpoints(
		strutsRouterGroup,
		transactionsDB,
		apiKeyRepository,
		apiKeyService,
		etimsVSCUService,
		sessionAuthenticator,
		sessionService,
	)

	etims_vscu.AddEndpoints(
		strutsRouterGroup,
		transactionsDB,
		apiKeyRepository,
		apiKeyService,
		etimsVSCUService,
		sessionAuthenticator,
		sessionService,
	)

	etims_items.AddEndpoints(
		strutsRouterGroup,
		transactionsDB,
		apiKeyRepository,
		apiKeyService,
		etimsItemService,
		sessionAuthenticator,
		sessionService,
	)

	etims_stock.AddEndpoints(
		strutsRouterGroup,
		transactionsDB,
		apiKeyRepository,
		apiKeyService,
		etimsStockService,
		sessionAuthenticator,
		sessionService,
	)

	excel_file_uploads.AddEndpoints(strutsRouterGroup, transactionsDB, excelFileUploadService, sessionAuthenticator, sessionService)

	income_tracking.AddEndpoints(strutsRouterGroup, transactionsDB, incomeTrackingService, sessionAuthenticator, sessionService)
	instagram.AddEndpoints(strutsRouterGroup, transactionsDB, apiKeyService, sessionAuthenticator, sessionService)
	invoices.AddEndpoints(strutsRouterGroup, transactionsDB, invoiceService, sessionAuthenticator, sessionService)
	items.AddEndpoints(strutsRouterGroup, transactionsDB, itemService, sessionAuthenticator, sessionService)

	payments.AddEndpoints(
		strutsRouterGroup,
		transactionsDB,
		paymentService,
		paymentTransactionsService,
		sessionAuthenticator,
		sessionService,
	)

	phone_number_hashes.AddEndpoints(
		strutsRouterGroup,
		transactionsDB,
		apiKeyRepository,
		phoneNumberHasheservice,
		sessionAuthenticator,
		sessionService,
	)

	receivings.AddEndpoints(strutsRouterGroup, transactionsDB, receivingService, sessionAuthenticator, sessionService)
	sales.AddEndpoints(strutsRouterGroup, transactionsDB, saleService, sessionAuthenticator, sessionService)
	sessions.AddEndpoints(strutsRouterGroup, transactionsDB, userService)
	settings.AddEndpoints(strutsRouterGroup, transactionsDB, settingService, sessionAuthenticator, sessionService)
	stores.AddEndpoints(strutsRouterGroup, transactionsDB, storeService, sessionAuthenticator, sessionService)
	suppliers.AddEndpoints(strutsRouterGroup, transactionsDB, supplierService, sessionAuthenticator, sessionService)
	user_credits.AddEndpoints(strutsRouterGroup, transactionsDB, userCreditService, sessionAuthenticator, sessionService)
	users.AddEndpoints(strutsRouterGroup, transactionsDB, userService, sessionAuthenticator, sessionService)

	uiRouterGroup := appRouter.Group("/go")

	analytics_ui.AddEndpoints(uiRouterGroup, transactionsDB, analyticsService)
	dashboard_ui.AddEndpoints(uiRouterGroup, transactionsDB, analyticsService)
	invoices_ui.AddEndpoints(appRouter, uiRouterGroup, transactionsDB, analyticsService, invoiceService)
	logout_ui.AddEndpoints(uiRouterGroup, transactionsDB, userService)
	login_ui.AddEndpoints(uiRouterGroup, transactionsDB, userService)
}
