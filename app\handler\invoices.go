package handler

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/invoicetemplates"
	"compliance-and-risk-management-backend/app/models"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/utils"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/gorilla/mux"
	"github.com/jinzhu/gorm"
	pdfcpu "github.com/pdfcpu/pdfcpu/pkg/api"
	"github.com/skip2/go-qrcode"
)

func ProcessInvoices(
	operations txns_db.TransactionsSQLOperations,
	invoiceService services.InvoiceService,
) {

	// Read input folder
	// Extract data from PDF
	// Save data to db
	// Export to excel file
	// END

	utils.GetTodaysInvoiceDate()
	utils.CreateDirectories()

	inputFolder := os.Getenv("INPUT_FOLDER")
	backupFolder := os.Getenv("BACKUP_FOLDER")
	outputFolder := os.Getenv("OUTPUT_FOLDER")
	txtFolder := os.Getenv("TXT_FOLDER")
	pdfFolder := "C:\\ESD_DOCUMENTS\\PDF"
	mergePDFFolder := "C:\\ESD_DOCUMENTS\\Merge_PDF"
	// excelFilePath := outputFolder + "//Invoices.xlsx"

	files, err := os.ReadDir(inputFolder + "/")
	if err != nil {
		log.Println("Error processing inputFolder contents >>> ", err)
	}

out:
	for _, f := range files {

		fileName := f.Name()
		log.Println("Found File  >>> ", fileName)

		// Delete unrecognized files
		if !strings.HasSuffix(strings.ToUpper(f.Name()), ".PDF") {
			fmt.Printf("Deleting unrecognized file=[%v]...\n", f.Name())
			utils.DeleteFile(inputFolder + "//" + f.Name())
			break out
		}

		// Delay for specified config seconds to allow for files copying
		time.Sleep(500 * time.Millisecond)

		if strings.Contains(fileName, " ") {
			newFileName := strings.Replace(fileName, " ", "", -1)
			utils.CopyFile(inputFolder+"//"+fileName, inputFolder+"//"+newFileName)
			utils.DeleteFile(inputFolder + "//" + fileName)
			fileName = newFileName
		}

		// Backup the file
		utils.CopyFile(inputFolder+"//"+fileName, backupFolder+"//"+fileName)
		utils.CopyFile(inputFolder+"//"+fileName, "invoice.pdf")
		time.Sleep(300 * time.Millisecond) // Allow for copying - especially large files

		// Delete file so that it's not picked again for processing
		utils.DeleteFile(inputFolder + "//" + fileName)

		utils.SplitPDF(backupFolder + "//" + fileName)
		time.Sleep(500 * time.Millisecond) // Allow for splitting

		pdfFolderName := strings.Replace(fileName, ".pdf", "", -1)
		os.Mkdir("C:\\ESD_DOCUMENTS\\Txt\\"+pdfFolderName, 0777)

		// Copy filies to a created dir under PDF
		os.Mkdir(mergePDFFolder+"\\"+pdfFolderName, 0777)

		pdfFolderDir := pdfFolder + "//" + pdfFolderName
		splitFiles, err := os.ReadDir(pdfFolderDir)
		if err != nil {
			// log.Println("Error spliting pdf file.. processing...")
			time.Sleep(500 * time.Millisecond)
		}

		splitFiles = utils.ReverseStrinArr(splitFiles)
		splitFilesCount := len(splitFiles)
		fileCounter := 0
		invoice := &entities.Invoice{}

		// for _, splitFile := range splitFiles {
		for i := splitFilesCount; i > 0; i-- {

			splitFileName := pdfFolderName + "-split-" + utils.ConvertIntToString(i) + ".pdf"
			fmt.Printf("Processing split fileName=[%v] \n", splitFileName)

			pdfFile := pdfFolderDir + "//" + splitFileName
			txtFile := txtFolder + "//" + pdfFolderName + "//" + splitFileName + ".txt"

			utils.ConvertPDFtoTxt(pdfFile, txtFile)

			// Determine the invoice type from the 7 templates
			// Route invoice respectively to correct handler
			err = invoicetemplates.GetInvoiceType(txtFile, invoice)
			if err != nil {
				fmt.Printf("err processing invoice type, err=[%v]\n", err)
			}

			invoice.FileName = outputFolder + "\\" + pdfFolderName + ".pdf"
			invoice.OriginalFileName = outputFolder + "\\" + pdfFolderName + ".pdf"

			// Double check amounts
			// invoice = invoicetemplates.ValidateTotals(invoice)

			// fmt.Printf("fileCounter=[%v], splitFilesCount=[%v] \n", fileCounter, splitFilesCount)

			if fileCounter == (splitFilesCount - 1) {
				err = invoiceService.SaveInvoice(context.Background(), operations, invoice)
				if err != nil {
					log.Printf("failed to save invoice, err=[%v]\n", err)
				}
			}

			// Sign invoice txt file
			invoiceContent := "Nyota Tanzania Limited INVOICE\n"
			invoiceContent += "TIN Number         : 100-235-544 \n"
			invoiceContent += "VRN Number         : 10-011705-X \n"
			invoiceContent += "Invoice Number     : " + invoice.InvoiceNumber + "\n"
			invoiceContent += "Invoice Date       : " + invoice.InvoiceDate + "\n"
			invoiceContent += "Customer TIN       : " + invoice.CustomerTIN + "\n"
			invoiceContent += "Customer Name      : " + invoice.CustomerName + "\n"
			invoiceContent += "Customer Number    : " + invoice.CustomerNumber + "\n"
			invoiceContent += "Net Amount         : " + utils.ConvertFloat64ToString(invoice.NetAmount) + "\n"
			invoiceContent += "VAT                : " + utils.ConvertFloat64ToString(invoice.Vat) + "\n"
			invoiceContent += "Grand Total        : " + utils.ConvertFloat64ToString(invoice.GrandTotal) + "\n"
			invoiceContent += "Signature          : " + invoice.Signature + "\n"
			invoiceContent += "Verification URL   : " + invoice.VerificationURL + "\n"

			fmt.Printf("%+v\n", invoiceContent)

			time.Sleep(500 * time.Millisecond)

			// Append data to excel file
			// Deprecated, user to export invoices on demand
			// utils.AppendDataToExcelFile(excelFilePath, invoice)

			fileCounter++
		}

		fmt.Printf("Document processed, invoiceNumber=[%v], fileName=[%v]\n", invoice.InvoiceNumber, fileName)
		fmt.Printf("-------------------------------------------------------------------------------------------\n\n")
	}

}

func SignInvoice(db *gorm.DB, w http.ResponseWriter, r *http.Request) {

	// Check License validity
	license := os.Getenv("LICENSE")
	// log.Println("license      >>> ", license)
	isLicensed := ValidateLicense(license)
	// log.Println("isLicensed   >>> ", isLicensed)

	response := models.JSONResponse{}

	if isLicensed {
		// Get Posted invoice Details
		invoice := models.InvoiceReport{}
		decoder := json.NewDecoder(r.Body)
		if err := decoder.Decode(&invoice); err != nil {
			respondError(w, http.StatusBadRequest, err.Error())
			return
		}
		defer r.Body.Close()

		log.Println("Signing invoice with invoice number >>>> ", invoice.InvoiceNumber, " :",
			invoice.InvoiceDate, " | ", invoice.GrossAmount, " | ", invoice.Vat, " | ", invoice.GrandTotal)

		// Remember to trim the invoice number
		invoiceNumberString := strings.Replace(invoice.InvoiceNumber, " ", "", -1)
		invoiceFileName := "C:\\ESD_DOCUMENTS\\Backup\\" + invoiceNumberString + ".txt"
		// Create text file - with InvoiceNumber as file name
		invoiceFile, err := os.Create(invoiceFileName)
		utils.CheckError(err, "Error creating invoice file name ")

		invoiceDetails := "TAX INVOICE\n"
		invoiceDetails += "Invoice Date : " + invoice.InvoiceDate + "\n"
		invoiceDetails += "Invoice Number : " + invoice.InvoiceNumber + "\n"
		invoiceDetails += "Gross Amount : " + utils.FloatToString(invoice.GrossAmount) + "\n"
		invoiceDetails += "Vat : " + utils.FloatToString(invoice.Vat) + "\n"
		invoiceDetails += "Grand Total : " + utils.FloatToString(invoice.GrandTotal) + "\n"

		saveData, err2 := invoiceFile.WriteString(invoiceDetails)
		utils.CheckError(err2, "Error saving invoice data ")
		invoiceFile.Close()

		log.Println(saveData, " bytes written successfully.")

		// Sign - Copy to ESD Input
		esdInputFileName := "C:\\ESD_DOCUMENTS\\ESD_Input\\" + invoiceNumberString + ".txt"
		utils.CopyFile(invoiceFileName, esdInputFileName)

		// Retrieve Signature - File from ESD Output with the same filename
		signedInvoiceFileName := "C:\\ESD_DOCUMENTS\\ESD_Output\\" + invoiceNumberString + ".txt.s"
		invoiceErrorFile := "C:\\ESD_DOCUMENTS\\Error_Documents\\" + invoiceNumberString + ".txt"

		for !utils.FileExists(signedInvoiceFileName) && !utils.FileExists(invoiceErrorFile) {
			log.Println("Invoice not signed, waiting...")
			time.Sleep(500 * time.Millisecond) //  Delay for a second to allow for signing
		}

		if utils.FileExists(signedInvoiceFileName) {
			log.Println("Retrieving invoice signature...")
			b, err := ioutil.ReadFile(signedInvoiceFileName)
			if err != nil {
				log.Print(err)
			}
			invoiceSignature := string(b)
			invoiceSignature = strings.Replace(invoiceSignature, "\n", "", -1)
			log.Println("invoiceSignature  >>> ", invoiceSignature)
			invoice.Signature = invoiceSignature
			respondJSON(w, http.StatusOK, invoice)

		} else if utils.FileExists(invoiceErrorFile) {
			response.Status = "01"
			response.Description = "Could not sign invoice : Error in invoice details."
			respondJSON(w, http.StatusOK, response)

		} else {
			response.Status = "01"
			response.Description = "Could not sign invoice : Check ESD Connection."
			respondJSON(w, http.StatusOK, response)

		}
	} else {
		response.Status = "01"
		response.Description = "Licenese Expired! Please contact your system provider."
		respondJSON(w, http.StatusOK, response)
	}

}

// RetrieveSignature - RetrieveSignature
func RetrieveSignature(db *gorm.DB, w http.ResponseWriter, r *http.Request) {

	// Check License validity
	license := os.Getenv("LICENSE")
	// log.Println("license      >>> ", license)
	isLicensed := ValidateLicense(license)
	// log.Println("isLicensed   >>> ", isLicensed)

	response := models.JSONResponse{}

	if isLicensed {
		// Retrieve parsed invoice number
		vars := mux.Vars(r)
		invoiceNumber := vars["invoice_number"]

		log.Println("Checking signature for invoice number >>>> ", invoiceNumber)

		invoice := models.InvoiceSignature{}
		invoice.InvoiceNumber = invoiceNumber

		// Check whether invoice file name is available in file system
		invoiceNumberString := strings.Replace(invoice.InvoiceNumber, " ", "", -1)
		signedInvoiceFileName := "C:\\ESD_DOCUMENTS\\ESD_Output\\" + invoiceNumberString + ".txt.s"

		// If available, return signature
		// If not - return appropriate message
		invoiceErrorFile := "C:\\ESD_DOCUMENTS\\Error_Documents\\" + invoiceNumberString + ".txt"

		if utils.FileExists(signedInvoiceFileName) {
			// Retrieve and set signatue
			log.Println("Retrieving invoice signature...")
			b, err := ioutil.ReadFile(signedInvoiceFileName)
			if err != nil {
				log.Print(err)
			}
			// log.Println(b) // print the content as 'bytes'
			invoiceSignature := string(b) // convert content to a 'string'
			invoiceSignature = strings.Replace(invoiceSignature, "\n", "", -1)
			log.Println("invoiceSignature  >>> ", invoiceSignature)
			invoice.Signature = invoiceSignature
			respondJSON(w, http.StatusOK, invoice)

		} else if utils.FileExists(invoiceErrorFile) {
			// Error file exists
			response := models.JSONResponse{}
			response.Status = "01"
			response.Description = "Could not sign invoice : Error in invoice details."
			respondJSON(w, http.StatusOK, response)
		} else {
			response := models.JSONResponse{}
			response.Status = "01"
			response.Description = "Invoice not found!"
			respondJSON(w, http.StatusOK, response)
		}
	} else {
		response.Status = "01"
		response.Description = "Licenese Expired! Please contact your system provider."
		respondJSON(w, http.StatusOK, response)
	}
}

func ProcessOldSignedInvoices(db *gorm.DB) {
	for {

		utils.CreateDirectories()

		signedInputFolder := os.Getenv("OLD_SIGNED_INPUT_FOLDER")
		signedOutputFolder := os.Getenv("SIGNED_OUTPUT_FOLDER")

		// log.Println("signedInputFolder  >>> ", signedInputFolder)
		// log.Println("[" + signedInputFolder + "] Checking for old signed invoices to process...")

		// Read ESD_Documents Signed folder
		files, err := ioutil.ReadDir(signedInputFolder + "/")
		if err != nil {
			log.Println("Error processing signedInputFolder contents >>> ", err)
		}

		invoice := &entities.Invoice{}

		for _, f := range files {

			log.Println("Found File  >>> ", f.Name())

			// if invoice does not end with .pdf, delete it

			srcFile := signedInputFolder + "\\" + f.Name()
			backupFile := signedOutputFolder + "\\" + f.Name()
			destFile := f.Name()
			txtFile := f.Name() + ".txt"

			// Backup the file
			utils.CopyFile(srcFile, backupFile)

			// Remember to Take care of spaces in file name
			// Copy file to local directory for processing
			utils.CopyFile(srcFile, destFile)
			utils.ConvertPDFtoTxt(destFile, txtFile)

			// Determine the invoice type from the 7 templates
			// Route invoice respectively to correct handler
			invoicetemplates.GetInvoiceType(txtFile, invoice)
			invoice.FileName = backupFile
			invoice.OriginalFileName = f.Name()

			// Save to DB
			log.Println("------------------------[" + f.Name() + "]----------------------------------")
			log.Println("invoice.InvoiceNumber   >>> ", invoice.InvoiceNumber)
			log.Println("invoice.InvoiceDate     >>> ", invoice.InvoiceDate)
			log.Println("invoice.DateCreated     >>> ", invoice.DateCreated)
			log.Println("invoice.Vat             >>> ", invoice.Vat)
			log.Println("invoice.GrandTotal      >>> ", invoice.GrandTotal)
			log.Println("invoice.Signature       >>> ", invoice.Signature)
			log.Println("========================================================================")

			// response := saveInvoiceToDB(invoice)
			// Delete input file(s) after processing
			// delete invoice file(s)
			// if response > 0 {
			// 	utils.DeleteFile(srcFile)
			// 	utils.DeleteFile(destFile)
			// 	utils.DeleteFile(txtFile)
			// }
		}

		time.Sleep(500 * time.Millisecond) // Check every 2 seconds
	}
}

func SplitPDF() {
	// Create single page PDFs for in.pdf in outDir using the default configuration.
	pdfcpu.SplitFile("in.pdf", "outDir", 1, nil)
}

func SignInvoices(
	operations txns_db.TransactionsSQLOperations,
	invoiceService services.InvoiceService,
) {

	// Read input folder
	// Extract data from PDF
	// Sign - by pasting to ESD Input
	// Retrieve signature
	// Append signature to PDF
	// END

	utils.GetTodaysInvoiceDate()
	utils.CreateDirectories()

	inputFolder := os.Getenv("INPUT_FOLDER")
	backupFolder := os.Getenv("BACKUP_FOLDER")
	outputFolder := os.Getenv("OUTPUT_FOLDER")
	txtFolder := os.Getenv("TXT_FOLDER")
	esdInputFolder := os.Getenv("ESD_INPUT_FOLDER")
	emailSendingInputFolder := os.Getenv("EMAIL_SENDING_INPUT_FOLDER")
	pdfFolder := "C:\\ESD_DOCUMENTS\\PDF"
	mergePDFFolder := "C:\\ESD_DOCUMENTS\\Merge_PDF"
	qrCodeFolder := "C:\\ESD_DOCUMENTS\\QR_Codes"

	files, err := os.ReadDir(inputFolder + "/")
	if err != nil {
		log.Println("Error processing inputFolder contents >>> ", err)
	}

out:
	for _, f := range files {

		fileName := f.Name()
		log.Println("Found File  >>> ", fileName)

		// Delete unrecognized files
		if !strings.HasSuffix(strings.ToUpper(f.Name()), ".PDF") {
			fmt.Printf("Deleting unrecognized file=[%v]...\n", f.Name())
			utils.DeleteFile(inputFolder + "//" + f.Name())
			break out
		}

		utils.RemoveContents("C:\\ESD_DOCUMENTS\\Error_Documents")

		// Delay for specified config seconds to allow for files copying
		time.Sleep(500 * time.Millisecond)

		if strings.Contains(fileName, " ") {
			newFileName := strings.Replace(fileName, " ", "", -1)
			utils.CopyFile(inputFolder+"//"+fileName, inputFolder+"//"+newFileName)
			utils.DeleteFile(inputFolder + "//" + fileName)
			fileName = newFileName
		}

		// Backup the file
		utils.CopyFile(inputFolder+"//"+fileName, backupFolder+"//"+fileName)
		utils.CopyFile(inputFolder+"//"+fileName, "invoice.pdf")
		time.Sleep(300 * time.Millisecond) // Allow for copying - especially large files

		// Delete file so that it's not picked again for processing
		utils.DeleteFile(inputFolder + "//" + fileName)

		utils.SplitPDF(backupFolder + "//" + fileName)
		time.Sleep(500 * time.Millisecond) // Allow for splitting

		pdfFolderName := strings.Replace(fileName, ".pdf", "", -1)
		os.Mkdir("C:\\ESD_DOCUMENTS\\Txt\\"+pdfFolderName, 0777)

		// Copy filies to a created dir under PDF
		os.Mkdir(mergePDFFolder+"\\"+pdfFolderName, 0777)

		pdfFolderDir := pdfFolder + "//" + pdfFolderName
		splitFiles, err := os.ReadDir(pdfFolderDir)
		if err != nil {
			// log.Println("Error spliting pdf file.. processing...")
			time.Sleep(500 * time.Millisecond)
		}

		splitFiles = utils.ReverseStrinArr(splitFiles)
		splitFilesCount := len(splitFiles)
		fileCounter := 0
		invoice := &entities.Invoice{}
		customerEmail := ""
		signedOutputFile := ""

		// fmt.Printf("splitFilesCount=[%v], fileCounter=[%v] \n", splitFilesCount, fileCounter)

		// for _, splitFile := range splitFiles {
		for i := splitFilesCount; i > 0; i-- {

			splitFileName := pdfFolderName + "-split-" + utils.ConvertIntToString(i) + ".pdf"
			fmt.Printf("Processing split fileName=[%v] \n", splitFileName)

			pdfFile := pdfFolderDir + "//" + splitFileName
			txtFile := txtFolder + "//" + pdfFolderName + "//" + splitFileName + ".txt"

			utils.ConvertPDFtoTxt(pdfFile, txtFile)

			// Determine the invoice type from the 7 templates
			// Route invoice respectively to correct handler
			invoicetemplates.GetInvoiceType(txtFile, invoice)
			invoice.FileName = outputFolder + "\\" + pdfFolderName + ".pdf"
			invoice.OriginalFileName = outputFolder + "\\" + pdfFolderName + ".pdf"

			invoice = invoicetemplates.ValidateTotals(invoice)
			customerEmail = invoice.CustomerEmail

			if fileCounter == 0 {
				err = invoiceService.SaveInvoice(context.Background(), operations, invoice)
				if err != nil {
					log.Printf("failed to save invoice, err=[%v]\n", err)
				}
			}

			// Sign invoice txt file
			invoiceContent := "Ultimate Security INVOICE\n"
			invoiceContent += "TIN Number         : 100-106-086 \n"
			invoiceContent += "VRN Number         : 10-000078-Z \n"
			invoiceContent += "Invoice Number     : " + invoice.InvoiceNumber + "\n"
			invoiceContent += "Invoice Date       : " + invoice.InvoiceDate + "\n"
			invoiceContent += "Customer TIN       : " + invoice.CustomerTIN + "\n"
			invoiceContent += "Customer Name      : " + invoice.CustomerName + "\n"
			invoiceContent += "Customer Number    : " + invoice.CustomerPhone + "\n"
			invoiceContent += "Customer Email(s)  : " + customerEmail + "\n"
			invoiceContent += "Gross Amount       : " + utils.ConvertFloat64ToString(invoice.GrossAmount) + "\n"
			invoiceContent += "VAT                : " + utils.ConvertFloat64ToString(invoice.Vat) + "\n"
			invoiceContent += "Grand Total        : " + utils.ConvertFloat64ToString(invoice.GrandTotal) + "\n"

			signedOutputFile = mergePDFFolder + "\\" + pdfFolderName + "\\" + splitFileName
			qrCodePNGFile := qrCodeFolder + "\\" + invoice.InvoiceNumber + ".png"
			qrSignedOutputFile := mergePDFFolder + "\\" + pdfFolderName + "\\qr_" + splitFileName

			invoiceFolders := entities.InvoiceFolders{
				OutputFolder:       outputFolder,
				PDFFolderName:      pdfFolderName,
				SplitFileName:      fileName,
				PDFFile:            pdfFile,
				QRCodePNGFile:      qrCodePNGFile,
				QRSignedOutputFile: qrSignedOutputFile,
				SignedOutputFile:   signedOutputFile,
			}

			if fileCounter == 0 {

				fmt.Printf("%v\n", invoiceContent)

				esdTxtFile := txtFolder + "\\" + pdfFolderName + "\\" + splitFileName + ".txt"
				utils.WriteToTxtFile(esdTxtFile, invoiceContent)

				// Copy txt file to ESD_Input folder
				esdInputFile := esdInputFolder + "\\" + splitFileName + ".txt"
				utils.CopyFile(esdTxtFile, esdInputFile)

				// Wait a bit to allow for invoice signing
				time.Sleep(500 * time.Millisecond)

				err = readSignature(invoice, splitFileName+".txt")
				if err != nil {
					fmt.Printf("Error reading signature err=[%v]\n", err)
				}

				err = invoiceService.SaveInvoice(context.Background(), operations, invoice)
				if err != nil {
					log.Printf("failed to save signed invoice details, err=[%v]\n", err)
				}

			} else {

				invoice, err = invoiceService.GetInvoiceByInvoiceNumber(context.Background(), invoice.InvoiceNumber)
				if err != nil {
					log.Printf("failed to find invoice by invoiceNumber=[%v], err=[%v]\n", invoice.InvoiceNumber, err)
				}

				if utils.FileExists(qrCodePNGFile) && len(invoice.Signature) > 0 {
					appendQRCodeAndSignature(invoice, invoiceFolders)
				} else {
					utils.CopyFile(pdfFile, mergePDFFolder+"\\"+pdfFolderName+"\\"+fileName)
					utils.CopyFile(txtFile, "C:\\ESD_DOCUMENTS\\Error_Documents\\"+fileName+".txt")
				}
			}

			err = qrcode.WriteFile(invoice.VerificationURL, qrcode.Medium, 256, qrCodePNGFile)
			if err != nil {
				fmt.Printf("Error generating QR Code, Err=[%v]\n", err)
			}

			if len(invoice.Signature) > 0 && len(invoice.VerificationURL) > 0 {
				err = qrcode.WriteFile(invoice.VerificationURL, qrcode.Medium, 256, qrCodePNGFile)
				if err != nil {
					fmt.Printf("Error generating QR Code, Err=[%v]\n", err)
				}
				time.Sleep(100 * time.Millisecond) // Allow for QR Code file creation

				appendQRCodeAndSignature(invoice, invoiceFolders)
			}

			// utils.DeleteFile(esdTxtFile)
			time.Sleep(500 * time.Millisecond)
			utils.DeleteFile(qrSignedOutputFile)
			// utils.RemoveDirContents(outputFolder + "\\" + pdfFolderName)
			// utils.DeleteFile(outputFolder + "\\" + pdfFolderName)

			fileCounter++
		}

		// fmt.Printf("splitFilesCount=[%v], fileCounter=[%v] \n", splitFilesCount, fileCounter)

		if splitFilesCount == fileCounter {

			if fileCounter == 1 {
				utils.CopyFile(signedOutputFile, outputFolder+"//"+fileName)
				utils.CopyFile(signedOutputFile, emailSendingInputFolder+"//"+invoice.InvoiceNumber+".pdf")

			} else {
				utils.MergePDF(mergePDFFolder, pdfFolderName, outputFolder+"//"+fileName)
				time.Sleep(2000 * time.Millisecond)
				utils.CopyFile(mergePDFFolder+"//"+fileName, emailSendingInputFolder+"//"+invoice.InvoiceNumber+".pdf")
			}
		}

		fmt.Printf("Document signed, invoiceNumber=[%v], fileName=[%v]\n", invoice.InvoiceNumber, fileName)
		fmt.Printf("-------------------------------------------------------------------------------------------\n\n")
	}

}

func appendQRCodeAndSignature(
	invoice *entities.Invoice,
	invoiceFolders entities.InvoiceFolders) {

	// exec.Command("qr_code_util_v1.0.3.exe", srcPDF, destPDF, qrCodeImage, "45", "45", "60", "32").Run()
	signaturePositioning := &entities.SignaturePositioning{
		QRCodeBottomOffset:    "32",
		QRCodeLeftOffset:      "60",
		QRCodeSquareSize:      "45",
		SignatureLeftOffset:   "60",
		SignatureBottomOffset: "32",
	}
	invoice.SignaturePositioning = signaturePositioning

	// Append QR Code and signature if the qr code file exists
	utils.StampQRCode(invoiceFolders.PDFFile, invoiceFolders.QRSignedOutputFile, invoiceFolders.QRCodePNGFile, invoice)

	// Wait for a second to allow for document saving before appending signature
	time.Sleep(500 * time.Millisecond)

	utils.AppendSignatureToPDF(invoiceFolders.QRSignedOutputFile, invoiceFolders.SignedOutputFile, invoice)
	time.Sleep(500 * time.Millisecond)
}

func readSignature(
	invoice *entities.Invoice,
	txtFile string,
) error {

	esdOutputFolder := os.Getenv("ESD_OUTPUT_FOLDER")
	errorFolder := os.Getenv("ERROR_FOLDER")

	signedInvoiceFile := esdOutputFolder + "\\" + txtFile + ".sign"
	errorInvoiceFile := errorFolder + "\\" + txtFile

	for !utils.FileExists(signedInvoiceFile) && !utils.FileExists(errorInvoiceFile) {
		log.Println("Invoice not signed, waiting...")
		time.Sleep(500 * time.Millisecond) //  Delay for a second to allow for signing
	}

	file, err := os.Open(signedInvoiceFile)
	if err != nil {
		fmt.Printf("Error reading signed file err=[%v]\n", err)
		return err
	}

	defer file.Close()

	scanner := bufio.NewScanner(file)
	scanner.Split(bufio.ScanLines)

	for scanner.Scan() {

		signatureLine := scanner.Text()

		if strings.Contains(signatureLine, "#") {
			fmt.Printf("Signature = [%v]\n", signatureLine)
			invoice.Signature = signatureLine

		} else if strings.Contains(signatureLine, "tra.go.tz") {
			fmt.Printf("Verification URL = [%v]\n", signatureLine)
			invoice.VerificationURL = signatureLine
		}
	}

	return nil
}

func getSignature(signatureFile, errorFile string) string {

	signature := ""
	if utils.FileExists(signatureFile) {
		log.Println("Getting signature from signatureFile >>> ", signatureFile)
		b, err := ioutil.ReadFile(signatureFile) // just pass the file name
		if err != nil {
			log.Print(err)
		}
		// log.Println(b) // print the content as 'bytes'
		str := string(b) // convert content to a 'string'
		log.Println(str) // print the content as a 'string'
		signature = str
		log.Println("signature >>> ", signature)

	} else if utils.FileExists(errorFile) {
		log.Println("Error file exists.. exiting.. ")
	} else {
		time.Sleep(500 * time.Millisecond)
		signature = getSignature(signatureFile, errorFile)
	}

	return signature
}

func saveInvoiceToDB(db *gorm.DB, invoice *models.Invoice) int {

	// Implement updating of invoice if invoice already exists in the DB **
	dbInvoice := GetInvoiceByInvoiceNumber(db, invoice.InvoiceNumber)

	if dbInvoice.ID > 0 {

		// Update invoice details
		// db.model(&dbInvoice).UpdateColumns(models.Invoice{
		// 	DateCreated:     invoice.DateCreated,
		// 	FileName:        invoice.FileName,
		// 	GrandTotal:      invoice.GrandTotal,
		// 	InvoiceDate:     invoice.InvoiceDate,
		// 	IsEmailSent:     invoice.IsEmailSent,
		// 	Signature:       invoice.Signature,
		// 	Vat:             invoice.Vat,
		// 	VerificationURL: invoice.VerificationURL,
		// })
		// return dbInvoice.ID
		return 0

	} else {

		// Get new id - database primary key
		var lastInvoiceID models.Invoice
		db.Last(&lastInvoiceID)
		invoice.ID = lastInvoiceID.ID + 1

		if err := db.Save(invoice).Error; err != nil {
			utils.CheckError(err, "Error saving invoice file!")
			return 0
		}

		return invoice.ID
	}
}

// GetInvoiceByInvoiceNumber - GetInvoiceByInvoiceNumber
func GetInvoiceByInvoiceNumber(db *gorm.DB, invoiceNumber string) *models.Invoice {
	invoice := models.Invoice{}
	db.Where("invoice_number = ? ", invoiceNumber).Find(&invoice).First(&invoice)
	return &invoice
}

// GetInvoiceDetails - GetInvoiceDetails
func GetInvoiceDetails(db *gorm.DB, invoiceID int) *models.Invoice {
	invoice := models.Invoice{}
	db.Where("id = ? ", invoiceID).Find(&invoice).First(&invoice)
	return &invoice
}
