package entities

import "time"

type Asset struct {
	SequentialIdentifier
	BlurHash             *string     `json:"blur_hash"`
	ContentMD5           string      `json:"content_md5"`
	ContentSize          int         `json:"content_size"`
	ContentType          string      `json:"content_type"`
	Description          string      `json:"description"`
	Duration             *int        `json:"duration"`
	Name                 string      `json:"name"`
	Key                  string      `json:"key"`
	ImageLength          *int        `json:"image_length"`
	ImageWidth           *int        `json:"image_width"`
	URL                  string      `json:"url"`
	FailedAt             *time.Time  `json:"failed_at"`
	Reason               string      `json:"reason"`
	Status               AssetStatus `json:"status"`
	ThumbnailContentSize *int        `json:"thumbnail_content_size"`
	ThumbnailContentType *string     `json:"thumbnail_content_type"`
	ThumbnailKey         *string     `json:"thumbnail_key"`
	ThumbnailImageLength *int        `json:"thumbnail_image_length"`
	ThumbnailImageWidth  *int        `json:"thumbnail_image_width"`
	ThumbnailStatus      AssetStatus `json:"thumbnail_status"`
	ThumbnailFailedAt    *time.Time  `json:"thumbnail_failed_at"`
	ThumbnailUploadedAt  *time.Time  `json:"thumbnail_uploaded_at"`
	ThumbnailURL         string      `json:"thumbnail_url"`
	UploadedAt           *time.Time  `json:"uploaded_at"`
	Timestamps
}

type AssetList struct {
	Assets     []*Asset    `json:"assets"`
	Pagination *Pagination `json:"pagination"`
}

type AssetUploadURL struct {
	Asset     *Asset `json:"asset"`
	ExpiresAt int64  `json:"expires_at"`
	UploadURL string `json:"upload_url"`
}
