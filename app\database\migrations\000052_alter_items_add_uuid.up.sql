ALTER TABLE items ADD COLUMN uuid VARCHAR(250) NOT NULL DEFAULT '';

ALTER TABLE etims_items ADD COLUMN uuid VARCHAR(250) NOT NULL DEFAULT '';

ALTER TABLE invoice_items ADD COLUMN uuid VARCHAR(250) NOT NULL DEFAULT '';


DROP INDEX IF EXISTS etims_items_item_code_uidx; 

CREATE UNIQUE INDEX IF NOT EXISTS etims_items_item_code_uuid_uidx ON etims_items(itemCd, uuid);

DROP INDEX IF EXISTS invoice_items_invoice_id_name_uidx; 

CREATE UNIQUE INDEX IF NOT EXISTS invoice_items_invoice_id_name_uuid_uidx ON invoice_items(invoice_id, name, uuid);
