package utils

import (
	"fmt"
	"os/exec"
	"runtime"
)

func RunOSLevelCommand(command string, args ...string) (string, error) {
	var cmd *exec.Cmd
	if runtime.GOOS == "darwin" || runtime.GOOS == "linux" {
		bashCmds := []string{"-c", command}
		bashCmds = append(bashCmds, args...)
		cmd = exec.Command("bash", bashCmds...)
	} else {
		cmd = exec.Command(command, args...)
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		fmt.Printf("Error: %s\n", err)
		return "", err
	}

	return string(output), nil
}
