CREATE TABLE IF NOT EXISTS etims_items (
    id          BIGSERIAL                   PRIMARY KEY, 
    tin         VARCHAR(120)                NOT NULL,    
    bhfId       VARCHAR(255)                NOT NULL,    
    itemCd      VARCHAR(120),
    itemClsCd   VARCHAR(120),
    itemTyCd    VARCHAR(120),
    itemNm      TEXT,
    itemStdNm   TEXT,
    orgnNatCd   VARCHAR(120),
    pkgUnitCd   VARCHAR(120),
    qtyUnitCd   VARCHAR(120),
    taxTyCd     VARCHAR(120),
    btchNo      BIGINT, 
    bcd         VARCHAR(120),
    dftPrc      NUMERIC(18,2),
    grpPrcL1    NUMERIC(18,2),
    grpPrcL2    NUMERIC(18,2),
    grpPrcL3    NUMERIC(18,2),
    grpPrcL4    NUMERIC(18,2),
    grpPrcL5    NUMERIC(18,2),
    addInfo     TEXT,
    sftyQty     NUMERIC(13,2),
    isrcAplcbYn VARCHAR(1), 
    useYn       VARCHAR(1),
    regrNm      VARCHAR(255),
    regrId      VARCHAR(255),
    modrNm      VARCHAR(255),
    modrId      VARCHAR(255),
    created_at  TIMESTAMP WITH TIME ZONE    NOT NULL DEFAULT now(),
    updated_at  TIMESTAMP WITH TIME ZONE
);

CREATE UNIQUE INDEX IF NOT EXISTS etims_items_item_code_uidx ON etims_items(itemCd);
