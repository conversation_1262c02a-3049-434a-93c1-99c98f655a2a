package providers

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"

	"compliance-and-risk-management-backend/app/entities"
)

type (
	MPesaPhoneNumberHashDecoder interface {
		DecodePhoneNumber(phoneNumberHash string) (*entities.MPesaPhoneNumberHashDecoderResponse, error)
	}

	AppMPesaPhoneNumberHashDecoder struct {
		apiKey string
		apiURL string
	}
)

func NewMPesaPhoneNumberHashDecoder() MPesaPhoneNumberHashDecoder {
	return NewMPesaPhoneNumberHashDecoderWithCredentials(
		os.Getenv("MPESA_HASH_API_KEY"),
		os.Getenv("MPESA_HASH_API_URL"),
	)
}

func NewMPesaPhoneNumberHashDecoderWithCredentials(apiKey, apiURL string) MPesaPhoneNumberHashDecoder {
	return &AppMPesaPhoneNumberHashDecoder{
		apiKey: apiKey,
		apiURL: apiURL,
	}
}

func (s *AppMPesaPhoneNumberHashDecoder) DecodePhoneNumber(
	phoneNumberHash string,
) (*entities.MPesaPhoneNumberHashDecoderResponse, error) {

	phoneNumberHashRespnse := &entities.MPesaPhoneNumberHashDecoderResponse{}
	url := fmt.Sprintf("%v/%v", s.apiURL, phoneNumberHash)
	method := "GET"

	client := &http.Client{}
	req, err := http.NewRequest(method, url, nil)
	if err != nil {
		fmt.Printf("error initiating phone number hash request, err=[%v]\n", err)
		return phoneNumberHashRespnse, err
	}
	req.Header.Add("X-API-KEY", s.apiKey)

	res, err := client.Do(req)
	if err != nil {
		fmt.Printf("error sending phone number hash request, err=[%v]\n", err)
		return phoneNumberHashRespnse, err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		fmt.Printf("error reading phone number hash response, err=[%v]\n", err)
		return phoneNumberHashRespnse, err
	}

	err = json.Unmarshal([]byte(string(body)), &phoneNumberHashRespnse)
	if err != nil {
		fmt.Printf("error unmarshaling phone number hash decode response, err=[%v]\n", err)
		return phoneNumberHashRespnse, err
	}

	if phoneNumberHashRespnse == nil {
		return &entities.MPesaPhoneNumberHashDecoderResponse{
			Key:   phoneNumberHash,
			Value: "",
		}, nil
	}

	return phoneNumberHashRespnse, nil
}
