package entities

import "gopkg.in/guregu/null.v3"

type PhoneNumber struct {
	SequentialIdentifier
	CountryCode string  `db:"country_code" json:"country_code"`
	MpesaHash   *string `json:"mpesa_hash"`
	Number      string  `db:"number" json:"number"`
	Timestamps
}

type NullPhoneNumber struct {
	CountryCode null.String `db:"country_code" json:"country_code"`
	Number      null.String `db:"number" json:"number"`
}

type RequestedPhoneNumber struct {
	CountryCode null.String `db:"requested_country_code" json:"country_code"`
	Number      null.String `db:"requested_number" json:"number"`
}

func (pn *PhoneNumber) String() string {
	return pn.CountryCode + pn.Number
}

func (pn *PhoneNumber) IsKenyan() bool {
	return pn.CountryCode == "+254"
}
