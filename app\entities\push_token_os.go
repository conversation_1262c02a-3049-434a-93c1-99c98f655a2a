package entities

import (
	"database/sql/driver"
)

type PushTokenOS string

const (
	PushTokenOSAndroid PushTokenOS = "android"
	PushTokenOSWeb     PushTokenOS = "web"
)

// Scan implements the Scanner interface.
func (s *PushTokenOS) Scan(value interface{}) error {
	*s = PushTokenOS(string(value.([]uint8)))
	return nil
}

// Value implements the driver Valuer interface.
func (s PushTokenOS) Value() (driver.Value, error) {
	return s.String(), nil
}

func (s PushTokenOS) String() string {
	return string(s)
}
