package utils

import (
	"log"
	"os"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

var Log *zap.SugaredLogger

func InitLogger(isDev bool) {
	var encoderConfig zapcore.EncoderConfig
	var level zapcore.Level
	var encoder zapcore.Encoder

	if isDev {
		encoderConfig = zap.NewDevelopmentEncoderConfig()
		level = zapcore.DebugLevel
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	} else {
		encoderConfig = zap.NewProductionEncoderConfig()
		level = zapcore.InfoLevel
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	}

	logWriter := zapcore.AddSync(&lumberjack.Logger{
		Filename:   "logs/app.log",
		MaxSize:    10, // MB
		MaxBackups: 3,
		MaxAge:     28, // days
		Compress:   true,
	})

	core := zapcore.NewTee(
		zapcore.NewCore(encoder, zapcore.AddSync(os.Stdout), level),
		zapcore.NewCore(encoder, logWriter, level),
	)

	logger := zap.New(core, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))
	Log = logger.Sugar()
}

func LogMessage(message string) {

	log.Println(message)
	Log.Infof(message)
}
