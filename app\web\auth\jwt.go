package auth

import (
	"encoding/json"
	"os"
	"time"

	"compliance-and-risk-management-backend/app/entities"

	"github.com/dgrijalva/jwt-go"
)

type (
	JWTHandler interface {
		CreateUserToken(*entities.User, *entities.Session) (string, error)
		TokenInfo(tokenValue string) (*entities.TokenInfo, error)
	}

	AppJWTHandler struct {
		signingKey []byte
	}
)

func NewJWTHandler() JWTHandler {
	return NewJWTHandlerWithSigningKey(os.Getenv("JWT_SIGNING_KEY"))
}

func NewJWTHandlerWithSigningKey(signingKey string) JWTHandler {
	return &AppJWTHandler{
		signingKey: []byte(signingKey),
	}
}

func (h *AppJWTHandler) CreateUserToken(
	user *entities.User,
	session *entities.Session,
) (string, error) {

	token := jwt.New(jwt.SigningMethodHS256)

	claims := make(jwt.MapClaims)

	claims["application_type"] = session.ApplicationType.String()
	claims["exp"] = time.Now().AddDate(1, 0, 0).Unix()
	claims["refresh"] = time.Now().Add(time.Hour).Unix()
	claims["session_id"] = session.ID
	claims["status"] = user.Status.String()
	claims["user_id"] = user.ID

	token.Claims = claims

	return token.SignedString(h.signingKey)
}

func (h *AppJWTHandler) TokenInfo(tokenValue string) (*entities.TokenInfo, error) {

	keyFunc := func(*jwt.Token) (interface{}, error) {
		return h.signingKey, nil
	}

	tokenInfo := &entities.TokenInfo{}

	token, err := jwt.Parse(tokenValue, keyFunc)
	if err != nil {
		return tokenInfo, err
	}

	mapClaims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return tokenInfo, nil
	}

	applicationType := entities.ApplicationType(h.getString(mapClaims, "application_type"))
	if applicationType == "" {
		// Check legacy applicationType. Will remove later.
		applicationType = entities.ApplicationType(h.getString(mapClaims, "application_type"))
	}

	clientID := h.getString(mapClaims, "client_id")
	exp := h.getInt64(mapClaims, "exp")
	refresh := h.getInt64(mapClaims, "refresh")
	sessionID := h.getInt64(mapClaims, "session_id")
	status := h.getString(mapClaims, "status")
	storeID := h.getInt64(mapClaims, "store_id")
	userID := h.getInt64(mapClaims, "user_id")

	tokenInfo.ApplicationType = applicationType
	tokenInfo.ClientID = clientID
	tokenInfo.Exp = time.Unix(exp, 0)
	tokenInfo.Refresh = time.Unix(refresh, 0)
	tokenInfo.SessionID = sessionID
	tokenInfo.Status = status
	tokenInfo.UserID = userID
	tokenInfo.StoreID = storeID

	return tokenInfo, nil
}

func (h *AppJWTHandler) getInt64(mapClaims jwt.MapClaims, key string) int64 {
	switch val := mapClaims[key].(type) {
	case float64:
		return int64(val)
	case json.Number:
		v, _ := val.Int64()
		return v
	}
	return 0
}

func (h *AppJWTHandler) getString(mapClaims jwt.MapClaims, key string) string {
	switch val := mapClaims[key].(type) {
	case string:
		return string(val)
	}
	return ""
}
