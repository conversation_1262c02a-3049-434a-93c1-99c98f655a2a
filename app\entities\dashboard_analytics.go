package entities

import "gopkg.in/guregu/null.v3"

type (
	DashboardAnalytic struct {
		SequentialIdentifier
		InvoicesCount         int64     `json:"invoices_count"`
		CreditNotesCount      int64     `json:"credit_notes_count"`
		FailedProcessingCount int64     `json:"failed_processing_count"`
		StoreID               int64     `json:"store_id"`
		Store                 *Store    `json:"store,omitempty"`
		StoresCount           int64     `json:"stores_count"`
		DeletedAt             null.Time `db:"deleted_at" json:"-"`
		Timestamps
	}

	DashboardAnalyticsList struct {
		DashboardAnalytics []*DashboardAnalytic `json:"dashboard_analytics"`
		Pagination         *Pagination          `json:"pagination"`
	}
)
