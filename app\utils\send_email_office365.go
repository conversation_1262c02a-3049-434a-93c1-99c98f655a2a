package utils

import (
	"bytes"
	"crypto/tls"
	"errors"
	"fmt"
	"io"
	"compliance-and-risk-management-backend/app/entities"
	"net"
	"net/smtp"
	"os"
	"strconv"
	"strings"
	"text/template"

	"gopkg.in/gomail.v2"
)

type loginAuth struct {
	username, password string
}

func <PERSON><PERSON>A<PERSON>(username, password string) smtp.Auth {
	return &loginAuth{username, password}
}

func (a *loginAuth) Start(server *smtp.ServerInfo) (string, []byte, error) {
	return "LOGIN", []byte(a.username), nil
}

func (a *loginAuth) Next(fromServer []byte, more bool) ([]byte, error) {
	if more {
		switch string(fromServer) {
		case "Username:":
			return []byte(a.username), nil
		case "Password:":
			return []byte(a.password), nil
		default:
			return nil, errors.New("Unknown from server")
		}
	}
	return nil, nil
}

func Office365SendEmail(invoice *entities.Invoice) bool {
	// Sender data.
	from := os.Getenv("SMTP_EMAIL")
	// password := os.Getenv("SMTP_PASSWORD")
	smtpHost := os.Getenv("SMTP_SERVER")
	smtpPortStr := os.Getenv("SMTP_PORT")

	smtpPort, err := strconv.Atoi(smtpPortStr)
	if err != nil {
		fmt.Printf("error converting smtp port=[%v] to int", smtpPortStr)
		return false
	}

	recipients := []string{invoice.CustomerEmail}

	m := gomail.NewMessage()
	m.SetHeader("From", from)

	if os.Getenv("ENVIRONMENT") != "production" {
		recipients = nil
		recipients = append(recipients, "<EMAIL>")
	}

	m.SetHeader("To", recipients...)

	ccEmails := os.Getenv("CC_EMAILS")
	ccEmailsArr := strings.Split(ccEmails, ",")
	m.SetHeader("Cc", ccEmailsArr...)
	m.SetHeader("Subject", fmt.Sprintf("Ultimate Security Invoice %v", invoice.InvoiceNumber))
	m.Attach(invoice.FileName)

	customerName := invoice.CustomerName
	if len(strings.TrimSpace(customerName)) == 0 {
		customerName = "Customer"
	}

	customerName = "Customer"

	t, _ := template.ParseFiles("templates/email_template.html")
	m.AddAlternativeWriter("text/html", func(w io.Writer) error {
		return t.Execute(w, struct {
			CustomerName  string
			InvoiceNumber string
		}{
			CustomerName:  customerName,
			InvoiceNumber: invoice.InvoiceNumber,
		})
	})

	// d := gomail.NewDialer(smtpHost, smtpPort, from, password)
	d := &gomail.Dialer{Host: smtpHost, Port: smtpPort}
	d.TLSConfig = &tls.Config{InsecureSkipVerify: true}

	// Send the email to Bob, Cora and Dan.
	if err := d.DialAndSend(m); err != nil {
		fmt.Printf("err sending email, err=[%v]\n", err.Error())
		return false
	} else {
		fmt.Printf("Email sent. \n")
		return true
	}
}

func Office365SendEmail_001() {

	// Sender data.
	from := os.Getenv("SMTP_EMAIL")
	password := os.Getenv("SMTP_PASSWORD")

	// Receiver email address.
	to := []string{
		"<EMAIL>",
	}

	// smtp server configuration.
	smtpHost := "smtp.office365.com"
	smtpPort := "587"

	conn, err := net.Dial("tcp", "smtp.office365.com:587")
	if err != nil {
		println(err)
	}

	c, err := smtp.NewClient(conn, smtpHost)
	if err != nil {
		println(err)
	}

	tlsconfig := &tls.Config{
		ServerName: smtpHost,
	}

	if err = c.StartTLS(tlsconfig); err != nil {
		println(err)
	}

	auth := LoginAuth(from, password)

	if err = c.Auth(auth); err != nil {
		println(err)
	}

	t, _ := template.ParseFiles("templates/email_template.html")

	var body bytes.Buffer

	mimeHeaders := "MIME-version: 1.0;\nContent-Type: text/html; charset=\"UTF-8\";\n\n"
	body.Write([]byte(fmt.Sprintf("Subject: This is a test subject \n%s\n\n", mimeHeaders)))

	t.Execute(&body, struct {
		Name    string
		Message string
	}{
		Name:    "Hasan Yousef",
		Message: "This is a test message in a HTML template",
	})

	// Sending email.
	err = smtp.SendMail(smtpHost+":"+smtpPort, auth, from, to, body.Bytes())
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("Email Sent!")
}
