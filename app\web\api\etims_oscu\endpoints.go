package etims_oscu

import (
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/web/auth"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	apiKeyRepository repos.APIKeyRepository,
	apiKeyService services.APIKeyService,
	etimsVSCUService services.EtimsVSCUService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyApiKeyUser(transactionsDB, apiKeyRepository))
	{
		protectedAPI.POST("/etims_oscu/invoices", signInvoice(transactionsDB, apiKeyService, etimsVSCUService))
		protectedAPI.GET("/etims_oscu/invoices", filterInvoices(transactionsDB, apiKeyService, etimsVSCUService))
		protectedAPI.GET("/etims_oscu/invoices/:id", getInvoice(transactionsDB, apiKeyService, etimsVSCUService))
	}
}
