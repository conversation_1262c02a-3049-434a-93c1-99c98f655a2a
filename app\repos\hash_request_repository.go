package repos

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
)

const (
	countHashRequestsSQL = `SELECT COUNT(*) AS count FROM hash_requests WHERE 1=1`

	createHashRequestSQL = `INSERT INTO hash_requests (user_id, phone_number, hash, status, description, transaction_id, created_at, 
		updated_at) VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING id`

	selectHashRequestByIDSQL = selectHashRequestSQL + ` WHERE id=$1`

	selectHashRequestSQL = `SELECT id, user_id, credit_balance, deleted_at, created_at, updated_at FROM hash_requests`

	updateHashRequestSQL = `UPDATE hash_requests SET credit_balance=$1, updated_at=$2 WHERE id=$3`
)

type (
	HashRequestRepository interface {
		CountHashRequests(context.Context, *entities.PaginationFilter) int
		FindByID(context.Context, int64) (*entities.HashRequest, error)
		Save(context.Context, txns_db.TransactionsSQLOperations, *entities.HashRequest) error
	}

	AppHashRequestRepository struct {
		db *sql.DB
	}
)

func NewHashRequestRepository(db *sql.DB) HashRequestRepository {
	return &AppHashRequestRepository{db: db}
}

func (r *AppHashRequestRepository) CountHashRequests(
	ctx context.Context,
	filter *entities.PaginationFilter,
) int {
	var count int
	query, args, _ := r.buildQueryFromFilter(countHashRequestsSQL, filter, []interface{}{}, 1)
	row := r.db.QueryRowContext(ctx, query, args...)
	row.Scan(&count)
	return count
}

func (r *AppHashRequestRepository) FindByID(
	ctx context.Context,
	id int64,
) (*entities.HashRequest, error) {
	row := r.db.QueryRow(selectHashRequestByIDSQL, id)
	return r.scanRowIntoHashRequest(row)
}

func (r *AppHashRequestRepository) Save(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	hashRequest *entities.HashRequest,
) error {

	hashRequest.Timestamps.Touch()
	var err error

	if hashRequest.IsNew() {

		res := operations.QueryRowContext(
			ctx,
			createHashRequestSQL,
			hashRequest.UserID,
			hashRequest.PhoneNumber,
			hashRequest.Hash,
			hashRequest.Status,
			hashRequest.Description,
			hashRequest.TransactionID,
			hashRequest.CreatedAt,
			hashRequest.UpdatedAt,
		)

		err = res.Scan(&hashRequest.ID)

	} else {

		_, err = operations.ExecContext(
			ctx,
			updateHashRequestSQL,
			hashRequest.UpdatedAt,
			hashRequest.ID,
		)
	}

	if err != nil {
		log.Printf("error saving user Credit Balance, err=[%v]\n", err.Error())
		return err
	}

	return nil
}

func (r *AppHashRequestRepository) buildQueryFromFilter(
	query string,
	filter *entities.PaginationFilter,
	args []interface{},
	currentIndex int,
) (string, []interface{}, int) {

	if filter.StartDate.Valid && filter.EndDate.Valid {
		query += fmt.Sprintf(" AND created_at >= $%d AND created_at < $%d", currentIndex, currentIndex+1)
		args = append(args, filter.StartDate.Time.Format("2006-01-02"))
		args = append(args, filter.EndDate.Time.Format("2006-01-02"))
		currentIndex += 2
	}

	if filter.UserID > 0 {
		query += fmt.Sprintf(" AND user_id = $%d", currentIndex)
		args = append(args, filter.UserID)
		currentIndex += 1
	}

	return query, args, currentIndex
}

func (r *AppHashRequestRepository) scanRowIntoHashRequest(
	rowScanner txns_db.RowScanner,
) (*entities.HashRequest, error) {

	var hashRequest entities.HashRequest

	err := rowScanner.Scan(
		&hashRequest.ID,
		&hashRequest.UserID,
		&hashRequest.Description,
		&hashRequest.Status,
		&hashRequest.CreatedAt,
		&hashRequest.UpdatedAt,
	)

	if err != nil {
		log.Printf("error scanning hashRequest,  err=[%v]\n", err.Error())
		return &hashRequest, err
	}

	return &hashRequest, nil
}
