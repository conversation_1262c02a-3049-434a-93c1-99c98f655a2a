package entities

import "database/sql/driver"

type ETIMSPaymentMethod string

const (
	ETIMSPaymentMethodCash        ETIMSPaymentMethod = "01"
	ETIMSPaymentMethodCredit      ETIMSPaymentMethod = "02"
	ETIMSPaymentMethodCashCredit  ETIMSPaymentMethod = "03"
	ETIMSPaymentMethodBankCheck   ETIMSPaymentMethod = "04"
	ETIMSPaymentMethodCard        ETIMSPaymentMethod = "05"
	ETIMSPaymentMethodMobileMoney ETIMSPaymentMethod = "06"
	ETIMSPaymentMethodOther       ETIMSPaymentMethod = "07"
)

// Scan implements the Scanner interface.
func (s *ETIMSPaymentMethod) Scan(value interface{}) error {
	*s = ETIMSPaymentMethod(string(value.([]uint8)))
	return nil
}

// Value implements the driver Valuer interface.
func (s ETIMSPaymentMethod) Value() (driver.Value, error) {
	return s.String(), nil
}

func (s ETIMSPaymentMethod) String() string {
	return string(s)
}
