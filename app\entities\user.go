package entities

import "gopkg.in/guregu/null.v3"

type (
	User struct {
		SequentialIdentifier
		AccountNumber  string        `json:"account_number"`
		DefaultStore   *Store        `json:"default_store"`
		Email          string        `json:"email"`
		FirstName      string        `json:"first_name"`
		LastName       string        `json:"last_name"`
		OrganizationID int64         `json:"organization_id"`
		Organization   *Organization `json:"organization"`
		PhoneNumberID  int64         `json:"phone_number_id"`
		PhoneNumber    *PhoneNumber  `json:"phone_number"`
		Username       string        `json:"username"`
		IsSynced       bool          `json:"is_synced"`
		NationalID     string        `json:"national_id"`
		Password       string        `json:"-"`
		Status         UserStatus    `json:"status"`
		Stores         []*Store      `json:"stores"`
		Token          string        `json:"token"`
		DeletedAt      null.Time     `db:"deleted_at" json:"-"`
		Timestamps
	}

	UserList struct {
		Users      []*User    `json:"users"`
		Pagination Pagination `json:"pagination"`
	}
)
