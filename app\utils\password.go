package utils

import "golang.org/x/crypto/bcrypt"

func GeneratePasswordHash(password []byte) (string, error) {
	hashedPassword, err := bcrypt.GenerateFromPassword(password, 14)
	if err != nil {
		return "", err
	}
	return string(hashedPassword), nil
}

func ComparePassword(passwordHash string, password string) error {
	passwordHashBytes := []byte(passwordHash)
	passwordBytes := []byte(password)
	return bcrypt.CompareHashAndPassword(passwordHashBytes, passwordBytes)
}
