package services

import (
	"context"
	"fmt"
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/logger"
	"compliance-and-risk-management-backend/app/providers"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/utils"
	"strings"
	"time"
)

type StarlinkVSCU interface {
	FetchNoticesFromKRA(ctx context.Context, operations txns_db.TransactionsSQLOperations) error
	GetInvoice(ctx context.Context, operations txns_db.TransactionsSQLOperations) (*entities.Invoice, error)
	SignInvoice(ctx context.Context, transactionsDB txns_db.TransactionsDB, form *forms.ETIMSInvoiceForm) (*entities.InvoiceSigningResult, error)
}

type AppStarlinkVSCU struct {
	etimsProductionAPIURL string
	etimsSandboxAPIURL    string
	etimsVSCU             providers.EtimsVSCU
	etimsVSCUService      EtimsItemService
	etimsItemRepository   repos.EtimsItemRepository
	etimsNoticeRepository repos.EtimsNoticeRepository
	invoiceItemRepository repos.InvoiceItemRepository
	invoiceRepository     repos.InvoiceRepository
	qrCodeFolder          string
	storeRepository       repos.StoreRepository
}

func NewStarlinkVSCU(
	etimsProductionAPIURL string,
	etimsSandboxAPIURL string,
	etimsVSCU providers.EtimsVSCU,
	etimsVSCUService EtimsItemService,
	etimsItemRepository repos.EtimsItemRepository,
	etimsNoticeRepository repos.EtimsNoticeRepository,
	invoiceItemRepository repos.InvoiceItemRepository,
	invoiceRepository repos.InvoiceRepository,
	qrCodeFolder string,
	storeRepository repos.StoreRepository,
) StarlinkVSCU {
	return &AppStarlinkVSCU{
		etimsProductionAPIURL: etimsProductionAPIURL,
		etimsSandboxAPIURL:    etimsSandboxAPIURL,
		etimsVSCU:             etimsVSCU,
		etimsVSCUService:      etimsVSCUService,
		etimsItemRepository:   etimsItemRepository,
		etimsNoticeRepository: etimsNoticeRepository,
		invoiceItemRepository: invoiceItemRepository,
		invoiceRepository:     invoiceRepository,
		qrCodeFolder:          qrCodeFolder,
		storeRepository:       storeRepository,
	}
}

func (s *AppStarlinkVSCU) FetchNoticesFromKRA(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
) error {

	return nil
}

func (s *AppStarlinkVSCU) GetInvoice(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
) (*entities.Invoice, error) {

	return nil, nil
}

func (s *AppStarlinkVSCU) SignInvoice(
	ctx context.Context,
	operations txns_db.TransactionsDB,
	form *forms.ETIMSInvoiceForm,
) (*entities.InvoiceSigningResult, error) {

	invoice, err := utils.SimulateInvoice()
	if err != nil {
		fmt.Printf("error simulating invoice, err=[%v]\n", err)
	}

	store := &entities.Store{
		EtimsBranch:       "00",
		EtimsDeviceSerial: "SN5433Q09",
		EtimsEnvironment:  "sandbox",
		Name:              "Functional Oil",
		PIN:               "P051922564N",
	}

	// TODO: This data can be sent periodicallyto KRA
	err = s.signInvoiceViaKRAETIMS(ctx, operations, store, invoice)
	if err != nil {
		fmt.Printf("error signing invoice via KRA ETIMS, err=[%v]\n", err)
	}

	return nil, nil
}

func (s *AppStarlinkVSCU) signInvoiceViaKRAETIMS(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	store *entities.Store,
	invoice *entities.Invoice,
) error {

	fmt.Printf("Signing invoice=[%v] via etims...\n", invoice.InvoiceNumber)

	timeNow := time.Now()
	commonDate := utils.FormatTime(timeNow, "20060102030405") // yyyyMMddhhmmss

	// TODO: Remove this after testing
	// commonDate = "20240814013856"

	itemsCount := len(invoice.InvoiceItems)

	customerName := invoice.CustomerName
	if len(customerName) >= 20 {
		customerName = customerName[:19]
	}

	storeName := store.Name
	if len(storeName) >= 20 {
		storeName = storeName[:19]
	}

	trimmedTraderName := strings.ReplaceAll(store.Name, " ", "")
	if len(trimmedTraderName) >= 20 {
		trimmedTraderName = trimmedTraderName[:19]
	}

	currReceiptNumber := invoice.CurrentReceiptNumber

	etimsReceiptForm := &forms.EtimsReceiptForm{
		Adrs:         "Nairobi,Kenya",
		BtmMsg:       store.Name,
		CurRcptNo:    currReceiptNumber,
		CustMblNo:    invoice.CustomerPhone,
		CustTin:      invoice.CustomerTIN,
		PrchrAcptcYn: "Y",
		RcptPbctDt:   commonDate,
		RptNo:        currReceiptNumber,
		TopMsg:       storeName,
		TotRcptNo:    currReceiptNumber,
		TrdeNm:       customerName,
	}

	var etimsItems []*forms.EtimsItemForm
	itemSeqCounter := 1

	for _, etimsItem := range invoice.EtimsItems {

		vat := etimsItem.DftPrc * 0.16
		vatStr := fmt.Sprintf("%.2f", vat)
		vatFloat64 := utils.ConvertStringToFloat64(vatStr)

		taxableAmount := etimsItem.DftPrc * etimsItem.SftyQty
		taxableAmountStr := fmt.Sprintf("%.2f", taxableAmount)
		taxableAmountFloat64 := utils.ConvertStringToFloat64(taxableAmountStr)

		etimsItem := &forms.EtimsItemForm{
			DcAmt:     etimsItem.DcAmt,
			DcRt:      etimsItem.DcRt,
			ItemSeq:   itemSeqCounter,
			ItemCd:    etimsItem.ItemCd,
			ItemClsCd: etimsItem.ItemClsCd,
			ItemNm:    etimsItem.ItemNm,
			Bcd:       etimsItem.Bcd,
			PkgUnitCd: etimsItem.PkgUnitCd,
			Pkg:       etimsItem.SftyQty,
			QtyUnitCd: etimsItem.QtyUnitCd,
			Qty:       etimsItem.SftyQty,
			Prc:       etimsItem.DftPrc,
			SplyAmt:   utils.RoundFloat64(taxableAmount, 2),
			IsrccCd:   "N",
			TaxTyCd:   etimsItem.TaxTyCd,
			TaxblAmt:  etimsItem.DftPrc,
			TaxAmt:    vatFloat64,
			TotAmt:    taxableAmountFloat64,
		}

		etimsItems = append(etimsItems, etimsItem)
		itemSeqCounter++
	}

	invoiceNumber := utils.ConvertStringToInt64(invoice.InvoiceNumber)

	totalTaxableAmount := invoice.GrandTotal - invoice.Vat
	totalTaxableAmountStr := fmt.Sprintf("%.2f", totalTaxableAmount)
	totalTaxableAmountFloat64 := utils.ConvertStringToFloat64(totalTaxableAmountStr)
	totalTaxableAmountFloat64 = utils.RoundFloat64(totalTaxableAmountFloat64, 2)
	totalVat := utils.RoundFloat64(invoice.Vat, 2)
	taxAmountB := utils.RoundFloat64(invoice.GrandTotal, 2)

	// Construct etims sale form
	etimsForm := &forms.CreateEtimsSalesForm{
		Tin:          store.PIN,
		BhfID:        store.EtimsBranch,
		TrdInvcNo:    invoiceNumber,
		InvcNo:       invoiceNumber,
		OrgInvcNo:    invoiceNumber,
		CustTin:      invoice.CustomerTIN,
		CustNm:       invoice.CustomerName,
		SalesTyCd:    "N",
		RcptTyCd:     "S", // S for Sale, R for Credit Note
		PmtTyCd:      entities.ETIMSPaymentMethodCash.String(),
		SalesSttsCd:  entities.TransactionProgressApproved.String(),
		CfmDt:        commonDate,
		SalesDt:      utils.FormatTime(timeNow, "20060102"), // yyyyMMdd
		StockRlsDt:   commonDate,
		TotItemCnt:   itemsCount,
		TaxblAmtA:    0.00,
		TaxblAmtB:    taxAmountB,
		TaxblAmtC:    0.00,
		TaxblAmtD:    0.00,
		TaxblAmtE:    0.00,
		TaxRtA:       0.00,
		TaxRtB:       16.00,
		TaxRtC:       0.00,
		TaxRtD:       0.00,
		TaxRtE:       8.00,
		TaxAmtA:      invoice.VatA,
		TaxAmtB:      totalVat,
		TaxAmtC:      invoice.VatC,
		TaxAmtD:      0.00,
		TaxAmtE:      invoice.VatE,
		TotTaxAmt:    totalVat,
		TotTaxblAmt:  totalTaxableAmountFloat64,
		TotAmt:       invoice.GrandTotal,
		TrdeNm:       trimmedTraderName,
		PrchrAcptcYn: "N",
		Remark:       fmt.Sprintf("Sale invoice number %v", invoice.InvoiceNumber),
		RegrID:       "11999",
		RegrNm:       trimmedTraderName,
		ModrID:       "45678",
		ModrNm:       trimmedTraderName,
		Receipt:      etimsReceiptForm,
		ItemList:     etimsItems,
	}

	if invoice.IsCreditNote {
		etimsForm.RcptTyCd = "R" // S for Sale, R for Credit Note
	}

	// internalData, err := utils.GenerateInternalData(invoice, etimsForm)
	// if err != nil {
	// 	fmt.Printf("error generating internal data, err=[%v]\n", err)
	// }
	// fmt.Printf("internalData=[%v]\n", internalData)

	// For debugging purposes only
	// payloadBytes, err := json.Marshal(etimsForm)
	// if err != nil {
	// 	logger.Errorf("failed to marshal VFMS sales form payload, err=[%v]\n", err)
	// 	return err
	// }
	// utils.PrettyPrintJSON(payloadBytes)

	// etimsVscu := s.etimsVscu

	// if store.EtimsEnvironment == "production" {
	// productionETIMSVSCUurl := os.Getenv("VSCU_API_URL_PRODUCTION") // "http://**************:8098"
	// s.etimsVscu = providers.NewEtimsVSCUWithParams(
	// 	productionETIMSVSCUurl,
	// 	store.EtimsBranch,
	// 	store.EtimsDeviceSerial,
	// 	store.PIN,
	// )
	// }

	// Sign invoice and generate internal and signature data, then send all the data to KRA
	err := signKRAInvoice(store, invoice, etimsForm)
	if err != nil {
		fmt.Printf("unable to sign invoice, err=[%v]\n", err)
		return err
	}

	return nil

	// Send to etims vscu provider
	// TODO: Seperate this process and send in a background task
	// response, err := s.etimsVscuCore.SaveSale(etimsForm)
	// if err != nil {
	// 	logger.Errorf("failed to post sale to etims, err=[%v]\n", err)
	// 	return err
	// }

	// fmt.Printf("etims save sale response code=[%v]\n", response.ResultCd)
	// utils.PrintStructToConsole(response)

	// if response.Status == 400 {
	// 	logger.Errorf("failed to post sale to etims, err=[%v]\n", err)
	// 	return err
	// }

	// if response.ResultCd == "000" {
	// 	// Update signature and verification url only on successful KRA response
	// 	invoice.EtimsInternalData = &response.Data.IntrlData
	// 	invoice.EtimsReceiptSignature = &response.Data.RcptSign
	// 	invoice.EtimsResultDateTime = &response.Data.VsdcRcptPbctDate
	// 	invoice.EtimsTotalReceiptNumber = &response.Data.RcptNo
	// 	invoice.EtimsVSCUReceiptPublicationDate = &response.Data.VsdcRcptPbctDate

	// 	// signature := fmt.Sprintf("%v/%v %v %v", response.Data.SdcID, response.Data.RcptNo, response.Data.RcptSign, response.Data.VsdcRcptPbctDate)
	// 	invoice.Signature = response.Data.RcptSign
	// 	invoice.SignedDate = utils.FormatTime(time.Now(), "20060102150405")

	// } else if response.ResultCd == "924" && invoice.Signature == "" && invoice.VerificationURL == "" {
	// 	// Update signature and verification url only on successful KRA response
	// 	// invoice.EtimsInternalData = &response.Data.IntrlData
	// 	// invoice.EtimsMrcNumber = &response.Data.MrcNo
	// 	// invoice.EtimsReceiptSignature = &response.Data.RcptSign
	// 	// invoice.EtimsResultDateTime = &response.Data.VsdcRcptPbctDate
	// 	// invoice.EtimsTotalReceiptNumber = &response.Data.RcptNo
	// 	// invoice.EtimsVSCUReceiptPublicationDate = &response.Data.VsdcRcptPbctDate

	// } else {
	// 	return err
	// }

	// if response.Data != nil {
	// 	invoice.EtimsInternalData = &response.Data.IntrlData
	// 	invoice.EtimsMrcNumber = &response.Data.MrcNo
	// 	invoice.EtimsReceiptNumber = &response.Data.RcptNo
	// 	invoice.EtimsResultDateTime = &response.Data.VsdcRcptPbctDate
	// 	invoice.EtimsResultCode = &response.ResultCd
	// 	invoice.EtimsResultMessage = &response.ResultMsg
	// 	invoice.EtimsSDCID = &response.Data.SdcID
	// }

	// fmt.Printf("invoice.Signature=[%v]\n", invoice.Signature)

	if invoice.Signature != "" {
		// https://etims-sbx.kra.go.ke/common/link/etims/receipt/indexEtimsReceiptData?Data=P000000010E00EIW354USAF6HTG5Q
		// Data=P000000010E (PIN) 00 (Branch ID) EIW354USAF6HTG5Q (signature from vscu)

		// To check if client is live - if live use prod, otherwise use sandbox
		apiBaseURL := s.etimsSandboxAPIURL

		if store.EtimsEnvironment == "production" {
			apiBaseURL = s.etimsProductionAPIURL
		}

		signatureData := fmt.Sprintf("%v%v%v", store.PIN, store.EtimsBranch, invoice.Signature)
		verificationURL := fmt.Sprintf("%v/common/link/etims/receipt/indexEtimsReceiptData?Data=%v", apiBaseURL, signatureData)
		invoice.VerificationURL = verificationURL

		// Generate Signature and QR Code
		if invoice.EtimsResultDateTime != nil {
			err = utils.GenerateETIMSVSCUQRCode(ctx, invoice)
			if err != nil {
				logger.Errorf("failed to generate QR Code, err=[%v]\n", err)
				return err
			}
		}

		// Save response recieved by updating the current invoice
		err = s.invoiceRepository.Save(ctx, operations, invoice)
		if err != nil {
			logger.Errorf("failed to update signed invoice, err=[%v]\n", err)
			return err
		}
	}

	return nil
}

func signKRAInvoice(store *entities.Store, invoice *entities.Invoice, form *forms.CreateEtimsSalesForm) error {

	tinBhfPath := fmt.Sprintf("%v_%v", store.PIN, store.EtimsBranch)
	fmt.Printf("tinBhfPath=[%v]\n", tinBhfPath)

	internalData, err := utils.GenerateInternalData(invoice, form, tinBhfPath)
	if err != nil {
		fmt.Printf("error generating internal data, err=[%v]\n", err)
	}

	fmt.Printf("internalData=[%v]\n", internalData)

	invoice.EtimsInternalData = &internalData

	// Generate invoice signature
	invoiceSignature, err := utils.GenerateEtimsSignature(invoice, form, tinBhfPath)
	if err != nil {
		fmt.Printf("error generating invoice signature, err=[%v]\n", err)
	}

	fmt.Printf("invoiceSignature=[%v]\n", invoiceSignature)
	invoice.EtimsReceiptSignature = &invoiceSignature

	return nil
}
