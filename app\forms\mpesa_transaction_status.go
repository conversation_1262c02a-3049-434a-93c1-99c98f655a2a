package forms

type MpesaTransactionStatusForm struct {
	Initiator          string `binding:"required" json:"initiator"`
	SecurityCredential string `binding:"required" json:"security_credential"`
	CommandID          string `binding:"required" json:"command_id"`
	TransactionID      string `binding:"required" json:"transaction_id"`
	PartyA             string `binding:"required" json:"party_a"`
	IdentifierType     string `binding:"required" json:"identifier_type"`
	ResultURL          string `binding:"required" json:"result_url"`
	QueueTimeOutURL    string `binding:"required" json:"queue_time_out_url"`
	Remarks            string `binding:"required" json:"remarks"`
	Occasion           string `binding:"required" json:"occasion"`
}
