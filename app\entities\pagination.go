package entities

import "gopkg.in/guregu/null.v3"

type Pagination struct {
	Count int `json:"count"`
	Page  int `json:"page"`
	Per   int `json:"per"`
}

type PaginationFilter struct {
	After            string
	Before           string
	DocumentType     string
	EndDate          null.Time
	IsEmailSent      null.Bool
	IsZReported      null.Bool
	Limit            int
	Month            int
	Offset           int
	OrderBy          string
	OrganizationID   int64
	Page             int
	Per              int
	Search           string
	SearchCriteria   string
	Show             string
	Sort             string
	StartDate        null.Time
	Status           string
	StoreID          int64
	TwitterNextToken string
	TwitterSinceID   string
	UserID           int64
	Year             int
}

func NewPagination(count, page, per int) *Pagination {
	return &Pagination{
		Count: count,
		Page:  page,
		Per:   per,
	}
}
