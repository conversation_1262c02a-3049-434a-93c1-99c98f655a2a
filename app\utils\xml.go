package utils

import (
	"encoding/xml"
)

// XMLMarshalWithHeader marshals dat into XML with the xml header prepended
func XMLMarshalWithHeader(data interface{}) ([]byte, error) {
	xmlstring, err := xml.Marshal(data)
	if err != nil {
		return nil, err
	}

	xmlstring = []byte(xml.Header + string(xmlstring))
	return xmlstring, nil
}

// XMLMarshalWithHeaderDebug for debugging, pretty prints the marshalled XML
func XMLMarshalWithHeaderDebug(data interface{}) ([]byte, error) {
	xmlstring, err := xml.MarshalIndent(data, "", "    ")
	if err != nil {
		return nil, err
	}

	xmlstring = []byte(xml.Header + string(xmlstring))
	return xmlstring, nil
}
