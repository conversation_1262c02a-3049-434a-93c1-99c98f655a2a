package analytics

import (
	"context"
	"net/http"
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/globals"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
)

func getAnalytics(
	transactionsDB *database.AppTransactionsDB,
	analyticsService services.AnalyticsService,
) func(ctx *gin.Context) {

	return func(ctx *gin.Context) {

		session := sessions.Default(ctx)
		user := session.Get(globals.Userkey)

		dashboardAnalytics, err := analyticsService.GetDashboardAnalytics(context.Background(), transactionsDB)
		if err != nil {
			ctx.JSON(500, gin.H{
				"message": "unable to retrieve dashboard analytics",
			})
			return
		}

		ctx.HTML(http.StatusOK, "analytics.html", gin.H{
			"content":            "Analytics",
			"user":               user,
			"dashboardAnalytics": dashboardAnalytics,
		})

	}
}
