package entities

type (
	EtimsBrancesResponse struct {
		ResultCd  string `json:"resultCd"`
		ResultMsg string `json:"resultMsg"`
		ResultDt  string `json:"resultDt"`
		Data      struct {
			BhfList []struct {
				Tin       string `json:"tin"`
				BhfID     string `json:"bhfId"`
				BhfNm     string `json:"bhfNm"`
				BhfSttsCd string `json:"bhfSttsCd"`
				PrvncNm   string `json:"prvncNm"`
				DstrtNm   string `json:"dstrtNm"`
				SctrNm    string `json:"sctrNm"`
				LocDesc   string `json:"locDesc"`
				MgrNm     string `json:"mgrNm"`
				MgrTelNo  string `json:"mgrTelNo"`
				MgrEmail  string `json:"mgrEmail"`
				HqYn      string `json:"hqYn"`
			} `json:"bhfList"`
		} `json:"data"`
	}

	EtimsCodesResponse struct {
		ResultCd  string `json:"resultCd"`
		ResultMsg string `json:"resultMsg"`
		ResultDt  string `json:"resultDt"`
		Data      struct {
			ClsList []struct {
				CdCls      string `json:"cdCls"`
				CdClsNm    string `json:"cdClsNm"`
				CdClsDesc  string `json:"cdClsDesc"`
				UseYn      string `json:"useYn"`
				UserDfnNm1 string `json:"userDfnNm1"`
				UserDfnNm2 string `json:"userDfnNm2"`
				UserDfnNm3 string `json:"userDfnNm3"`
				DtlList    []struct {
					Cd         string `json:"cd"`
					CdNm       string `json:"cdNm"`
					CdDesc     string `json:"cdDesc"`
					UseYn      string `json:"useYn"`
					SrtOrd     int    `json:"srtOrd"`
					UserDfnCd1 string `json:"userDfnCd1"`
					UserDfnCd2 string `json:"userDfnCd2"`
					UserDfnCd3 string `json:"userDfnCd3"`
				} `json:"dtlList"`
			} `json:"clsList"`
		} `json:"data"`
	}

	EtimsInfoResponse struct {
		ResultCd  string      `json:"resultCd"`
		ResultMsg string      `json:"resultMsg"`
		ResultDt  string      `json:"resultDt"`
		Data      interface{} `json:"data"`
	}

	EtimsItemCreationResponse struct {
		ResultCd  string      `json:"resultCd"`
		ResultMsg string      `json:"resultMsg"`
		ResultDt  string      `json:"resultDt"`
		Data      interface{} `json:"data"`
	}

	EtimsItemClassResponse struct {
		ResultCd  string `json:"resultCd"`
		ResultMsg string `json:"resultMsg"`
		ResultDt  string `json:"resultDt"`
		Data      struct {
			ItemClsList []struct {
				ItemClsCd  string `json:"itemClsCd"`
				ItemClsNm  string `json:"itemClsNm"`
				ItemClsLvl int    `json:"itemClsLvl"`
				TaxTyCd    string `json:"taxTyCd"`
				MjrTgYn    string `json:"mjrTgYn"`
				UseYn      string `json:"useYn"`
			} `json:"itemClsList"`
		} `json:"data"`
	}

	EtimsItemsResponse struct {
		ResultCd  string `json:"resultCd"`
		ResultMsg string `json:"resultMsg"`
		ResultDt  string `json:"resultDt"`
		Data      struct {
			ItemClsList []struct {
				ItemClsCd  string `json:"itemClsCd"`
				ItemClsNm  string `json:"itemClsNm"`
				ItemClsLvl int    `json:"itemClsLvl"`
				TaxTyCd    string `json:"taxTyCd"`
				MjrTgYn    string `json:"mjrTgYn"`
				UseYn      string `json:"useYn"`
			} `json:"itemClsList"`
		} `json:"data"`
	}

	EtimsNoticesResponse struct {
		ResultCd  string `json:"resultCd"`
		ResultMsg string `json:"resultMsg"`
		ResultDt  string `json:"resultDt"`
		Data      struct {
			NoticeList []struct {
				NoticeNo int    `json:"noticeNo"`
				Title    string `json:"title"`
				Cont     string `json:"cont"`
				DtlURL   string `json:"dtlUrl"`
				RegrNm   string `json:"regrNm"`
				RegDt    string `json:"regDt"`
			} `json:"noticeList"`
		} `json:"data"`
	}

	EtimsSaveSaleResponseData struct {
		RcptNo           int64  `json:"rcptNo"`
		IntrlData        string `json:"intrlData"`
		RcptSign         string `json:"rcptSign"`
		TotRcptNo        int    `json:"totRcptNo"`
		VsdcRcptPbctDate string `json:"vsdcRcptPbctDate"`
		SdcID            string `json:"sdcId"`
		MrcNo            string `json:"mrcNo"`
	}

	EtimsSaveSaleResponse struct {
		Data      *EtimsSaveSaleResponseData `json:"data"`
		ResultCd  string                     `json:"resultCd"`
		ResultDt  string                     `json:"resultDt"`
		ResultMsg string                     `json:"resultMsg"`
		Status    int64                      `json:"status"`
	}

	EtimsSelectItemsResponse struct {
		ResultCd  string `json:"resultCd"`
		ResultMsg string `json:"resultMsg"`
		ResultDt  string `json:"resultDt"`
		Data      struct {
			ItemList []struct {
				Tin         string `json:"tin"`
				ItemCd      string `json:"itemCd"`
				ItemClsCd   string `json:"itemClsCd"`
				ItemTyCd    string `json:"itemTyCd"`
				ItemNm      string `json:"itemNm"`
				ItemStdNm   string `json:"itemStdNm"`
				OrgnNatCd   string `json:"orgnNatCd"`
				PkgUnitCd   string `json:"pkgUnitCd"`
				QtyUnitCd   string `json:"qtyUnitCd"`
				TaxTyCd     string `json:"taxTyCd"`
				BtchNo      string `json:"btchNo"`
				RegBhfID    string `json:"regBhfId"`
				Bcd         string `json:"bcd"`
				DftPrc      int    `json:"dftPrc"`
				GrpPrcL1    int    `json:"grpPrcL1"`
				GrpPrcL2    int    `json:"grpPrcL2"`
				GrpPrcL3    int    `json:"grpPrcL3"`
				GrpPrcL4    int    `json:"grpPrcL4"`
				GrpPrcL5    int    `json:"grpPrcL5"`
				AddInfo     string `json:"addInfo"`
				SftyQty     int    `json:"sftyQty"`
				IsrcAplcbYn string `json:"isrcAplcbYn"`
				RraModYn    string `json:"rraModYn"`
				UseYn       string `json:"useYn"`
			} `json:"itemList"`
		} `json:"data"`
	}
)
