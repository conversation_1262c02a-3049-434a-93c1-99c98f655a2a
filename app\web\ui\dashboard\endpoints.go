package dashboard

import (
	"compliance-and-risk-management-backend/app/database"

	"compliance-and-risk-management-backend/app/services"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	analyticsService services.AnalyticsService,
) {

	unauthenticatedAPI := routerGroup.Group("")
	{
		unauthenticatedAPI.GET("/dashboard", getDashboardAnalytics(transactionsDB, analyticsService))
	}
}
