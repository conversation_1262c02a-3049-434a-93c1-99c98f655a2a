CREATE TABLE IF NOT EXISTS z_report_invoices (
    id                  BIGSERIAL                   PRIMARY KEY,    
    z_report_id         BIGINT                      NOT NULL REFERENCES z_reports(id),
    invoice_id          BIGINT                      NOT NULL REFERENCES invoices(id),
    is_synced           B<PERSON>OLEAN                     NOT NULL DEFAULT FALSE, 
    created_at          TIMESTAMP WITH TIME ZONE    DEFAULT now() NOT NULL, 
    updated_at          TIMESTAMP WITH TIME ZONE 
);

CREATE UNIQUE INDEX IF NOT EXISTS z_report_invoices_z_report_invoice_idx ON z_report_invoices(z_report_id, invoice_id);
