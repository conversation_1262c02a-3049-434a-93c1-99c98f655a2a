stages:
  - deploy

variables:
  APP_NAME: "compliance-and-risk-management-backend"
  APP_DIR: "/home/<USER>/Apps/PhoneNumberHashDecoder/phone-number-hash-decoder"
  GOLANG_VERSION: "1.22.2"
  SSH_HOST: "**************"
  SSH_USER: "gidemn"

deploy:
  stage: deploy
  before_script:
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - ssh-keyscan $SSH_HOST >> ~/.ssh/known_hosts
  script:
    - ssh ${SSH_USER}@${SSH_HOST} "cd ${APP_DIR} && ./deploy.sh"
  only:
    - main
