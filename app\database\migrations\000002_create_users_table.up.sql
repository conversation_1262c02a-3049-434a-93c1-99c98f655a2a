CREATE TABLE IF NOT EXISTS users (
    id                  BIGSERIAL                   PRIMARY KEY,    
    first_name          <PERSON><PERSON><PERSON><PERSON>(150)                NOT NULL,
    last_name           <PERSON><PERSON><PERSON><PERSON>(150)                NOT NULL,
    email               VARCHAR(255)                NOT NULL,
    password            VARCHAR(255)                NOT NULL, 
    phone_number        VARCHAR(150),
    is_synced           B<PERSON><PERSON>EAN                     NOT NULL DEFAULT FALSE, 
    organization_id     BIGINT                      NOT NULL REFERENCES organizations (id),    
    deleted_at          TIMESTAMP WITH TIME ZONE, 
    created_at          TIMESTAMP WITH TIME ZONE    DEFAULT now() NOT NULL,
    updated_at          TIMESTAMP WITH TIME ZONE
);
