package analytics

import (
	"compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/middleware"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/web/auth"

	"compliance-and-risk-management-backend/app/services"

	"github.com/gin-gonic/gin"
)

func AddEndpoints(
	routerGroup *gin.RouterGroup,
	transactionsDB *database.AppTransactionsDB,
	analyticsService services.AnalyticsService,
	sessionAuthenticator auth.SessionAuthenticator,
	sessionService services.SessionService,
	storeRepository repos.StoreRepository,
) {

	protectedAPI := routerGroup.Group("").Use(middleware.AllowOnlyActiveUser(transactionsDB, sessionAuthenticator, sessionService))
	{
		protectedAPI.GET("/analytics", getDashboardAnalytics(transactionsDB, analyticsService, storeRepository))
		protectedAPI.GET("/analytics/dashboard", getDashboardAnalytics(transactionsDB, analyticsService, storeRepository))
	}
}
