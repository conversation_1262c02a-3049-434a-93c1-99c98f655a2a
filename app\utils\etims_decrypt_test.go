package utils

import (
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

func TestDecryptEtimsData(t *testing.T) {
	Convey("DecryptEtimsData Util", t, func() {

		Convey("can decrypt data", func() {

			rawString := "rtZzYYaQvIKeh22Vz4FSzEM2OjLvK+k8GJr3bmPiw/+1oSwnsk8RZ8mGGKm5/qsh2bUfxr8w6OpMVszz/1dU8A=="
			decryptedData, err := DecryptEtimsData(rawString)
			So(err, ShouldBeNil)
			So(decryptedData, ShouldEqual, "09224EC00BCB483EA490EF12A247FEDAB870C86617F1498C89D1")
		})

		Convey("can encrypt data", func() {

			originalString := "09224EC00BCB483EA490EF12A247FEDAB870C86617F1498C89D1"

			encryptionKey := "DFC7A26B5BEEFFC0E85CE77FBE0060D6F85F0BDADF79ED875A00000000000000"

			encryptedData, err := EncryptEtimsData(originalString, encryptionKey)
			So(err, ShouldBeNil)
			So(encryptedData, ShouldEqual, "rtZzYYaQvIKeh22Vz4FSzEM2OjLvK+k8GJr3bmPiw/+1oSwnsk8RZ8mGGKm5/qsh2bUfxr8w6OpMVszz/1dU8A==")
		})
	})
}
