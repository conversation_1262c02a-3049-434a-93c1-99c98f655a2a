package entities

type (
	XeroRefreshToken struct {
		SequentialIdentifier
		AccessToken      string `json:"access_token"`
		ExpiresIn        int64  `json:"expires_in"`
		OrganizationID   int64  `json:"organization_id"`
		OrganizationName string `json:"organization_name"`
		RefreshToken     string `json:"refresh_token"`
		Scope            string `json:"scope"`
		TenantID         string `json:"tenant_id"`
		TenantType       string `json:"tenant_type"`
		TokenType        string `json:"token_type"`
		Timestamps
	}

	XeroInvoiceList struct {
		ID           string        `json:"id"`
		Status       int64         `json:"status"`
		ProviderName string        `json:"ProviderName"`
		DateTimeUTC  string        `json:"DateTimeUTC"`
		XeroInvoices []XeroInvoice `json:"Invoices"`
	}

	XeroInvoice struct {
		Type                   string                `json:"Type"`
		InvoiceID              string                `json:"InvoiceID"`
		InvoiceNumber          string                `json:"InvoiceNumber"`
		Reference              string                `json:"Reference"`
		Prepayments            []interface{}         `json:"Prepayments"`
		Overpayments           []interface{}         `json:"Overpayments"`
		AmountDue              float64               `json:"AmountDue"`
		AmountPaid             float64               `json:"AmountPaid"`
		SentToContact          bool                  `json:"SentToContact"`
		CurrencyRate           float64               `json:"CurrencyRate"`
		IsDiscounted           bool                  `json:"IsDiscounted"`
		HasAttachments         bool                  `json:"HasAttachments"`
		HasErrors              bool                  `json:"HasErrors"`
		Attachments            []interface{}         `json:"Attachments"`
		InvoicePaymentServices []interface{}         `json:"InvoicePaymentServices"`
		Contact                XeroContact           `json:"Contact"`
		DateString             string                `json:"DateString"`
		Date                   string                `json:"Date"`
		DueDateString          string                `json:"DueDateString"`
		DueDate                string                `json:"DueDate"`
		BrandingThemeID        string                `json:"BrandingThemeID"`
		Status                 string                `json:"Status"`
		LineAmountTypes        string                `json:"LineAmountTypes"`
		LineItems              []XeroInvoiceLineItem `json:"LineItems"`
		SubTotal               float64               `json:"SubTotal"`
		TotalTax               float64               `json:"TotalTax"`
		Total                  float64               `json:"Total"`
		UpdatedDateUTC         string                `json:"UpdatedDateUTC"`
		CurrencyCode           string                `json:"CurrencyCode"`
	}

	XeroInvoiceLineItem struct {
		Description      string        `json:"Description"`
		UnitAmount       float64       `json:"UnitAmount"`
		TaxType          string        `json:"TaxType"`
		TaxAmount        float64       `json:"TaxAmount"`
		LineAmount       float64       `json:"LineAmount"`
		AccountCode      string        `json:"AccountCode"`
		Tracking         []interface{} `json:"Tracking"`
		Quantity         float64       `json:"Quantity"`
		LineItemID       string        `json:"LineItemID"`
		ValidationErrors []interface{} `json:"ValidationErrors"`
	}

	XeroContact struct {
		ContactID                   string               `json:"ContactID"`
		ContactStatus               string               `json:"ContactStatus"`
		Name                        string               `json:"Name"`
		EmailAddress                string               `json:"EmailAddress"`
		BankAccountDetails          string               `json:"BankAccountDetails"`
		Addresses                   []XeroContactAddress `json:"Addresses"`
		Phones                      []XeroContactPhone   `json:"Phones"`
		UpdatedDateUTC              string               `json:"UpdatedDateUTC"`
		ContactGroups               []interface{}        `json:"ContactGroups"`
		IsSupplier                  bool                 `json:"IsSupplier"`
		IsCustomer                  bool                 `json:"IsCustomer"`
		DefaultCurrency             string               `json:"DefaultCurrency"`
		SalesTrackingCategories     []interface{}        `json:"SalesTrackingCategories"`
		PurchasesTrackingCategories []interface{}        `json:"PurchasesTrackingCategories"`
		ContactPersons              []interface{}        `json:"ContactPersons"`
		HasValidationErrors         bool                 `json:"HasValidationErrors"`
	}

	XeroContactAddress struct {
		AddressType string `json:"AddressType"`
		City        string `json:"City"`
		Region      string `json:"Region"`
		PostalCode  string `json:"PostalCode"`
		Country     string `json:"Country"`
	}

	XeroContactPhone struct {
		PhoneType        string `json:"PhoneType"`
		PhoneNumber      string `json:"PhoneNumber"`
		PhoneAreaCode    string `json:"PhoneAreaCode"`
		PhoneCountryCode string `json:"PhoneCountryCode"`
	}

	XeroConnectionsResponse struct {
		XeroConnections []XeroConnection
	}

	XeroConnection struct {
		ID             string `json:"id"`
		AuthEventID    string `json:"authEventId"`
		TenantID       string `json:"tenantId"`
		TenantType     string `json:"tenantType"`
		TenantName     string `json:"tenantName"`
		CreatedDateUtc string `json:"createdDateUtc"`
		UpdatedDateUtc string `json:"updatedDateUtc"`
	}
)
