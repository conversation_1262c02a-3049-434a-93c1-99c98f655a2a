package handler

import (
	"context"
	"fmt"
	"log"
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/services"
	"compliance-and-risk-management-backend/app/utils"
	"os"
	"strings"
)

func SendEmails(
	operations txns_db.TransactionsSQLOperations,
	invoiceService services.InvoiceService,
) {

	utils.GetTodaysInvoiceDate()
	utils.CreateDirectories()

	emailSendingInputFolder := os.Getenv("EMAIL_SENDING_INPUT_FOLDER")
	emailSendingBackupFolder := os.Getenv("EMAIL_SENDING_BACKUP_FOLDER")
	canSendEmail := false

	files, err := os.ReadDir(emailSendingInputFolder + "/")
	if err != nil {
		log.Printf("Error processing emails inputFolder contents err=[%v]\n", err)
	}

	counter := 1

	for _, f := range files {

		// Process 3 files at a time
		if counter == 3 {
			return
		}

		fileName := f.Name()

		info, err := f.Info()
		if err != nil {
			fmt.Printf("unable to retrieve sive for file=[%v]\n", f.Name())
			return
		}

		fileSize := info.Size()
		if fileSize == 0 {
			utils.DeleteFile(emailSendingInputFolder + "//" + fileName)
			return
		}

		log.Printf("Email sending found file=[%v]\n", fileName)

		utils.CopyFile(emailSendingInputFolder+"//"+fileName, emailSendingBackupFolder+"//"+fileName)
		utils.DeleteFile(emailSendingInputFolder + "//" + fileName)

		invoiceNumber := strings.ToUpper(fileName)
		invoiceNumber = strings.ReplaceAll(invoiceNumber, ".PDF", "")
		invoiceNumber = strings.TrimSpace(invoiceNumber)

		// Send email
		invoice, err := invoiceService.GetInvoiceByInvoiceNumber(context.Background(), invoiceNumber)
		if err != nil {
			log.Printf("failed to find invoice by invoiceNumber=[%v], err=[%v]\n", invoiceNumber, err)
		}

		customerEmail := invoice.CustomerEmail

		if len(customerEmail) > 0 {
			canSendEmail = true
		}

		fmt.Printf("invoiceNumber=[%v], CustomerEmail=[%v], canSendEmail=[%v] \n", invoiceNumber, customerEmail, canSendEmail)

		if canSendEmail {

			fmt.Printf("Sending email...\n")
			invoice.CustomerEmail = customerEmail
			invoice.FileName = fmt.Sprintf("%v//%v", emailSendingBackupFolder, fileName)

			// Update db
			isEmailSent := utils.Office365SendEmail(invoice)
			if isEmailSent {
				invoice.IsEmailSent = true

				// response = saveInvoiceToDB(db, invoice)
				err = invoiceService.SaveInvoice(context.Background(), operations, invoice)
				if err != nil {
					log.Printf("failed to save invoice, err=[%v]\n", err)
				}
			}
		}

		counter++
	}
}
