package providers

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"

	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/utils"
)

type (
	Twitter interface {
		FilterUserTweets(twitterUserID string, filter *entities.PaginationFilter) (*entities.TwitterFeed, error)
		GetTwitterUserByUsername(twitterHandle string) (*entities.TwitterUserData, error)
		MakeTwitterPost(form *forms.TwitterPostForm) (*entities.TweetPostAPIRespose, error)
	}

	twitterAuthResponse struct {
		TokenType   string `json:"token_type"`
		AccessToken string `json:"access_token"`
	}

	AppTwitter struct {
		accessToken       string
		accessTokenSecret string
		apiURL            string
		bearerToken       string
		clientID          string
		clientSecret      string
	}
)

func NewTwitter() Twitter {
	return NewTwitterWithCredentials(
		os.Getenv("TWITTER_ACCESS_TOKEN"),
		os.<PERSON>env("TWITTER_ACCESS_TOKEN_SECRET"),
		os.<PERSON>env("TWITTER_API_URL"),
		os.Getenv("TWITTER_CLIENT_ID"),
		os.Getenv("TWITTER_CLIENT_SECRET"),
	)
}

func NewTwitterWithCredentials(accessToken, accessTokenSecret, apiURL, clientID, clientSecret string) Twitter {

	appTwitter := &AppTwitter{
		accessToken:       accessToken,
		accessTokenSecret: accessTokenSecret,
		apiURL:            apiURL,
		clientID:          clientID,
		clientSecret:      clientSecret,
	}

	go appTwitter.setBearerToken()

	return appTwitter
}

func (s *AppTwitter) FilterUserTweets(
	twitterUserID string,
	filter *entities.PaginationFilter,
) (*entities.TwitterFeed, error) {

	twitterFeed := &entities.TwitterFeed{}
	tweets := make([]*entities.Tweet, 0)
	apiURL := fmt.Sprintf("%v/2/users/%v/tweets?expansions=attachments.media_keys&media.fields=url", s.apiURL, twitterUserID)

	if filter != nil {
		if filter.Per > 0 {
			apiURL += fmt.Sprintf("&max_results=%v", filter.Per)

		} else if len(filter.TwitterNextToken) > 0 && len(filter.TwitterSinceID) > 0 {
			apiURL += fmt.Sprintf("&since_id=%v&next_token=%v", filter.TwitterSinceID, filter.TwitterNextToken)
		}
	}

	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		return twitterFeed, utils.NewError(
			err,
			"unable to retrieve user tweets",
		)
	}

	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Add("Authorization", "Bearer "+s.bearerToken)
	req.Header.Add("Accept", "application/json")
	req.Header.Add("Cache-Control", "no-cache")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return twitterFeed, utils.NewError(
			err,
			"unable to process twitter feed request.",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return twitterFeed, utils.NewError(
			err,
			"unable to process twitter feed response body.",
		)
	}

	apiResponse := entities.TweetsDataAPIResponse{}
	json.Unmarshal([]byte(string(body)), &apiResponse)

	mediaMap := make(map[string]*entities.Media)
	tweetMediaMap := make(map[string][]*entities.Media)

	for _, tweet := range apiResponse.Tweets {

		tweetMediaList := make([]*entities.Media, 0)

		for _, mediaKey := range tweet.Attachments.MediaKeys {
			media := &entities.Media{
				TweetID:  tweet.ID,
				MediaKey: mediaKey,
			}

			tweetMediaList = append(tweetMediaList, media)
			mediaMap[mediaKey] = media
		}

		tweet.MediaList = tweetMediaList
		tweetMediaMap[tweet.ID] = tweetMediaList
		tweets = append(tweets, tweet)
	}

	for _, media := range apiResponse.MediaIncludes.MediaList {
		mediaMap[media.MediaKey].Type = media.Type
		mediaMap[media.MediaKey].URL = media.URL
	}

	twitterFeed.Tweets = tweets
	twitterFeed.Meta = apiResponse.Meta

	return twitterFeed, nil
}

func (s *AppTwitter) GetTwitterUserByUsername(
	username string,
) (*entities.TwitterUserData, error) {

	twitterUser := &entities.TwitterUser{}
	twitterUserData := &entities.TwitterUserData{}

	if len(s.bearerToken) == 0 {
		err := s.setBearerToken()
		if err != nil {
			log.Printf("failed to set twitter bearer token, err=[%v]\n", err)
			return twitterUserData, err
		}
	}

	apiURL := fmt.Sprintf("%v/2/users/by/username/%v", s.apiURL, username)
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		return twitterUserData, utils.NewError(
			err,
			"unable to send twitter userID request",
		)
	}

	req.Header.Add("Authorization", "Bearer "+s.bearerToken)
	req.Header.Add("Accept", "application/json")
	req.Header.Add("Cache-Control", "no-cache")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return twitterUserData, utils.NewError(
			err,
			"unable to process twitter userID request.",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return twitterUserData, utils.NewError(
			err,
			"unable to process twitter userID response body.",
		)
	}

	json.Unmarshal([]byte(string(body)), &twitterUser)
	twitterUserData = &twitterUser.UserData

	return twitterUserData, nil
}

func (s *AppTwitter) MakeTwitterPost(form *forms.TwitterPostForm) (*entities.TweetPostAPIRespose, error) {

	tweetAPIResponse := &entities.TweetPostAPIRespose{}

	apiURL := fmt.Sprintf("%v/2/tweets", s.apiURL)
	method := "POST"

	tweetPayload, err := json.Marshal(form)
	if err != nil {
		log.Printf("failed to marshal twitter form payload, err=[%v]\n", err)
	}

	payload := strings.NewReader(string(tweetPayload))

	client := &http.Client{}
	req, err := http.NewRequest(method, apiURL, payload)
	if err != nil {
		fmt.Println(err)
		log.Printf("error crafting tweet request, err=[%v]\n", err)
		return tweetAPIResponse, err
	}

	authStr := s.generateOAuthAuthorization(method, apiURL)

	req.Header.Add("Authorization", authStr)
	req.Header.Add("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		fmt.Println(err)
		log.Printf("error posting tweet, err=[%v]\n", err)
		return tweetAPIResponse, err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		fmt.Println(err)
		log.Printf("error reading tweet response, err=[%v]\n", err)
		return tweetAPIResponse, err
	}

	responseBody := string(body)
	json.Unmarshal([]byte(responseBody), tweetAPIResponse)

	return tweetAPIResponse, nil
}

func (s *AppTwitter) generateOAuthAuthorization(method, apiURL string) string {

	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	nonce := utils.GenerateURLUUID()

	authStr := fmt.Sprintf("OAuth oauth_consumer_key=\"%v\",", s.clientID)
	authStr += fmt.Sprintf("oauth_token=\"%v\",", s.accessToken)
	authStr += "oauth_signature_method=\"HMAC-SHA1\","
	authStr += fmt.Sprintf("oauth_timestamp=\"%v\",", timestamp)
	authStr += fmt.Sprintf("oauth_nonce=\"%v\",", nonce)
	authStr += "oauth_version=\"1.0\","

	params := make(map[string]string, 0)
	params["oauth_consumer_key"] = s.clientID
	params["oauth_token"] = s.accessToken
	params["oauth_signature_method"] = "HMAC-SHA1"
	params["oauth_timestamp"] = timestamp
	params["oauth_nonce"] = nonce
	params["oauth_version"] = "1.0"

	oauthSignature := utils.GenerateTwitterOAuthSignature(method, apiURL, params, s.clientSecret, s.accessTokenSecret)
	oauthSignature = url.QueryEscape(oauthSignature)

	authStr += fmt.Sprintf("oauth_signature=\"%v\"", oauthSignature)

	return authStr
}

func (s *AppTwitter) setBearerToken() error {

	authURL := fmt.Sprintf("%v/oauth2/token?grant_type=client_credentials", s.apiURL)
	req, err := http.NewRequest("POST", authURL, nil)
	if err != nil {
		return utils.NewError(
			err,
			"unable to POST twitter authentication request",
		)
	}

	auth := s.clientID + ":" + s.clientSecret
	basicAuth := base64.StdEncoding.EncodeToString([]byte(auth))
	req.Header.Add("Authorization", "Basic "+basicAuth)
	req.Header.Add("Accept", "application/json")
	req.Close = true

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return utils.NewError(
			err,
			"unable to process twitter authentication request.",
		)
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return utils.NewError(
			err,
			"unable to process twitter response body.",
		)
	}

	tokenResponse := twitterAuthResponse{}
	json.Unmarshal([]byte(string(body)), &tokenResponse)
	s.bearerToken = tokenResponse.AccessToken

	return nil
}
