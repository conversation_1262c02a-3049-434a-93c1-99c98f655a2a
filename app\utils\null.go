package utils

import (
	"strings"
	"time"
)

func Bool(v bool) *bool {
	return &v
}

func BoolValue(v *bool) bool {
	if v != nil {
		return *v
	}
	return false
}

func Float64(v float64) *float64 {
	return &v
}

func Float64Value(v *float64) float64 {
	if v != nil {
		return *v
	}
	return 0
}

func Int(v int) *int {
	return &v
}

func IntValue(v *int) int {
	if v != nil {
		return *v
	}
	return 0
}

func Int64(v int64) *int64 {
	return &v
}

func Int64Value(v *int64) int64 {
	if v != nil {
		return *v
	}
	return 0
}

func String(v string) *string {
	return &v
}

func StringValue(v *string) string {
	if v != nil {
		return *v
	}
	return ""
}

func Time(v time.Time) *time.Time {
	return &v
}

func TimeValue(v *time.Time) time.Time {
	if v != nil {
		return *v
	}
	return time.Time{}
}

func TrimString(v *string) *string {
	if v == nil {
		return v
	}

	return String(strings.TrimSpace(StringValue(v)))
}
