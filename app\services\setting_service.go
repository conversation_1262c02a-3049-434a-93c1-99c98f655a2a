package services

import (
	"context"
	"database/sql"
	"errors"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/repos"

	txns_db "compliance-and-risk-management-backend/app/database"
)

type SettingService interface {
	CreateSetting(ctx context.Context, operations txns_db.TransactionsSQLOperations, setting forms.CreateSettingForm) (*entities.Setting, error)
	FilterSettings(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter) (*entities.SettingList, error)
	FindSettingByID(ctx context.Context, settingID int64) (*entities.Setting, error)
	GetSettingByName(ctx context.Context, settingNumber string) (*entities.Setting, error)
	SaveSetting(ctx context.Context, operations txns_db.TransactionsSQLOperations, setting *entities.Setting) error
	UpdateSetting(ctx context.Context, operations txns_db.TransactionsSQLOperations, setting forms.CreateSettingForm) (*entities.Setting, error)
}

type AppSettingService struct {
	settingRepository repos.SettingRepository
}

func NewSettingService(
	settingRepo repos.SettingRepository,
) SettingService {
	return &AppSettingService{
		settingRepository: settingRepo,
	}
}

func (s *AppSettingService) CreateSetting(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	form forms.CreateSettingForm,
) (*entities.Setting, error) {

	setting := &entities.Setting{}

	setting, err := s.settingRepository.GetSettingByName(ctx, form.Name)
	if err != nil && err != sql.ErrNoRows {
		return setting, errors.New("setting already exists")
	}

	userID := form.UserID

	setting.Name = form.Name
	setting.Value = form.Value
	setting.CreatedBy = userID
	setting.UpdatedBy = &userID

	err = s.settingRepository.Save(ctx, operations, setting)
	if err != nil {
		return setting, err
	}

	return setting, nil
}

func (s *AppSettingService) FilterSettings(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) (*entities.SettingList, error) {

	settingList := &entities.SettingList{}

	count, err := s.settingRepository.CountSettings(ctx, filter)
	if err != nil {
		return settingList, err
	}

	settings, err := s.settingRepository.FilterSettings(ctx, operations, filter)
	if err != nil {
		return settingList, err
	}

	settingList.Settings = settings

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	settingList.Pagination = pagination

	return settingList, nil
}

func (s *AppSettingService) FindSettingByID(
	ctx context.Context,
	settingID int64,
) (*entities.Setting, error) {

	setting, err := s.settingRepository.GetSettingByID(ctx, settingID)
	if err != nil {
		return setting, err
	}

	return setting, nil
}

func (s *AppSettingService) GetSettingByName(
	ctx context.Context,
	settingNumber string,
) (*entities.Setting, error) {

	setting, err := s.settingRepository.GetSettingByName(ctx, settingNumber)
	if err != nil {
		return setting, err
	}

	return setting, nil
}

func (s *AppSettingService) SaveSetting(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	setting *entities.Setting,
) error {

	err := s.settingRepository.Save(ctx, operations, setting)
	if err != nil {
		return err
	}

	return nil
}

func (s *AppSettingService) UpdateSetting(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	form forms.CreateSettingForm,
) (*entities.Setting, error) {

	setting := &entities.Setting{}

	setting, err := s.settingRepository.GetSettingByName(ctx, form.Name)
	if err != nil && err != sql.ErrNoRows {
		return setting, errors.New("setting already exists")
	}

	userID := form.UserID

	setting.Name = form.Name
	setting.Value = form.Value
	setting.CreatedBy = userID
	setting.UpdatedBy = &userID

	err = s.settingRepository.Save(ctx, operations, setting)
	if err != nil {
		return setting, err
	}

	return setting, nil
}
