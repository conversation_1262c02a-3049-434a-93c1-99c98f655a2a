CREATE TABLE IF NOT EXISTS organizations (
    id                BIGSERIAL                   PRIMARY KEY,    
    name              <PERSON><PERSON><PERSON><PERSON>(150)                NOT NULL,
    address           VARCHAR(150),
    branch            VARCHAR(150),
    contact_person    VARCHAR(250)                NOT NULL, 
    email             VARCHAR(250)                NOT NULL,
    phone_number      VARCHAR(150),
    date_format       VARCHAR(150),  
    password          VARCHAR(250)                NOT NULL,  
    deleted_at        TIMESTAMP,
    created_at        TIMESTAMP WITH TIME ZONE    DEFAULT now() NOT NULL, 
    updated_at        TIMESTAMP WITH TIME ZONE 
);

CREATE UNIQUE INDEX IF NOT EXISTS organizations_name_email_uidx ON organizations(name, email);
