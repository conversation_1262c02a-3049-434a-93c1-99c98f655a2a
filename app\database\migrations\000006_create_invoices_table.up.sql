CREATE TABLE IF NOT EXISTS invoices (
    id                  BIGSERIAL                   PRIMARY KEY,    
    invoice_number      VARCHAR(150)                NOT NULL,
    invoice_date        VARCHAR(50)                 NOT NULL,
    customer_name       VARCHAR(255)                NOT NULL,
    customer_email      TEXT                        NOT NULL DEFAULT '',
    customer_phone      VARCHAR(150),
    customer_tin        VARCHAR(50),
    customer_vrn        VARCHAR(50),
    gc                  BIGINT,
    dc                  BIGINT,
    rctvnum             VARCHAR(50),    
    gross_amount        NUMERIC(15,2)               NOT NULL,
    net_amount          NUMERIC(15,2)               NOT NULL,
    vat                 NUMERIC(15,2)               NOT NULL,
    grand_total         NUMERIC(15,2)               NOT NULL,
    is_synced           BOOLEAN                     NOT NULL DEFAULT FALSE, 
    file_name           VARCHAR(150)                NOT NULL,
    is_email_sent       BOOLEAN                     NOT NULL DEFAULT FALSE,
    is_z_reported       BOOLEAN                     NOT NULL DEFAULT FALSE,
    organization_id     BIGINT                      NOT NULL, 
    original_file_name  VARCHAR(150)                NOT NULL,
    receipt_code        VARCHAR(150), 
    signature           VARCHAR(255)                NOT NULL DEFAULT '',
    signed_date         VARCHAR(50)                 NOT NULL,
    taxable_amount      NUMERIC(15,2),
    verification_url    VARCHAR(150)                NOT NULL DEFAULT '',
    created_at          TIMESTAMP WITH TIME ZONE    DEFAULT now() NOT NULL,
    updated_at          TIMESTAMP WITH TIME ZONE
);

CREATE UNIQUE INDEX IF NOT EXISTS invoices_invoice_number_org_idx ON invoices(invoice_number, organization_id);