package services

import (
	"context"
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"io/ioutil"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/models"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/utils"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	"github.com/jung-kurt/gofpdf"
	"golang.org/x/crypto/pkcs12"
	"gopkg.in/guregu/null.v3"

	txns_db "compliance-and-risk-management-backend/app/database"
)

type TRAVFDService interface {
	CheckCompanyRegistrationDetails(ctx context.Context)
	PostZReport(ctx context.Context, transactionsDB txns_db.TransactionsDB)
	RegisterCompanyOnStartUp(ctx context.Context) error
	SignInvoice(ctx context.Context, invoice *entities.Invoice) error
}

type AppTRAVFDService struct {
	companyRegistrationRepository repos.CompanyRegistrationRepository
	customerIDTypeRepository      repos.CustomerIDTypeRepository
	invoiceRepository             repos.InvoiceRepository
	zReportRepository             repos.ZReportRepository
	zReportInvoicesRepository     repos.ZReportInvoiceRepository
}

func NewTRAVFDService(
	companyRegistrationRepository repos.CompanyRegistrationRepository,
	customerIDTypeRepository repos.CustomerIDTypeRepository,
	invoiceRepository repos.InvoiceRepository,
	zReportRepository repos.ZReportRepository,
	zReportInvoicesRepository repos.ZReportInvoiceRepository,
) TRAVFDService {
	return &AppTRAVFDService{
		companyRegistrationRepository: companyRegistrationRepository,
		customerIDTypeRepository:      customerIDTypeRepository,
		invoiceRepository:             invoiceRepository,
		zReportRepository:             zReportRepository,
		zReportInvoicesRepository:     zReportInvoicesRepository,
	}
}

func (s *AppTRAVFDService) CheckCompanyRegistrationDetails(
	ctx context.Context,
) {

	for {

		// Get company registration info by TIN Number
		// Check last updated at, if within the last 6 hours
		// if greater than or empty - fetch new company registration details

		companyRegistration, err := s.companyRegistrationRepository.GetCompanyRegistrationByCompanyTIN(ctx, os.Getenv("TIN_NUMBER"))
		if err != nil {
			if err == sql.ErrNoRows {

				// Create new entry via a company registration
				registerCompany(ctx, s.companyRegistrationRepository, companyRegistration)

			} else {
				fmt.Printf("error retrieving company details, err=[%v]\n", err)
			}
		}

		timeNow := time.Now()
		sixHrsFromUpdateTime := companyRegistration.UpdatedAt.Add(6 * time.Hour)
		if timeNow.After(sixHrsFromUpdateTime) {

			// fmt.Printf("Updating co registration details...\n")
			// Update reg info
			_, err := registerCompany(ctx, s.companyRegistrationRepository, companyRegistration)
			if err != nil {
				fmt.Printf("error registering company, err=[%v]\n", err)
			}

		}

		time.Sleep(1 * time.Second)
	}
}

func (s *AppTRAVFDService) PostZReport(
	ctx context.Context,
	transactionsDB txns_db.TransactionsDB,
) {

	// Generate XML report - with the daily calculations.
	// 1. POST it to TRA server.
	// 2. Generate Z-Report PDF file

	fmt.Printf("posting z report...\n")

	companyRegistration, err := s.companyRegistrationRepository.GetCompanyRegistrationByCompanyTIN(
		ctx,
		os.Getenv("TIN_NUMBER"),
	)
	if err != nil {
		fmt.Printf("error retrieving company reg info by tin, err=[%v]\n", err)
	}

	// fmt.Printf("companyRegistration details=[%v]\n", companyRegistration)

	registerCompany(ctx, s.companyRegistrationRepository, companyRegistration)

	t := time.Now()
	zreportDate := fmt.Sprintf("%d-%02d-%02d", t.Year(), t.Month(), t.Day())       // 2020-11-24
	zreportTime := fmt.Sprintf("%02d:%02d:%02d", t.Hour(), t.Minute(), t.Second()) // 11:40:00
	part1PdfData := make([][]string, 0)
	part2PdfData := make([][]string, 0)
	part3PdfData := make([][]string, 0)
	part4PdfData := make([][]string, 0)
	part5PdfData := make([][]string, 0)

	zReportTotals, err := s.invoiceRepository.GetZReportTotals(ctx, transactionsDB, true)
	if err != nil {
		fmt.Printf("error retrieving z-report totals, err=[%v]\n", err)
		return
	}

	netAmount := fmt.Sprintf("%.2f", zReportTotals.NetAmount)
	vatAmount := fmt.Sprintf("%.2f", zReportTotals.VatAmount)
	pmtAmount := fmt.Sprintf("%.2f", zReportTotals.TotalAmount)
	dailyTotalAmt := fmt.Sprintf("%.2f", zReportTotals.TotalAmount)

	// Part 1 Z-Report data - DAILY Z-REPORT
	part1PdfData = append(part1PdfData, []string{"DAILY TOTAL AMOUNT:", formatStrFigure(dailyTotalAmt, 11)})
	part1PdfData = append(part1PdfData, []string{"CORRECTIONS:", formatStrFigure("0.00", 11)})
	part1PdfData = append(part1PdfData, []string{"DISCOUNTS:", formatStrFigure("0.00", 11)})
	part1PdfData = append(part1PdfData, []string{"SURCHAGES:", formatStrFigure("0.00", 11)})
	part1PdfData = append(part1PdfData, []string{"VOIDS:", formatStrFigure("0", 13)})
	part1PdfData = append(part1PdfData, []string{"VOIDS TOTAL:", formatStrFigure("0.00", 11)})
	part1PdfData = append(part1PdfData, []string{"FIRST RECEIPT:", formatStrFigure("0.00", 11)})
	part1PdfData = append(part1PdfData, []string{"LAST RECEIPT:", formatStrFigure("0.00", 11)})
	part1PdfData = append(part1PdfData, []string{"RECEIPTS ISSUED:", formatStrFigure("0.00", 11)})

	// Part 2 Z-Report data - PAYMENTS REPORT
	part2PdfData = append(part2PdfData, []string{"CASH:", formatStrFigure(pmtAmount, 11)})
	part2PdfData = append(part2PdfData, []string{"CHEQUE:", formatStrFigure("0.00", 11)})
	part2PdfData = append(part2PdfData, []string{"CCARD:", formatStrFigure("0.00", 11)})
	part2PdfData = append(part2PdfData, []string{"EMONEY:", formatStrFigure("0.00", 11)})

	// Part 3 Z-Report data - TAX REPORT
	part3PdfData = append(part3PdfData, []string{"1. TAX A-(18.00)", ""})
	part3PdfData = append(part3PdfData, []string{"TURNOVER:", formatStrFigure(pmtAmount, 11)})
	part3PdfData = append(part3PdfData, []string{"NET SUM:", formatStrFigure(netAmount, 11)})
	part3PdfData = append(part3PdfData, []string{"TAX", formatStrFigure(vatAmount, 11)})
	part3PdfData = append(part3PdfData, []string{"2. TAX B-(0.00)", ""})
	part3PdfData = append(part3PdfData, []string{"TURNOVER:", formatStrFigure("0.00", 11)})
	part3PdfData = append(part3PdfData, []string{"NET SUM:", formatStrFigure("0.00", 11)})
	part3PdfData = append(part3PdfData, []string{"TAX", formatStrFigure("0.00", 11)})
	part3PdfData = append(part3PdfData, []string{"3. TAX C-(0.00)", ""})
	part3PdfData = append(part3PdfData, []string{"TURNOVER:", formatStrFigure("0.00", 11)})
	part3PdfData = append(part3PdfData, []string{"NET SUM:", formatStrFigure("0.00", 11)})
	part3PdfData = append(part3PdfData, []string{"TAX", formatStrFigure("0.00", 11)})
	part3PdfData = append(part3PdfData, []string{"4. TAX D-(0.00)", ""})
	part3PdfData = append(part3PdfData, []string{"TURNOVER:", formatStrFigure("0.00", 11)})
	part3PdfData = append(part3PdfData, []string{"NET SUM:", formatStrFigure("0.00", 11)})
	part3PdfData = append(part3PdfData, []string{"TAX", formatStrFigure("0.00", 11)})
	part3PdfData = append(part3PdfData, []string{"5. TAX E-(0.00)", ""})
	part3PdfData = append(part3PdfData, []string{"TURNOVER:", formatStrFigure("0.00", 11)})
	part3PdfData = append(part3PdfData, []string{"NET SUM:", formatStrFigure("0.00", 11)})
	part3PdfData = append(part3PdfData, []string{"TAX", formatStrFigure("0.00", 11)})

	// Part 4 Z-Report data - TOTAL
	part4PdfData = append(part4PdfData, []string{"TURNOVER: (A+B+C)", formatStrFigure(pmtAmount, 11)})
	part4PdfData = append(part4PdfData, []string{"NET: (A+B+C)", formatStrFigure(netAmount, 11)})
	part4PdfData = append(part4PdfData, []string{"TAX: (A+B+C)", formatStrFigure(vatAmount, 11)})
	part4PdfData = append(part4PdfData, []string{"TURNOVER: (D+E)", formatStrFigure("0.00", 11)})

	// Retrieve all totals
	zReportGrandTotals, err := s.invoiceRepository.GetZReportTotals(ctx, transactionsDB, false)
	if err != nil {
		fmt.Printf("error retrieving z-report totals, err=[%v]\n", err)
		return
	}

	netAmount = fmt.Sprintf("%.2f", zReportGrandTotals.NetAmount)
	vatAmount = fmt.Sprintf("%.2f", zReportGrandTotals.VatAmount)
	pmtAmount = fmt.Sprintf("%.2f", zReportGrandTotals.TotalAmount)

	// Part 5 Z-Report data - TOTAL SUMMARY
	part5PdfData = append(part5PdfData, []string{"TOTAL GROSS: A", formatStrFigure(pmtAmount, 11)})
	part5PdfData = append(part5PdfData, []string{"TOTAL NET: A", formatStrFigure(netAmount, 11)})
	part5PdfData = append(part5PdfData, []string{"TOTAL TAX: A", formatStrFigure(vatAmount, 11)})
	part5PdfData = append(part5PdfData, []string{"TOTAL GROSS: B", formatStrFigure("0.00", 11)})
	part5PdfData = append(part5PdfData, []string{"TOTAL NET: B", formatStrFigure("0.00", 11)})
	part5PdfData = append(part5PdfData, []string{"TOTAL TAX: B", formatStrFigure("0.00", 11)})
	part5PdfData = append(part5PdfData, []string{"TOTAL GROSS: C", formatStrFigure("0.00", 11)})
	part5PdfData = append(part5PdfData, []string{"TOTAL NET: C", formatStrFigure("0.00", 11)})
	part5PdfData = append(part5PdfData, []string{"TOTAL TAX: C", formatStrFigure("0.00", 11)})
	part5PdfData = append(part5PdfData, []string{"TOTAL GROSS: D", formatStrFigure("0.00", 11)})
	part5PdfData = append(part5PdfData, []string{"TOTAL GROSS: E", formatStrFigure("0.00", 11)})
	part5PdfData = append(part5PdfData, []string{"GROSS", formatStrFigure(pmtAmount, 11)})

	toSign := "<ZREPORT>" +
		"<DATE>" + zreportDate + "</DATE>" +
		"<TIME>" + zreportTime + "</TIME>" +
		"<HEADER>" +
		"<LINE>" + companyRegistration.Name + "</LINE>" +
		"<LINE>PLOT:125/126/127,MAGOMENI STREET</LINE>" +
		"<LINE>TEL NO:+**********</LINE>" +
		"<LINE>DAR ES SALAAM,TANZANIA</LINE>" +
		"</HEADER>" +
		"<VRN>" + companyRegistration.VRN + "</VRN>" +
		"<TIN>" + os.Getenv("TIN_NUMBER") + "</TIN>" +
		"<TAXOFFICE>" + companyRegistration.TaxOffice + "</TAXOFFICE>" +
		"<REGID>" + companyRegistration.RegID + "</REGID>" +
		"<ZNUMBER>1</ZNUMBER>" +
		"<EFDSERIAL>" + os.Getenv("TRA_CERT_SERIAL") + "</EFDSERIAL>" +
		"<REGISTRATIONDATE>" + zreportDate + "</REGISTRATIONDATE>" +
		"<USER>" + companyRegistration.Username + "</USER>" +
		"<SIMIMSI>WEBAPI</SIMIMSI>" +
		"<TOTALS>" +
		"<DAILYTOTALAMOUNT>" + dailyTotalAmt + "</DAILYTOTALAMOUNT>" +
		"<GROSS>0.00</GROSS>" +
		"<CORRECTIONS>0.00</CORRECTIONS>" +
		"<DISCOUNTS>0.00</DISCOUNTS>" +
		"<SURCHARGES>0.00</SURCHARGES>" +
		"<TICKETSVOID>0</TICKETSVOID>" +
		"<TICKETSVOIDTOTAL>0.00</TICKETSVOIDTOTAL>" +
		"<TICKETSFISCAL>0</TICKETSFISCAL>" +
		"<TICKETSNONFISCAL>0</TICKETSNONFISCAL>" +
		"</TOTALS>" +
		"<VATTOTALS>" +
		"<VATRATE>A-18.00</VATRATE>" +
		"<NETTAMOUNT>" + netAmount + "</NETTAMOUNT>" +
		"<TAXAMOUNT>" + vatAmount + "</TAXAMOUNT>" +
		"<VATRATE>B-0.00</VATRATE>" +
		"<NETTAMOUNT>0.00</NETTAMOUNT>" +
		"<TAXAMOUNT>0.00</TAXAMOUNT>" +
		"<VATRATE>C-0.00</VATRATE>" +
		"<NETTAMOUNT>0.00</NETTAMOUNT>" +
		"<TAXAMOUNT>0.00</TAXAMOUNT>" +
		"<VATRATE>D-0.00</VATRATE>" +
		"<NETTAMOUNT>0.00</NETTAMOUNT>" +
		"<TAXAMOUNT>0.00</TAXAMOUNT>" +
		"<VATRATE>E-0.00</VATRATE>" +
		"<NETTAMOUNT>0.00</NETTAMOUNT>" +
		"<TAXAMOUNT>0.00</TAXAMOUNT>" +
		"</VATTOTALS>" +
		"<PAYMENTS>" +
		"<PMTTYPE>CASH</PMTTYPE>" +
		"<PMTAMOUNT>" + pmtAmount + "</PMTAMOUNT>" +
		"<PMTTYPE>CHEQUE</PMTTYPE>" +
		"<PMTAMOUNT>0.00</PMTAMOUNT>" +
		"<PMTTYPE>CCARD</PMTTYPE>" +
		"<PMTAMOUNT>0.00</PMTAMOUNT>" +
		"<PMTTYPE>EMONEY</PMTTYPE>" +
		"<PMTAMOUNT>0.00</PMTAMOUNT>" +
		"<PMTTYPE>INVOICE</PMTTYPE>" +
		"<PMTAMOUNT>0.00</PMTAMOUNT>" +
		"</PAYMENTS>" +
		"<CHANGES>" +
		"<VATCHANGENUM>0</VATCHANGENUM>" +
		"<HEADCHANGENUM>0</HEADCHANGENUM>" +
		"</CHANGES>" +
		"<ERRORS></ERRORS>" +
		"<FWVERSION>3.0</FWVERSION>" +
		"<FWCHECKSUM>WEBAPI</FWCHECKSUM>" +
		"</ZREPORT>"

	regDataSignedData := signData(toSign)

	payload := strings.NewReader("<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
		"<EFDMS>" +
		toSign +
		"<EFDMSSIGNATURE>" + regDataSignedData + "</EFDMSSIGNATURE>" +
		"</EFDMS>")

	// fmt.Printf("zreport payload=[\n%v\n]\n", payload)

	url := "https://virtual.tra.go.tz/efdmsRctApi/api/efdmszreport"
	method := "POST"

	client := &http.Client{}
	req, err := http.NewRequest(method, url, payload)
	if err != nil {
		fmt.Printf("error creating new request, err=[%v]\n", err)
	}

	certSerial := base64.StdEncoding.EncodeToString([]byte(os.Getenv("TRA_CERT_SERIAL")))

	token, err := sendTokenAuthenticationRequest(companyRegistration)
	if err != nil {
		fmt.Printf("error retrieving token, err=[%v]\n", err)
	}

	// fmt.Printf("Bearer token=[%v]\n\n", token.AccessToken)

	req.Header.Add("Authorization", "Bearer "+token.AccessToken)
	req.Header.Add("Cert-Serial", certSerial)
	req.Header.Add("Client", "WEBAPI")
	req.Header.Add("Content-Type", "application/xml")
	req.Header.Add("Routing-Key", "vfdzreport")

	res, err := client.Do(req)
	if err != nil {
		fmt.Printf("error sending zreport request, err=[%v]\n", err)
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		fmt.Printf("error reading zreport response, err=[%v]\n", err)
	}

	rawXmlData := string(body)
	fmt.Printf("zreport response=[%v]\n", rawXmlData)

	err = transactionsDB.InTransaction(ctx, func(ctx context.Context, operations txns_db.TransactionsSQLOperations) error {

		paginationFilter := &entities.PaginationFilter{
			IsZReported: null.BoolFrom(false),
		}

		// Get all the invoices not z-reported
		invoices, err := s.invoiceRepository.FilterInvoices(ctx, operations, paginationFilter)
		if err != nil {
			return err
		}

		zReportInvoicesNum := int64(len(invoices))

		// Save z-report data to db
		zReport := &entities.ZReport{
			InvoiceCount:   zReportInvoicesNum,
			NetAmount:      zReportTotals.NetAmount,
			VatAmount:      zReportTotals.VatAmount,
			TotalAmount:    zReportTotals.TotalAmount,
			PmtAmount:      zReportTotals.PmtAmount,
			Status:         "sent",
			ZReportDate:    zreportDate,
			ZReportTime:    zreportTime,
			ZReportPayload: toSign,
		}

		err = s.zReportRepository.Save(ctx, operations, zReport)
		if err != nil {
			fmt.Printf("failed to save z-report, err=[%v]\n", err)
			return err
		}

		// Generate Z-Report PDF file
		fileName := fmt.Sprintf("C://ESD_Documents//Z_Reports//Z-Report-%v.pdf", zReport.ID)
		utils.GeneratePDF(
			companyRegistration,
			zReport,
			part1PdfData,
			part2PdfData,
			part3PdfData,
			part4PdfData,
			part5PdfData,
			fileName,
		)

		zReport.FileName = fileName
		err = s.zReportRepository.Save(ctx, operations, zReport)
		if err != nil {
			fmt.Printf("failed to update z-report, err=[%v]\n", err)
			return err
		}

		// err = generateZReportPDF(companyRegistration, zReport, part1PdfData)
		// if err != nil {
		// 	fmt.Printf("failed to generate z-report pdf file, err=[%v]\n", err)
		// 	return err
		// }

		if zReportInvoicesNum == 0 {
			// Exit early if no invoices
			// Can send zero amt z-report though.
			return nil
		}

		// Update all invoices to is_z_reported = true
		zReportInvoices := make([]*entities.ZReportInvoice, 0)
		for _, invoice := range invoices {
			invoice.IsZReported = true

			zReportInvoice := &entities.ZReportInvoice{
				InvoiceID: invoice.ID,
				ZReportID: zReport.ID,
			}

			zReportInvoices = append(zReportInvoices, zReportInvoice)
		}

		err = s.invoiceRepository.UpdateMultipleInvoices(ctx, operations, invoices)
		if err != nil {
			fmt.Printf("failed to update multiple invoices, err=[%v]\n", err)
			return err
		}

		// Save invoices with z-report ID
		err = s.zReportInvoicesRepository.SaveMultiple(ctx, operations, zReportInvoices)
		if err != nil {
			fmt.Printf("failed to save z-report invoices, err=[%v]\n", err)
			return err
		}

		return nil
	})

	if err != nil {
		fmt.Printf("failed to save z-report data, err=[%v] \n", err)
	}

	// xml.Unmarshal([]byte(rawXmlData), registrationResponse)

	// fmt.Println("================================ vfdRegResponse ================================")
	// fmt.Println(rawXmlData)
	// fmt.Println("================================ vfdRegResponse ================================")
	// fmt.Println("================================ registrationResponse ================================")
	// fmt.Println(registrationResponse)
	// fmt.Println("================================ registrationResponse ================================")
}

func (s *AppTRAVFDService) RegisterCompanyOnStartUp(
	ctx context.Context,
) error {

	companyRegistration, err := s.companyRegistrationRepository.GetCompanyRegistrationByCompanyTIN(ctx, os.Getenv("TIN_NUMBER"))
	if err != nil {
		fmt.Printf("error retrieving company reg info by tin, err=[%v]\n", err)
		return err
	}

	// fmt.Printf("companyRegistration=[%v]\n", companyRegistration)

	registerCompany(ctx, s.companyRegistrationRepository, companyRegistration)
	fmt.Printf("Current GC : [%v]\n", companyRegistration.GC)
	return nil
}

func (s *AppTRAVFDService) SignInvoice(
	ctx context.Context,
	invoice *entities.Invoice,
) error {

	companyRegistration, err := s.companyRegistrationRepository.GetCompanyRegistrationByCompanyTIN(ctx, os.Getenv("TIN_NUMBER"))
	if err != nil {
		fmt.Printf("error retrieving company reg info by tin, err=[%v]\n", err)
		return err
	}

	token, err := sendTokenAuthenticationRequest(companyRegistration)
	if err != nil {
		return err
	}

	newGC := utils.ConvertStringToInt(companyRegistration.GC) + 1

	// to increment DC
	DC := fmt.Sprintf("%v", invoice.DC)
	GC := fmt.Sprintf("%v", newGC)

	RCTVNUM := fmt.Sprintf("%v%v", companyRegistration.ReceiptCode, GC)

	RCTNUM := GC
	fmt.Printf("----------------------------------------------------------------\n")
	fmt.Printf("GC=[%v]\n", GC)
	fmt.Printf("DC=[%v]\n", DC)
	fmt.Printf("RECEIPTCODE=[%v]\n", companyRegistration.ReceiptCode)
	fmt.Printf("RCTVNUM=[%v]\n", RCTVNUM)
	fmt.Printf("----------------------------------------------------------------\n")

	rctInfoURL := "https://virtual.tra.go.tz/efdmsRctApi/api/efdmsRctInfo"

	t := time.Now()
	receiptDate := fmt.Sprintf("%d-%02d-%02d", t.Year(), t.Month(), t.Day())       // 2020-11-24
	receiptTime := fmt.Sprintf("%02d:%02d:%02d", t.Hour(), t.Minute(), t.Second()) // 11:40:00
	znumDate := fmt.Sprintf("%d%02d%02d", t.Year(), t.Month(), t.Day())            // 20190625

	// Verification URL
	// https://virtual.tra.go.tz/efdmsRctVerify/31BF7E960_114000
	verificationURL := "https://virtual.tra.go.tz/efdmsRctVerify/"
	verificationURL += RCTVNUM
	verificationURL += "_" + strings.ReplaceAll(receiptTime, ":", "")

	grandTotal := fmt.Sprintf("%f", invoice.GrandTotal)
	vatA := fmt.Sprintf("%f", invoice.Vat)
	grossAmt := fmt.Sprintf("%f", invoice.GrossAmount)

	// Customer ID types;
	// 1. TIN
	// 2. NIDA
	// 3. PASSPORT
	// 4. DRIVING LIVENCE
	// 5. VORTERS ID
	// 6. NON OF THE ABOVE

	customerIDType, err := s.customerIDTypeRepository.GetDefaultCustomerIDType(ctx)
	if err != nil && utils.IsErrNoRows(err) {
		fmt.Printf("unable to retrieve default customer id type, err=[%v]\n", err)
	}

	fmt.Printf("Default Customer ID Type=[%v]\n", customerIDType.CustomerIDType)

	if customerIDType.CustomerIDType != invoice.CustomerIDTypeStr.String() {
		invoice.CustomerIDTypeValue = ""
	}

	if len(invoice.CustomerIDTypeValue) > 0 {

		customerIDType.CustomerID = invoice.CustomerIDTypeValue

		switch customerIDType.CustomerIDType {
		case "tin":
			customerIDType.CustomerIDType = "1"
		case "nida":
			customerIDType.CustomerIDType = "2"
		case "passport":
			customerIDType.CustomerIDType = "3"
		case "driving_license":
			customerIDType.CustomerIDType = "4"
		case "voters_id":
			customerIDType.CustomerIDType = "5"
		case "none_of_the_above":
			customerIDType.CustomerIDType = "6"
		}
	} else {
		// Set TIN as default customer ID type
		if len(invoice.CustomerTIN) > 0 {
			customerIDType.CustomerIDType = "1"
			customerIDType.CustomerID = invoice.CustomerTIN
		}
	}

	fmt.Printf("customerIDType.CustomerIDType=[%v]\n", customerIDType.CustomerIDType)
	fmt.Printf("customerIDType.CustomerID=[%v]\n", customerIDType.CustomerID)
	fmt.Printf("----------------------------------------------------------------\n")

	itemsStringData := ""

	for i := 0; i < len(invoice.TRAVFDItems); i++ {
		item := invoice.TRAVFDItems[i]
		taxCode := fmt.Sprintf("%v", item.TaxCode)

		itemQty := strings.ReplaceAll(item.Quantity, ",", "")
		itemTotalAmt := strings.ReplaceAll(item.Total, ",", "")

		// "<ITEM><ID>1</ID><DESC>Fiscal devices and services</DESC><QTY>1</QTY><TAXCODE>1</TAXCODE><AMT>20000.01</AMT></ITEM>"
		itemStringData := fmt.Sprintf(
			"<ITEM><ID>%v</ID><DESC>%v</DESC><QTY>%v</QTY><TAXCODE>%v</TAXCODE><AMT>%v</AMT></ITEM>",
			i+1,
			item.Description,
			itemQty,
			taxCode,
			itemTotalAmt,
		)

		fmt.Printf("item=[%v]\n", itemStringData)
		itemsStringData += itemStringData
	}

	fmt.Printf("----------------------------------------------------------------\n")

	rctData := "<RCT>" +
		"<DATE>" + receiptDate + "</DATE>" +
		"<TIME>" + receiptTime + "</TIME>" +
		"<TIN>" + os.Getenv("TIN_NUMBER") + "</TIN>" +
		"<REGID>" + companyRegistration.RegID + "</REGID>" +
		"<EFDSERIAL>" + os.Getenv("TRA_CERT_KEY") + "</EFDSERIAL>" +
		"<CUSTIDTYPE>" + customerIDType.CustomerIDType + "</CUSTIDTYPE>" +
		"<CUSTID>" + customerIDType.CustomerID + "</CUSTID>" +
		"<CUSTNAME>" + invoice.CustomerName + "</CUSTNAME>" +
		"<MOBILENUM>0713655545</MOBILENUM>" +
		"<RCTNUM>" + RCTNUM + "</RCTNUM>" +
		"<DC>" + DC + "</DC>" +
		"<GC>" + GC + "</GC>" +
		"<ZNUM>" + znumDate + "</ZNUM>" +
		"<RCTVNUM>" + RCTVNUM + "</RCTVNUM>" +
		"<ITEMS>" +
		itemsStringData +
		"</ITEMS>" +
		"<TOTALS>" +
		"<TOTALTAXEXCL>" + grossAmt + "</TOTALTAXEXCL><TOTALTAXINCL>" + grandTotal + "</TOTALTAXINCL><DISCOUNT>0.00</DISCOUNT>" +
		"</TOTALS>" +
		"<PAYMENTS>" +
		"<PMTTYPE>CASH</PMTTYPE><PMTAMOUNT>" + grandTotal + "</PMTAMOUNT>" +
		"<PMTTYPE>CHEQUE</PMTTYPE><PMTAMOUNT>0.00</PMTAMOUNT>" +
		"<PMTTYPE>CCARD</PMTTYPE><PMTAMOUNT>0.00</PMTAMOUNT>" +
		"<PMTTYPE>EMONEY</PMTTYPE><PMTAMOUNT>0.00</PMTAMOUNT>" +
		"</PAYMENTS>" +
		"<VATTOTALS>" +
		"<VATRATE>A</VATRATE><NETTAMOUNT>100000.00</NETTAMOUNT><TAXAMOUNT>" + vatA + "</TAXAMOUNT>" +
		"<VATRATE>B</VATRATE><NETTAMOUNT>100000.00</NETTAMOUNT><TAXAMOUNT>0.00</TAXAMOUNT>" +
		"<VATRATE>C</VATRATE><NETTAMOUNT>100000.00</NETTAMOUNT><TAXAMOUNT>0.00</TAXAMOUNT>" +
		"</VATTOTALS>" +
		"</RCT>"

	fmt.Printf("%v\n", rctData)

	signedReceiptData := signData(rctData)
	// fmt.Println("================================ signedReceiptData ================================")
	// fmt.Println(signedReceiptData)
	// fmt.Println("================================ signedReceiptData ================================")

	payload := strings.NewReader("<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
		"<EFDMS>" + rctData + "<EFDMSSIGNATURE>" + signedReceiptData + "</EFDMSSIGNATURE>" + "</EFDMS>")

	client := &http.Client{}
	req, err := http.NewRequest("POST", rctInfoURL, payload)
	if err != nil {
		return err
	}

	certSerial := base64.StdEncoding.EncodeToString([]byte(os.Getenv("TRA_CERT_SERIAL")))
	// fmt.Printf("certSerial=[%v]\n", certSerial)

	req.Header.Add("Authorization", "Bearer "+token.AccessToken)
	req.Header.Add("Cert-Serial", certSerial)
	req.Header.Add("Client", "WEBAPI")
	req.Header.Add("Content-Type", "application/xml")
	req.Header.Add("Routing-Key", companyRegistration.RoutingKey)

	res, err := client.Do(req)
	if err != nil {
		return err
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return err
	}

	rawXmlData := string(body)
	receiptInfoResponse := &models.ReceiptInfoResponse{}
	xml.Unmarshal([]byte(rawXmlData), receiptInfoResponse)

	// fmt.Println("================================ vfdRegResponse ================================")
	// fmt.Println(rawXmlData)
	// fmt.Println("================================ vfdRegResponse ================================")
	// fmt.Println("================================ receiptInfoResponse ================================")
	// fmt.Println(receiptInfoResponse)
	// fmt.Println("================================ receiptInfoResponse ================================")

	ackMsg := strings.ToLower(receiptInfoResponse.RCTACK.ACKMSG)
	fmt.Printf("receiptInfoResponse.RCTACK.ACKMSG=[%v]\n", ackMsg)
	// fmt.Printf("receiptInfoResponse.EFDMSSIGNATURE=[%v]\n", receiptInfoResponse.EFDMSSIGNATURE)

	invoice.Signature = receiptInfoResponse.EFDMSSIGNATURE

	// https://verify.tra.go.tz/Home/Index
	// https://virtual.tra.go.tz/efdmsRctVerify/31BF7E960_114000
	invoice.VerificationURL = verificationURL
	fmt.Printf("VerificationURL=[%v]\n", verificationURL)
	// Receipt verification code/serial number/date and time/Total amount
	signature := fmt.Sprintf("%v/%v/%v/%v", RCTVNUM, receiptDate, receiptTime, invoice.GrandTotal)
	fmt.Printf("signature=[%v]\n", signature)
	invoice.Signature = signature

	if ackMsg == "success" {
		// Save updated GC after success
		companyRegistration.GC = fmt.Sprintf("%v", newGC)
		err = s.companyRegistrationRepository.Save(ctx, companyRegistration)
		if err != nil {
			fmt.Printf("error updating company reg details, err=[%v]\n", err)
		}
	}

	return nil
}

func generateZReportPDF(
	companyRegistration *entities.CompanyRegistration,
	zReport *entities.ZReport,
	data []string,
) error {

	pdf := gofpdf.New("P", "mm", "A4", "")
	pdf.AddPage()
	pdf.SetFont("Arial", "", 8)

	pdf.SetXY(0, 1)
	str := "*** START OF LEGAL RECEIPT ***\n"
	str += companyRegistration.Name + "\n"
	str += companyRegistration.City + "\n"
	str += "TIN: " + companyRegistration.TIN + "\n"
	str += "VRN: " + companyRegistration.VRN + "\n"
	str += "UIN: " + companyRegistration.UIN + "\n"
	str += "TAX OFFICE: " + companyRegistration.TaxOffice + "\n\n"

	pdf.MultiCell(90, 5, str, "", "C", false)

	str = "DATE: 22/04/2022            TIME: 16:14:20"
	pdf.MultiCell(90, 5, str, "", "L", false)
	pdf.Line(0, 42, 90-0, 42)

	pdf.SetFont("Arial", "B", 8)
	str = "DAILY Z REPORT"
	pdf.MultiCell(60, 5, str, "", "C", false)
	pdf.Line(0, 45, 90-0, 45)

	dateFormat := "2006-01-02 15:04:05"
	str = fmt.Sprintf("CURRENT Z:           %v\n", zReport.ID)
	str += fmt.Sprintf("PREVIOUS Z:           %v\n", zReport.CreatedAt.Format(dateFormat))
	pdf.MultiCell(120, 5, str, "", "L", false)
	pdf.Line(0, 60, 90-0, 60)

	pdf.SetFont("Arial", "", 7)
	str = "\nDAILY TOTAL AMOUNT:        0.00\n"
	str += "CORRECTIONS:              0.00\n"
	str += "DISCOUNTS:                0.00\n"
	str += "SURCHAGES:                0.00\n"
	str += "VOIDS:                0.00\n"
	str += "VOIDS TOTAL:                0.00\n"
	str += "FIRST RECEIPT:                0.00\n"
	str += "LAST RECEIPT:                0.00\n"
	str += "RECEIPTS ISSUED:                0.00\n"
	pdf.MultiCell(120, 5, str, "", "L", false)
	pdf.Line(0, 85, 90-0, 85)

	pdf.SetFont("Arial", "B", 8)
	str = "PAYMENTS REPORT"
	pdf.MultiCell(60, 5, str, "", "C", false)
	pdf.Line(0, 90, 90-0, 90)

	linePos := 11.0
	for i := 0; i < len(data); i++ {
		linePos++
		pdf.Cell(1, linePos, data[i])
	}

	fileName := fmt.Sprintf("C://ESD_Documents//Z_Reports//%v.pdf", zReport.ID)
	fmt.Printf("Saving zeport file=[%v]\n", fileName)
	return pdf.OutputFileAndClose(fileName)
}

func registerCompany(
	ctx context.Context,
	companyRegistrationRepository repos.CompanyRegistrationRepository,
	companyRegistration *entities.CompanyRegistration,
) (*models.RegistrationResponse, error) {

	registrationResponse := &models.RegistrationResponse{}

	toSign := "<REGDATA><TIN>" + os.Getenv("TIN_NUMBER") + "</TIN><CERTKEY>" + os.Getenv("TRA_CERT_KEY") + "</CERTKEY></REGDATA>"

	// fmt.Println("================================ toSign ================================")
	// fmt.Println(toSign)
	// fmt.Println("================================ toSign ================================")

	regDataSignedData := signData(toSign)
	// fmt.Println("================================ regDataSignedData ================================")
	// fmt.Println(regDataSignedData)
	// fmt.Println("================================ regDataSignedData ================================")

	url := "https://virtual.tra.go.tz/efdmsRctApi/api/vfdRegReq"
	method := "POST"

	payload := strings.NewReader("<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
		"<EFDMS>" +
		"<REGDATA>" +
		"<TIN>" + os.Getenv("TIN_NUMBER") + "</TIN>" +
		"<CERTKEY>" + os.Getenv("TRA_CERT_KEY") + "</CERTKEY>" +
		"</REGDATA>" +
		"<EFDMSSIGNATURE>" + regDataSignedData + "</EFDMSSIGNATURE>" +
		"</EFDMS>")

	// fmt.Println("================================ payload ================================")
	// fmt.Println(payload)
	// fmt.Println("================================ payload ================================")

	client := &http.Client{}
	req, err := http.NewRequest(method, url, payload)
	if err != nil {
		return registrationResponse, err
	}

	// certSerialStr := utils.GetCertSerial(os.Getenv("TRA_CERT_FILE"))
	// fmt.Printf("certSerialStr=[%v] \n", certSerialStr)
	// fmt.Printf("certSerialConfig=[%v] \n", os.Getenv("TRA_CERT_SERIAL"))

	certSerial := base64.StdEncoding.EncodeToString([]byte(os.Getenv("TRA_CERT_SERIAL")))

	req.Header.Add("Content-Type", "application/xml")
	req.Header.Add("Cert-Serial", certSerial)
	req.Header.Add("Client", "WEBAPI")

	res, err := client.Do(req)
	if err != nil {
		return registrationResponse, err
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return registrationResponse, err
	}

	rawXmlData := string(body)
	xml.Unmarshal([]byte(rawXmlData), registrationResponse)

	// fmt.Println("================================ vfdRegResponse ================================")
	// fmt.Println(rawXmlData)
	// fmt.Println("================================ vfdRegResponse ================================")
	// fmt.Println("================================ registrationResponse ================================")
	// fmt.Println(registrationResponse)
	// fmt.Println("================================ registrationResponse ================================")

	// Update registration details every time registerCompany call is invoked
	companyRegistration.RegID = registrationResponse.EFDMS.REGID
	companyRegistration.Serial = registrationResponse.EFDMS.SERIAL
	companyRegistration.UIN = registrationResponse.EFDMS.UIN
	companyRegistration.TIN = os.Getenv("TIN_NUMBER")
	companyRegistration.VRN = registrationResponse.EFDMS.VRN
	companyRegistration.Mobile = registrationResponse.EFDMS.MOBILE
	companyRegistration.Street = registrationResponse.EFDMS.STREET
	companyRegistration.City = registrationResponse.EFDMS.CITY
	companyRegistration.Country = registrationResponse.EFDMS.COUNTRY
	companyRegistration.Name = registrationResponse.EFDMS.NAME
	companyRegistration.ReceiptCode = registrationResponse.EFDMS.RECEIPTCODE
	companyRegistration.Region = registrationResponse.EFDMS.REGION
	companyRegistration.RoutingKey = registrationResponse.EFDMS.ROUTINGKEY
	companyRegistration.GC = registrationResponse.EFDMS.GC
	companyRegistration.TaxOffice = registrationResponse.EFDMS.TAXOFFICE
	companyRegistration.Username = registrationResponse.EFDMS.USERNAME
	companyRegistration.Password = registrationResponse.EFDMS.PASSWORD
	companyRegistration.TokenPath = registrationResponse.EFDMS.TOKENPATH
	companyRegistration.EFDMSSignature = registrationResponse.EFDMS.EFDMSSIGNATURE

	err = companyRegistrationRepository.Save(ctx, companyRegistration)
	if err != nil {
		fmt.Printf("error updating company reg details, err=[%v]\n", err)
	}

	return registrationResponse, nil
}

func sendTokenAuthenticationRequest(
	companyRegistration *entities.CompanyRegistration,
) (*models.Token, error) {

	tokenDetails := &models.Token{}

	vfdTokenURL := "https://virtual.tra.go.tz/efdmsRctApi/vfdtoken"

	data := url.Values{}
	data.Set("username", companyRegistration.Username)
	data.Set("password", companyRegistration.Password)
	data.Set("grant_type", "password")

	req, err := http.NewRequest("POST", vfdTokenURL, strings.NewReader(data.Encode()))
	if err != nil {
		fmt.Println(err)
		return tokenDetails, err
	}
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return tokenDetails, err
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return tokenDetails, err
	}

	rawJsonData := string(body)
	// fmt.Println("================================ rawJsonData ================================")
	// fmt.Println(rawJsonData)
	// fmt.Println("================================ rawJsonData ================================")

	json.Unmarshal([]byte(rawJsonData), &tokenDetails)
	// fmt.Printf("tokenDetails.AccessToken: %s\n", tokenDetails.AccessToken)

	return tokenDetails, nil
}

func signData(toSign string) string {

	b, err := ioutil.ReadFile(os.Getenv("TRA_CERT_FILE"))
	if err != nil {
		fmt.Println(err)

	}

	privk, _, err := pkcs12.Decode(b, os.Getenv("TRA_CERT_PASSWORD"))
	if err != nil {
		fmt.Println(err)
	}

	pv := privk.(*rsa.PrivateKey)
	sign, _ := sign(pv, toSign)
	return sign
}

func sign(privateKey *rsa.PrivateKey, data string) (string, error) {

	h := crypto.SHA1.New()
	h.Write([]byte(data))
	hashed := h.Sum(nil)

	sign, err := rsa.SignPKCS1v15(rand.Reader, privateKey, crypto.SHA1, hashed)
	if err != nil {
		return "", err
	}

	return base64.StdEncoding.EncodeToString(sign), err
}

func formatStrFigure(amtStr string, desiredLen int) string {
	amtStr = strings.ReplaceAll(amtStr, " ", "")
	amtStr = strings.ReplaceAll(amtStr, ",", "")
	strLen := len(amtStr)
	spaces := ""

	requiredSpaces := desiredLen - strLen
	if requiredSpaces == 3 {
		requiredSpaces = 2
	} else if requiredSpaces == 5 {
		spaces = " "
	} else if requiredSpaces >= 7 {
		spaces = "   "
	}

	// fmt.Printf("amtStr=[%v], strLen=[%v], desiredLen=[%v], requiredSpaces=[%v]\n", amtStr, strLen, desiredLen, requiredSpaces)

	for i := 0; i < requiredSpaces; i++ {
		spaces += " "
	}

	return spaces + amtStr
}
