package entities

import (
	"database/sql/driver"
)

type PaymentMethod string

const (
	PaymentMethodCard  PaymentMethod = "card"
	PaymentMethodCash  PaymentMethod = "cash"
	PaymentMethodMPesa PaymentMethod = "mpesa"
)

func (s PaymentMethod) IsValid() bool {
	return s == PaymentMethodCard || s == PaymentMethodCash || s == PaymentMethodMPesa
}

// Scan implements the Scanner interface.
func (s *PaymentMethod) Scan(value interface{}) error {
	*s = PaymentMethod(string(value.([]uint8)))
	return nil
}

// Value implements the driver Valuer interface.
func (s PaymentMethod) Value() (driver.Value, error) {
	return s.String(), nil
}

func (s PaymentMethod) String() string {
	return string(s)
}
