package repos

import (
	"context"
	"fmt"
	db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/utils"
)

const (
	countSessionsSQL = `SELECT COUNT(*) FROM sessions`

	createSessionSQL = `INSERT INTO sessions (user_id, application_id, ip_address, user_agent, last_refreshed_at, 
			created_at, updated_at) VALUES  ($1, $2, $3, $4, $5, $6, $7) RETURNING id`

	findFullSessionSQL = `SELECT sess.id, sess.application_id, p.application_type, sess.deactivated_at, sess.ip_address, 
		sess.last_refreshed_at, sess.user_agent, sess.user_id, sess.created_at, sess.updated_at 
		FROM sessions sess
		JOIN applications p ON sess.application_id = p.id
		JOIN users u ON sess.user_id = u.id`

	findUserSessionByIDSQL = findFullSessionSQL + ` WHERE p.application_type = $1 AND sess.id = $2`

	selectSessionsSQL = `SELECT id, user_id, application_id, ip_address, user_agent, last_refreshed_at, created_at, 
			updated_at FROM sessions`

	selectSessionByIDSQL = selectSessionsSQL + ` WHERE id = $1`

	updateSessionSQL = `UPDATE sessions SET last_refreshed_at = $1, updated_at = $2 WHERE id = $3`

	deactivateSessionSQL = `UPDATE sessions SET deactivated_at = $1, updated_at = $2 WHERE id = $3`
)

type (
	SessionRepository interface {
		CountSessions(ctx context.Context, operations db.TransactionsSQLOperations, filter *entities.PaginationFilter) (int, error)
		DeactivateSession(ctx context.Context, operations db.TransactionsSQLOperations, newSession *entities.Session) error
		FilterSessions(ctx context.Context, operations db.TransactionsSQLOperations, filter *entities.PaginationFilter) ([]*entities.Session, error)
		FindByID(ctx context.Context, operations db.TransactionsSQLOperations, sessionID int64) (*entities.Session, error)
		FindFullSession(ctx context.Context, operations db.TransactionsSQLOperations, applicationType entities.ApplicationType, sessionID int64) (*entities.FullSession, error)
		Save(ctx context.Context, operations db.TransactionsSQLOperations, newSession *entities.Session) error
	}

	AppSessionRepository struct {
	}
)

func NewSessionRepository() SessionRepository {
	return &AppSessionRepository{}
}

func (r *AppSessionRepository) CountSessions(
	ctx context.Context,
	operations db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) (int, error) {

	var count int
	args := []interface{}{}

	err := operations.QueryRowContext(ctx, countSessionsSQL, args...).Scan(&count)
	if err != nil {
		return count, utils.NewDatabaseError(
			err,
			"Failed to query sessions count with filter=[%v]",
			filter,
		)
	}

	return count, nil
}

func (s *AppSessionRepository) DeactivateSession(
	ctx context.Context,
	operations db.TransactionsSQLOperations,
	session *entities.Session,
) error {

	session.Timestamps.Touch()

	_, err := operations.ExecContext(
		ctx,
		deactivateSessionSQL,
		session.DeactivatedAt,
		session.UpdatedAt,
		session.ID,
	)

	return err
}

func (r *AppSessionRepository) FilterSessions(
	ctx context.Context,
	operations db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) ([]*entities.Session, error) {

	sessions := make([]*entities.Session, 0)
	query := selectSessionsSQL
	currentIndex := 1
	args := []interface{}{}

	query += fmt.Sprintf(" ORDER BY id DESC OFFSET $%d LIMIT $%d", currentIndex, currentIndex+1)
	args = append(args, filter.Offset, filter.Limit)

	rows, err := operations.QueryContext(ctx, query, args...)
	if err != nil {
		return sessions, err
	}
	defer rows.Close()

	for rows.Next() {

		member, err := r.scanRowIntoSession(rows)
		if err != nil {
			return sessions, err
		}

		sessions = append(sessions, member)
	}

	return sessions, nil
}

func (r *AppSessionRepository) FindByID(
	ctx context.Context,
	operations db.TransactionsSQLOperations,
	sessionID int64,
) (*entities.Session, error) {

	row := operations.QueryRowContext(ctx, selectSessionByIDSQL, sessionID)
	return r.scanRowIntoSession(row)
}

func (r *AppSessionRepository) FindFullSession(
	ctx context.Context,
	operations db.TransactionsSQLOperations,
	applicationType entities.ApplicationType,
	sessionID int64,
) (*entities.FullSession, error) {

	row := operations.QueryRowContext(ctx, findUserSessionByIDSQL, applicationType, sessionID)
	return r.scanRowIntoFullSession(row)
}

func (s *AppSessionRepository) Save(
	ctx context.Context,
	operations db.TransactionsSQLOperations,
	session *entities.Session,
) error {

	session.Timestamps.Touch()

	if session.IsNew() {

		res := operations.QueryRowContext(
			ctx,
			createSessionSQL,
			session.UserID,
			session.ApplicationID,
			session.IPAddress,
			session.UserAgent,
			session.LastRefreshedAt,
			session.CreatedAt,
			session.UpdatedAt,
		)

		err := res.Scan(&session.ID)
		return err

	} else {

		_, err := operations.ExecContext(
			ctx,
			updateSessionSQL,
			session.LastRefreshedAt,
			session.UpdatedAt,
			session.ID,
		)

		return err
	}
}

func (r *AppSessionRepository) scanRowIntoFullSession(
	rowScanner db.RowScanner,
) (*entities.FullSession, error) {

	var fullSession entities.FullSession

	err := rowScanner.Scan(
		&fullSession.ID,
		&fullSession.ApplicationID,
		&fullSession.ApplicationType,
		&fullSession.DeactivatedAt,
		&fullSession.IPAddress,
		&fullSession.LastRefreshedAt,
		&fullSession.UserAgent,
		&fullSession.UserID,
		&fullSession.Timestamps.CreatedAt,
		&fullSession.Timestamps.UpdatedAt,
	)
	if err != nil {
		return &fullSession, utils.NewDatabaseError(
			err,
			"fullSession row scan",
		)
	}

	return &fullSession, nil
}

func (r *AppSessionRepository) scanRowIntoSession(
	rowScanner db.RowScanner,
) (*entities.Session, error) {

	var session entities.Session

	err := rowScanner.Scan(
		&session.ID,
		&session.UserID,
		&session.ApplicationID,
		&session.IPAddress,
		&session.UserAgent,
		&session.LastRefreshedAt,
		&session.CreatedAt,
		&session.UpdatedAt,
	)

	return &session, err
}
