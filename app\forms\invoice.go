package forms

type (
	InvoiceForm struct {
		CustomerName     string      `binding:"required" json:"customer_name"`
		CustomerTIN      string      `binding:"required" json:"customer_tin"`
		CustomerVRN      string      `json:"customer_vrn"`
		DC               int         `json:"dc"`
		FileName         string      `json:"file_name"`
		GC               int         `json:"gc"`
		GrandTotal       float64     `binding:"required" json:"grand_total"`
		GrossAmount      float64     `binding:"required" json:"gross_amount"`
		InvoiceNumber    string      `binding:"required" json:"invoice_number"`
		Items            []*ItemForm `json:"items"`
		OriginalFileName string      `json:"original_file_name"`
		ReceiptCode      string      `json:"receipt_code"`
		Rctvnum          string      `json:"rctvnum"`
		Signature        string      `json:"signature"`
		Vat              float64     `binding:"required" json:"vat"`
		VerificationURL  string      `json:"verification_url"`
	}

	ItemForm struct {
		ID          int64   `json:"id"`
		AmtUSD      string  `json:"amt_usd"`
		GoodsName   string  `json:"goods_name"`
		HSCode      string  `json:"hs_code"`
		Description string  `binding:"required" json:"description"`
		ItemType    string  `json:"item_type"`
		NetPrice    string  `json:"net_price"`
		Quantity    float64 `binding:"required" json:"quantity"`
		TaxCode     int     `binding:"required" json:"tax_code"`
		Total       float64 `binding:"required" json:"total"`
		UnitPrice   string  `json:"unit_price"`
		VatRate     string  `json:"vat_rate"`
	}
)

// # Tax Codes  Description
// 1 - A       18% Tax
// 2 - B-10    Special Rate B
// 3 - C-0     Zero Rated
// 4 - D-SR    Special Rate D-SR
// 5 - E-EX    Exempt E-Ex
