package handler

import (
	"encoding/json"
	"log"
	"math"
	"compliance-and-risk-management-backend/app/models"
	"compliance-and-risk-management-backend/app/utils"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gorilla/mux"
	"github.com/jinzhu/gorm"
)

// passphrase for passwords encryption
var passphrase = "USUCitKwDMONwYuZMRyEwOMuzXNSrGpuzvdvUrgW"

// Authenticate - authenticate user
func Authenticate(db *gorm.DB, w http.ResponseWriter, r *http.Request) {

	response := models.JSONResponse{}

	w.Header().Set("Content-Type", "application/json")
	basicAuth := r.Header.Get("Authorization")
	s := strings.Split(basicAuth, " ")
	value := s[1]
	details := utils.Base64DecodeString(value)

	userDetails := strings.Split(details, ":")
	email, password := userDetails[0], userDetails[1]

	validateUser := models.User{}
	_ = json.NewDecoder(r.Body).Decode(&validateUser)

	userValidation := models.User{}
	db.Where("email = ? and password = ?", email, password).Find(&userValidation).First(&userValidation)

	if userValidation.ID > 0 {

		activity := models.Activity{}
		activity.UserID = userValidation.ID
		activity.ActivityType = "USER_LOGIN"
		activity.Description = "Login Successful."

		// decrypted, err := utils.DecryptString(userValidation.Password, passphrase)
		// if err != nil {
		// 	log.Println(err)
		// }
		// if password == decrypted {
		// 	activity.Description = "Login Successful."
		// 	utils.CreateTokenEndpointCustom(w, userValidation)
		// } else {
		// 	activity.Description = "Login Failed!"
		// 	w.WriteHeader(http.StatusUnauthorized)
		// }

		// utils.CreateTokenEndpointCustom(w, userValidation)
		CreateActivity(db, activity)

	} else {
		response.Status = "01"
		response.Description = "Invalid Credentials!"
		w.WriteHeader(http.StatusUnauthorized)
	}

}

func GetAllUsers(db *gorm.DB, w http.ResponseWriter, r *http.Request) {

	apiResponse := models.UsersRepsApiReport{}
	reportsFilter := models.ReportsFilter{}
	usersCount := CountUsers(db, reportsFilter)
	apiResponse.UsersCount = usersCount

	vars := mux.Vars(r)
	limit := utils.ConvertStringToInt64(vars["limit"])
	offset := utils.ConvertStringToInt64(vars["offset"])
	search := vars["search"]

	apiResponse.Limit = limit
	apiResponse.Offset = offset
	apiResponse.Search = search

	noPagesOne := float64(usersCount) / float64(limit)
	noPages := math.Ceil(float64(noPagesOne))
	apiResponse.NumPages = int64(noPages)

	users := []models.User{}

	if search == "query" {
		db.Order("Id DESC").Offset(offset).Limit(limit).Find(&users)
	} else {
		apiResponse.NumPages = 1
		s := "%" + search + "%"
		db.Order("Id DESC").Where(`email LIKE ? OR firstname LIKE ? OR lastname LIKE ? OR msisdn LIKE ? OR name LIKE ?  `, s, s, s, s, s).Limit(limit).Find(&users)
	}

	apiResponse.Users = users

	respondJSON(w, http.StatusOK, apiResponse)
}

func FilterUsers(db *gorm.DB, w http.ResponseWriter, r *http.Request) {

	apiResponse := models.UsersRepsApiReport{}

	vars := mux.Vars(r)
	startDate := utils.FormatReportDateStringTimestamp(vars["startdate"])
	endDate := utils.FormatReportDateStringTimestamp(vars["enddate"])

	reportsFilter := models.ReportsFilter{}
	reportsFilter.StartDate = startDate
	reportsFilter.EndDate = endDate

	users := []models.User{}
	db.Order("Id DESC").Where("date_created BETWEEN ? and ? ", startDate, endDate).Find(&users)
	apiResponse.UsersCount = CountUsers(db, reportsFilter)
	apiResponse.Users = users
	respondJSON(w, http.StatusOK, apiResponse)
}

func CreateUser(db *gorm.DB, w http.ResponseWriter, r *http.Request) {

	user := models.User{}
	var last_user models.User
	db.Last(&last_user)
	user.ID = last_user.ID + 1
	user.Status = "ACTIVE"
	user.DateCreated = time.Now()

	decoder := json.NewDecoder(r.Body)
	if err := decoder.Decode(&user); err != nil {
		respondError(w, http.StatusBadRequest, err.Error())
		return
	}
	defer r.Body.Close()

	// encrypted, err := utils.EncryptString(user.Password, passphrase)
	// if err != nil {
	// 	log.Println(err)
	// } else {
	// 	user.Password = encrypted
	// }

	if err := db.Save(&user).Error; err != nil {
		respondError(w, http.StatusInternalServerError, err.Error())
		return
	}

	activity := models.Activity{}
	activity.UserID = user.ID
	activity.ActivityType = "USER_CREATION"
	activity.Description = "User created successfully."
	CreateActivity(db, activity)

	respondJSON(w, http.StatusCreated, user)
}

func GetUser(db *gorm.DB, w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	user_id := vars["id"]
	user := getUserOr404(db, user_id, w, r)
	if user == nil {
		return
	}
	respondJSON(w, http.StatusOK, user)
}

func UpdateUser(db *gorm.DB, w http.ResponseWriter, r *http.Request) {

	vars := mux.Vars(r)
	user_id := vars["id"]
	user := getUserOr404(db, user_id, w, r)
	if user == nil {
		return
	}

	updateUserDetails := models.User{}
	decoder := json.NewDecoder(r.Body)
	if err := decoder.Decode(&updateUserDetails); err != nil {
		respondError(w, http.StatusBadRequest, err.Error())
		return
	}
	defer r.Body.Close()

	user.Name = updateUserDetails.Name
	user.Firstname = updateUserDetails.Firstname
	user.Lastname = updateUserDetails.Lastname
	user.Phone = updateUserDetails.Phone
	user.Msisdn = updateUserDetails.Msisdn
	user.Email = updateUserDetails.Email
	user.Username = updateUserDetails.Username

	if updateUserDetails.Password != "" {
		// Encrypt the users pwd before saving to db
		encrypted, err := utils.EncryptString(updateUserDetails.Password, passphrase)
		if err != nil {
			log.Println(err)
		} else {
			user.Password = encrypted
		}
	}

	if err := db.Save(&user).Error; err != nil {
		respondError(w, http.StatusInternalServerError, err.Error())
		return
	}
	respondJSON(w, http.StatusOK, user)
}

func DeleteUser(db *gorm.DB, w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	user_id := vars["id"]
	user := getUserOr404(db, user_id, w, r)
	if user == nil {
		return
	}
	if err := db.Delete(&user).Error; err != nil {
		respondError(w, http.StatusInternalServerError, err.Error())
		return
	}
	respondJSON(w, http.StatusNoContent, nil)
}

func ArchiveUser(db *gorm.DB, w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)

	user_id := vars["id"]
	user := getUserOr404(db, user_id, w, r)
	if user == nil {
		return
	}
	if err := db.Save(&user).Error; err != nil {
		respondError(w, http.StatusInternalServerError, err.Error())
		return
	}
	respondJSON(w, http.StatusOK, user)
}

func RestoreUser(db *gorm.DB, w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	user_id := vars["id"]
	user := getUserOr404(db, user_id, w, r)
	if user == nil {
		return
	}
	if err := db.Save(&user).Error; err != nil {
		respondError(w, http.StatusInternalServerError, err.Error())
		return
	}
	respondJSON(w, http.StatusOK, user)
}

// GeneratePassword
func GeneratePassword(db *gorm.DB, w http.ResponseWriter, r *http.Request) {
	pwd := utils.GenerateRandomString(40)
	respondJSON(w, http.StatusOK, pwd)
}

func CountUsers(db *gorm.DB, reportsFilter models.ReportsFilter) int64 {

	var count int64

	if reportsFilter.StartDate.IsZero() && reportsFilter.EndDate.IsZero() {
		db.Table("users").Count(&count)
	} else {
		db.Table("users").Where("date_created BETWEEN ? and ? ",
			reportsFilter.StartDate, reportsFilter.EndDate).Count(&count)
	}

	return count
}

// GetUserDetails - GetUserDetails
func GetUserDetails(db *gorm.DB, userID int) *models.User {
	user := models.User{}
	if err := db.First(&user, models.User{ID: userID}).Error; err != nil {
		return &user
	}
	return &user
}

// getUserOr404 gets a user instance if exists, or respond the 404 error otherwise
func getUserOr404(db *gorm.DB, user_id string, w http.ResponseWriter, r *http.Request) *models.User {
	user := models.User{}
	n, err := strconv.Atoi(user_id)
	if err != nil {
		log.Println("Error converting user_id to int >>> ", err)
	}

	if err := db.First(&user, models.User{ID: n}).Error; err != nil {
		respondError(w, http.StatusNotFound, err.Error())
		return nil
	}
	return &user
}
