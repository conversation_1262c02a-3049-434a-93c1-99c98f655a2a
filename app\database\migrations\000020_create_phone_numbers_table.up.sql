-- +goose Up
CREATE TABLE phone_numbers (
  id           BIGSERIAL    NOT NULL PRIMARY KEY,
  country_code VA<PERSON>HAR(7)   NOT NULL,
  number       VARCHAR(15)  NOT NULL,
  created_at   TIMESTAMPTZ  NOT NULL DEFAULT clock_timestamp(),
  updated_at   TIMESTAMPTZ  NOT NULL DEFAULT clock_timestamp()
);

-- There should at most one record for a contact for a user 
CREATE UNIQUE INDEX phone_number_uniq_idx ON phone_numbers(country_code, number);
