package entities

import "time"

type (
	Invoice struct {
		SequentialIdentifier
		AmountPaidInUSD                 float64               `json:"amt_paid_in_usd"`
		CanSign                         bool                  `json:"can_sign"`
		CreditNoteNumber                string                `json:"credit_note_number"`
		CuNumber                        string                `json:"cu_number"`
		CurrentReceiptNumber            int64                 `json:"current_receipt_number"`
		CustomerIDTypeStr               CustomerIDTypeStr     `json:"customer_identifier_type"`
		CustomerIDTypeValue             string                `json:"customer_identifier_value"`
		CustomerName                    string                `json:"customer_name"`
		CustomerEmail                   string                `json:"customer_email"`
		CustomerNumber                  string                `json:"customer_number"`
		CustomerPhone                   string                `json:"customer_phone"`
		CustomerTIN                     string                `json:"customer_tin"`
		CustomerVRN                     string                `json:"customer_vrn"`
		DateCreated                     time.Time             `json:"date_created"`
		DocumentTypeCode                *string               `json:"document_type_code,omitempty"` // S - Sale, R - Credit Note
		DC                              int                   `json:"dc"`
		EtimsInternalData               *string               `json:"etims_internal_data,omitempty"`
		EtimsItems                      []*EtimsItem          `json:"etims_items,omitempty"`
		EtimsMrcNumber                  *string               `json:"etims_mrc_number,omitempty"`
		EtimsReceiptNumber              *int64                `json:"etims_receipt_number,omitempty"`
		EtimsReceiptSignature           *string               `json:"etims_receipt_signature,omitempty"`
		EtimsResultCode                 *string               `json:"etims_result_code,omitempty"`
		EtimsResultDateTime             *string               `json:"etims_result_date_time,omitempty"`
		EtimsResultMessage              *string               `json:"etims_result_message,omitempty"`
		EtimsSDCID                      *string               `json:"etims_sdcid,omitempty"`
		EtimsTotalReceiptNumber         *int64                `json:"etims_total_receipt_number,omitempty"`
		EtimsVSCUReceiptPublicationDate *string               `json:"etims_vscu_receipt_publication_date,omitempty"`
		ExchangeRate                    float64               `json:"exchange_rate"`
		FileName                        string                `json:"file_name"`
		GC                              int                   `json:"gc"`
		GrandTotal                      float64               `json:"grand_total"`
		GrossAmount                     float64               `json:"gross_amount"`
		GrossAmountStr                  string                `json:"gross_amount_str"`
		HasLevy                         bool                  `json:"has_levy"`
		HasVAT                          bool                  `json:"has_vat"`
		InvoiceDate                     string                `json:"invoice_date"`
		InvoiceItems                    []*InvoiceItem        `json:"invoice_items"`
		InvoiceNumber                   string                `json:"invoice_number"`
		InvoiceStrContent               string                `json:"invoice_str_content"`
		IsCreditNote                    bool                  `json:"is_credit_note"`
		IsEmailSent                     bool                  `json:"is_email_sent"`
		IsZReported                     bool                  `json:"is_z_reported"`
		Items                           []*Item               `json:"items"`
		ItemsString                     string                `json:"items_string"`
		ItemThatHasVAT                  string                `json:"item_that_has_vat"`
		NetAmount                       float64               `json:"net_amount"`
		OrganizationID                  int64                 `json:"organization_id"`
		OriginalFileName                string                `json:"original_file_name"`
		OriginalInvoiceNumber           string                `json:"original_invoice_number"`
		ReceiptCode                     string                `json:"receipt_code"`
		Rctvnum                         string                `json:"rctvnum"`
		Signature                       string                `json:"signature"`
		SignedDate                      string                `json:"signed_date"`
		SignaturePositioning            *SignaturePositioning `json:"-"`
		StoreID                         *int64                `json:"store_id"`
		TaxableAmount                   float64               `json:"taxable_amount"`
		TextData                        string                `json:"text_data"`
		TotalTaxFloat                   float64               `json:"total_tax_float"`
		TRAVFDItems                     []*TRAVFDItem         `json:"tra_vfd_items"`
		Vat                             float64               `json:"vat"`
		VatA                            float64               `json:"vat_a"` // Ex
		VatB                            float64               `json:"vat_b"` // 16%
		VatC                            float64               `json:"vat_c"` // 0%
		VatE                            float64               `json:"vat_e"` // 8%
		VatAmtStr                       string                `json:"vat_amt_str"`
		VerificationURL                 string                `json:"verification_url"`
		Timestamps
	}

	InvoiceList struct {
		Invoices   []*Invoice  `json:"invoices"`
		Pagination *Pagination `json:"pagination"`
	}

	// InvoicesRepsApiReport struct
	InvoicesRepsApiReport struct {
		Invoices      []Invoice `json:"invoices"`
		InvoicesCount int64     `json:"invoices_count"`
		Limit         int64     `json:"limit"`
		NumPages      int64     `json:"num_pages"`
		Offset        int64     `json:"offset"`
		Search        string    `json:"search"`
	}

	InvoiceSigningResult struct {
		ETIMSSDCID                      *string `json:"etims_sdcid"`
		ETIMSInternalData               *string `json:"etims_internal_data"`
		EtimsMrcNumber                  *string `json:"etims_mrc_number"`
		EtimsTotalReceiptNumber         *int64  `json:"etims_total_receipt_number"`
		EtimsVSCUReceiptPublicationDate *string `json:"etims_vscu_receipt_publication_date"`
		ETIMSReceiptSignature           *string `json:"etims_receipt_signature"`
		EtimsResultDateTime             *string `json:"etims_result_date_time"`
		InvoiceNumber                   string  `json:"invoice_number"`
		VerificationURL                 string  `json:"verification_url"`
	}

	// Result - db result
	Result struct {
		Total float64 `json:"total"`
	}

	// InvoiceReport struct
	InvoiceReport struct {
		GrandTotal    float64 `json:"grand_total"`
		GrossAmount   float64 `json:"gross_amount"`
		InvoiceDate   string  `json:"invoice_date"`
		InvoiceNumber string  `json:"invoice_number"`
		Signature     string  `json:"signature"`
		Vat           float64 `json:"vat"`
	}

	// SummaryReport struct
	SummaryReport struct {
		AllInvoicesCount              int64   `json:"all_invoices_count"`
		AllInvoicesTotalSum           float64 `json:"all_invoices_total_sum"`
		AllInvoicesVatSum             float64 `json:"all_invoices_vat_sum"`
		InvoicesThatFailedToSignCount int64   `json:"invoices_failed_to_sign_count"`
	}
)
