package services

import (
	"context"
	"errors"
	"fmt"
	"log"
	"compliance-and-risk-management-backend/app/apperr"
	txns_db "compliance-and-risk-management-backend/app/database"
	"compliance-and-risk-management-backend/app/entities"
	"compliance-and-risk-management-backend/app/forms"
	"compliance-and-risk-management-backend/app/repos"
	"compliance-and-risk-management-backend/app/utils"
	"compliance-and-risk-management-backend/app/web/ctxhelper"
)

type PhoneNumberHashService interface {
	CreatePhoneNumberHash(ctx context.Context, operations txns_db.TransactionsSQLOperations, form *forms.CreatePhoneNumberHashForm) (*entities.PhoneNumberHash, error)
	FilterPhoneNumberHashes(ctx context.Context, operations txns_db.TransactionsSQLOperations, filter *entities.PaginationFilter) (*entities.PhoneNumberHashList, error)
	FindByHash(ctx context.Context, transactionsDB txns_db.TransactionsDB, phoneNumberHash string) (*entities.PhoneNumberHashAPIResponse, error)
	FindByID(ctx context.Context, phoneNumberHashID int64) (*entities.PhoneNumberHash, error)
	FindByPhoneNumber(ctx context.Context, transactionsDB txns_db.TransactionsDB, phoneNumber string) (*entities.PhoneNumberHashAPIResponse, error)
	UpdatePhoneNumberHash(ctx context.Context, transactionsDB *txns_db.AppTransactionsDB, phoneNumberHashID int64, form *forms.UpdatePhoneNumberHashForm) (*entities.PhoneNumberHash, error)
	UploadMultiplePhoneNumberHashes(ctx context.Context, operations txns_db.TransactionsSQLOperations, form *forms.UploadMultiplePhoneNumberHashesForm) (*entities.PhoneNumberHashUploadAPIResponse, error)
}

type AppPhoneNumberHashService struct {
	hashRequestRepository     repos.HashRequestRepository
	phoneNumberHashRepository repos.PhoneNumberHashRepository
	transactionRepository     repos.TransactionRepository
	userCreditRepository      repos.UserCreditRepository
}

func NewPhoneNumberHashService(
	hashRequestRepository repos.HashRequestRepository,
	phoneNumberHashRepo repos.PhoneNumberHashRepository,
	transactionRepository repos.TransactionRepository,
	userCreditRepository repos.UserCreditRepository,
) PhoneNumberHashService {
	return &AppPhoneNumberHashService{
		hashRequestRepository:     hashRequestRepository,
		phoneNumberHashRepository: phoneNumberHashRepo,
		transactionRepository:     transactionRepository,
		userCreditRepository:      userCreditRepository,
	}
}

func (s *AppPhoneNumberHashService) CreatePhoneNumberHash(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	form *forms.CreatePhoneNumberHashForm,
) (*entities.PhoneNumberHash, error) {

	phoneNumberHash := &entities.PhoneNumberHash{
		PhoneNumber: form.PhoneNumber,
		Hash:        form.Hash,
		Provider:    form.Provider,
	}

	err := s.phoneNumberHashRepository.Save(ctx, phoneNumberHash)
	if err != nil {
		return phoneNumberHash, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to create phone number hash for phoneNumber=[%v], and hash=[%v]",
			form.PhoneNumber,
			form.Hash,
		)
	}

	return phoneNumberHash, nil
}

func (s *AppPhoneNumberHashService) FilterPhoneNumberHashes(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	filter *entities.PaginationFilter,
) (*entities.PhoneNumberHashList, error) {

	phoneNumberHashList := &entities.PhoneNumberHashList{}

	tokenInfo := ctxhelper.TokenInfo(ctx)

	count, err := s.phoneNumberHashRepository.CountPhoneNumberHashes(ctx, filter)
	if err != nil {
		fmt.Printf("unable to count phoneNumberHashes, err=[%v]\n", err.Error())
		return phoneNumberHashList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to count phone number hashs for user=[%v]",
			tokenInfo.UserID,
		)
	}

	phoneNumberHashes, err := s.phoneNumberHashRepository.FilterPhoneNumberHashes(ctx, operations, filter)
	if err != nil {
		fmt.Printf("unable to filter phoneNumberHashes, err=[%v]\n", err.Error())
		return phoneNumberHashList, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to filter phone number hashs for user=[%v]",
			tokenInfo.UserID,
		)
	}

	phoneNumberHashList.PhoneNumberHashes = phoneNumberHashes

	pagination := &entities.Pagination{
		Count: count,
		Page:  filter.Page,
		Per:   filter.Per,
	}

	phoneNumberHashList.Pagination = pagination

	return phoneNumberHashList, nil
}

func (s *AppPhoneNumberHashService) FindByHash(
	ctx context.Context,
	transactionsDB txns_db.TransactionsDB,
	hash string,
) (*entities.PhoneNumberHashAPIResponse, error) {

	phoneNumberHash := &entities.PhoneNumberHashAPIResponse{}
	tokenInfo := ctxhelper.TokenInfo(ctx)

	userCredits, err := s.checkUserCreditBalance(ctx, tokenInfo)
	if err != nil {
		return nil, err
	}

	log.Printf("tokenInfo.UserID=[%v], creditBalance=[%v]\n", tokenInfo.UserID, userCredits.CreditBalance)

	// Return phone number hash details
	phoneNumberHashResp, err := s.phoneNumberHashRepository.FindByHash(ctx, hash)
	if err != nil {

		if utils.IsErrNoRows(err) {
			return phoneNumberHash, utils.NewErrorWithErrorCodeAndMessage(
				errors.New("phoneNumberHash not found"),
				utils.ErrorCodeResourceNotFound,
				"phoneNumberHash not found",
				"phoneNumberHash=[%v] not found",
				hash,
			)
		}

		return phoneNumberHash, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to find phoneNumberHash by hash=[%v]",
			hash,
		)
	}

	trxCost := 0.5

	err = transactionsDB.InTransaction(ctx, func(ctx context.Context, operations txns_db.TransactionsSQLOperations) error {

		// Save transaction
		transaction := &entities.Transaction{
			UserID:          tokenInfo.UserID,
			Amount:          trxCost,
			Description:     "phone number request by hash",
			Status:          "success",
			TransactionType: "debit",
		}

		err = s.transactionRepository.Save(ctx, operations, transaction)
		if err != nil {
			return apperr.Wrap(
				err,
			).AddLogMessagef(
				"failed to find save hash request transaction by hash=[%v]",
				hash,
			)
		}

		// Create credit and debit entries, credit Growth Logic, debit customer
		// TODO: Create Struts Debit transaction

		// Save to hash requests table
		hashRequest := &entities.HashRequest{
			UserID:        tokenInfo.UserID,
			PhoneNumber:   phoneNumberHash.PhoneNumber,
			Hash:          phoneNumberHash.Hash,
			Status:        "success",
			Description:   "hash request processed successfully",
			TransactionID: transaction.ID,
		}

		err = s.hashRequestRepository.Save(ctx, operations, hashRequest)
		if err != nil {
			return apperr.Wrap(
				err,
			).AddLogMessagef(
				"failed to find save hash request by hash=[%v]",
				hash,
			)
		}

		// Charge account - Deduct KES 0.50 / 50 Cents.
		// Update user balance - deduct trx cost
		newBalance := userCredits.CreditBalance - trxCost
		userCredits.CreditBalance = newBalance
		err = s.userCreditRepository.Save(ctx, operations, userCredits)
		if err != nil {
			return apperr.Wrap(
				err,
			).AddLogMessagef(
				"failed to update user credit balance=[%v]",
				newBalance,
			)
		}

		phoneNumberHash.NewBalance = newBalance

		return err
	})

	phoneNumberHash.PhoneNumber = phoneNumberHashResp.PhoneNumber
	phoneNumberHash.Hash = phoneNumberHashResp.Hash
	phoneNumberHash.Provider = phoneNumberHashResp.Provider

	return phoneNumberHash, nil
}

func (s *AppPhoneNumberHashService) FindByID(
	ctx context.Context,
	keyID int64,
) (*entities.PhoneNumberHash, error) {

	phoneNumberHash := &entities.PhoneNumberHash{}
	var err error

	phoneNumberHash, err = s.phoneNumberHashRepository.FindByID(ctx, keyID)
	if err != nil {

		if utils.IsErrNoRows(err) {
			return phoneNumberHash, utils.NewErrorWithErrorCodeAndMessage(
				errors.New("phoneNumberHash not found"),
				utils.ErrorCodeResourceNotFound,
				"phoneNumberHash not found",
				"phoneNumberHash=[%v] not found",
				keyID,
			)
		}

		return phoneNumberHash, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to find phoneNumberHash by id=[%v]",
			keyID,
		)
	}

	return phoneNumberHash, nil
}

func (s *AppPhoneNumberHashService) FindByPhoneNumber(
	ctx context.Context,
	transactionsDB txns_db.TransactionsDB,
	phoneNumber string,
) (*entities.PhoneNumberHashAPIResponse, error) {

	phoneNumberHash := &entities.PhoneNumberHashAPIResponse{}
	var err error

	tokenInfo := ctxhelper.TokenInfo(ctx)

	userCredits, err := s.checkUserCreditBalance(ctx, tokenInfo)
	if err != nil {
		return nil, err
	}

	log.Printf("tokenInfo.UserID=[%v], creditBalance=[%v]\n", tokenInfo.UserID, userCredits.CreditBalance)

	phoneNumberHashResp, err := s.phoneNumberHashRepository.FindByPhoneNumber(ctx, phoneNumber)
	if err != nil {

		if utils.IsErrNoRows(err) {
			return phoneNumberHash, utils.NewErrorWithErrorCodeAndMessage(
				errors.New("phoneNumberHash not found"),
				utils.ErrorCodeResourceNotFound,
				"phoneNumberHash not found",
				"phoneNumberHash=[%v] not found",
				phoneNumber,
			)
		}

		return phoneNumberHash, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to find phoneNumberHash by phoneNumber=[%v]",
			phoneNumber,
		)
	}

	trxCost := 0.5

	err = transactionsDB.InTransaction(ctx, func(ctx context.Context, operations txns_db.TransactionsSQLOperations) error {

		// Save transaction
		transaction := &entities.Transaction{
			UserID:          tokenInfo.UserID,
			Amount:          trxCost,
			Description:     "phone number request by hash",
			Status:          "success",
			TransactionType: "debit",
		}

		err = s.transactionRepository.Save(ctx, operations, transaction)
		if err != nil {
			return apperr.Wrap(
				err,
			).AddLogMessagef(
				"failed to find save hash request transaction by phoneNumber=[%v]",
				phoneNumber,
			)
		}

		// Create credit and debit entries, credit Growth Logic, debit customer
		// TODO: Create Struts Debit transaction

		// Save to hash requests table
		hashRequest := &entities.HashRequest{
			UserID:        tokenInfo.UserID,
			PhoneNumber:   phoneNumberHash.PhoneNumber,
			Hash:          phoneNumberHash.Hash,
			Status:        "success",
			Description:   "hash request processed successfully",
			TransactionID: transaction.ID,
		}

		err = s.hashRequestRepository.Save(ctx, operations, hashRequest)
		if err != nil {
			return apperr.Wrap(
				err,
			).AddLogMessagef(
				"failed to find save hash request by phoneNumber=[%v]",
				phoneNumber,
			)
		}

		// Charge account - Deduct KES 0.50 / 50 Cents.
		// Update user balance - deduct trx cost
		newBalance := userCredits.CreditBalance - trxCost
		userCredits.CreditBalance = newBalance
		err = s.userCreditRepository.Save(ctx, operations, userCredits)
		if err != nil {
			return apperr.Wrap(
				err,
			).AddLogMessagef(
				"failed to update user credit balance=[%v]",
				newBalance,
			)
		}

		phoneNumberHash.NewBalance = newBalance

		return err
	})

	phoneNumberHash.PhoneNumber = phoneNumberHashResp.PhoneNumber
	phoneNumberHash.Hash = phoneNumberHashResp.Hash
	phoneNumberHash.Provider = phoneNumberHashResp.Provider

	return phoneNumberHash, nil
}

func (s *AppPhoneNumberHashService) UpdatePhoneNumberHash(
	ctx context.Context,
	transactionsDB *txns_db.AppTransactionsDB,
	phoneNumberHashID int64,
	form *forms.UpdatePhoneNumberHashForm,
) (*entities.PhoneNumberHash, error) {

	phoneNumberHash, err := s.phoneNumberHashRepository.FindByID(ctx, phoneNumberHashID)
	if err != nil {
		return phoneNumberHash, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to find phoneNumberHash by id=[%v]",
			phoneNumberHashID,
		)
	}

	phoneNumberHash.Hash = form.Hash

	err = s.phoneNumberHashRepository.Save(ctx, phoneNumberHash)
	if err != nil {
		return phoneNumberHash, apperr.Wrap(
			err,
		).AddLogMessagef(
			"Failed to update phoneNumberHash by id=[%v]",
			phoneNumberHashID,
		)
	}

	return phoneNumberHash, nil
}

func (s *AppPhoneNumberHashService) checkUserCreditBalance(
	ctx context.Context,
	tokenInfo *entities.TokenInfo,
) (*entities.UserCredit, error) {

	// Retrieve user credits
	userCredits, err := s.userCreditRepository.FindByUserID(ctx, tokenInfo.UserID)
	if err != nil {
		return nil, apperr.Wrap(
			err,
		).AddLogMessagef(
			"unable to find user credits by id=[%v]",
			tokenInfo.UserID,
		)
	}

	// Check balance
	if userCredits.CreditBalance < 0.5 {

		return userCredits, utils.NewErrorWithCode(
			errors.New("insufficient balance"),
			utils.ErrorCodeInsuficientBalance,
			"insufficient balance to perform transaction, user balance=[%v]",
			userCredits.CreditBalance,
		)
	}

	return userCredits, nil
}

func (s *AppPhoneNumberHashService) UploadMultiplePhoneNumberHashes(
	ctx context.Context,
	operations txns_db.TransactionsSQLOperations,
	form *forms.UploadMultiplePhoneNumberHashesForm,
) (*entities.PhoneNumberHashUploadAPIResponse, error) {

	phoneNumberHashes := make([]*entities.PhoneNumberHash, len(form.PhoneNumberHashes))

	for index, phoneNumberHash := range form.PhoneNumberHashes {
		phoneNumberHashes[index] = &entities.PhoneNumberHash{
			PhoneNumber: phoneNumberHash.PhoneNumber,
			Hash:        phoneNumberHash.Hash,
			Provider:    phoneNumberHash.Provider,
		}
	}

	err := s.phoneNumberHashRepository.SaveMultiple(ctx, operations, phoneNumberHashes)
	if err != nil {
		return nil, apperr.Wrap(
			err,
		).AddLogMessagef(
			"failed to save multiple phoneNumberHashes",
		)
	}

	response := &entities.PhoneNumberHashUploadAPIResponse{
		Count: len(phoneNumberHashes),
	}

	return response, nil
}
