package entities

type InvoiceItem struct {
	SequentialIdentifier
	Amount         float64 `json:"amount,omitempty"`
	AmtUSD         string  `json:"amt_usd,omitempty"`
	DiscountAmount float64 `json:"discount_amount"`
	DiscountRate   float64 `json:"discount_rate"`
	Description    string  `json:"description,omitempty"`
	GoodsName      string  `json:"goods_name,omitempty"`
	HSCode         string  `json:"hs_code,omitempty"`
	InvoiceID      int64   `json:"invoice_id"`
	ItemID         int     `json:"item_id"`
	ItemName       string  `json:"item_name,omitempty"`
	ItemType       string  `json:"item_type,omitempty"`
	Name           string  `json:"name"`
	NetPrice       string  `json:"net_price,omitempty"`
	Quantity       float64 `json:"quantity"`
	Rate           string  `json:"rate"`
	SerialNumber   string  `json:"serial_number"`
	Tax            string  `json:"tax,omitempty"`
	TaxCode        int     `json:"tax_code,omitempty"`
	Total          float64 `json:"total"`
	UnitPrice      string  `json:"unit_price,omitempty"`
	UUID           string  `json:"uuid"`
	Vat            float64 `json:"vat,omitempty"`
	VatRate        string  `json:"vat_rate,omitempty"`
	Timestamps
}

// # Tax Codes  Description
// 1 - A       18% Tax
// 2 - B-10    Special Rate B
// 3 - C-0     Zero Rated
// 4 - D-SR    Special Rate D-SR
// 5 - E-EX    Exempt E-Ex
