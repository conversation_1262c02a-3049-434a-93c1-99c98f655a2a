package entities

type (
	ChartSummary struct {
		RequestsCount         int `json:"requests_count"`
		Failed                int `json:"failed"`
		Month                 int `json:"month"`
		ProcessedSuccessfully int `json:"processed_successfully"`
		TotalRequests         int `json:"total_requests"`
	}

	DashboardAnalytics struct {
		AccountNumber string         `json:"account_number"`
		APIKeysCount  int            `json:"api_keys_count"`
		UserCredits   float64        `json:"user_credits"`
		ChartSummary  []ChartSummary `json:"chart_summary"`
	}
)
